import { get_department } from "@api/centre"
const state = {
    //部门数据
    departmentList:[]
}

const mutations = {
    SET_STATE(state, payload) {
        state[payload.key] = payload.value
    }
}

const actions = {
    //获取部门数据
    async get_departmentList({ commit }, payload) {
        return new Promise(resolve =>{
            get_department.then(res=>{
                console.log(res)
            })
        })
    },
    

}


export default {
    namespaced: true,
    state,
    mutations,
    actions,
}