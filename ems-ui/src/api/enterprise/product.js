import request from '@/utils/request'

// 查询产品列表
export function listProduct(query) {
  return request({
    url: '/energy/product/list',
    method: 'get',
    params: query
  })
}

// 查询产品详细
export function getProduct(proId) {
  return request({
    url: '/energy/product/' + proId,
    method: 'get'
  })
}

// 新增产品
export function addProduct(data) {
  return request({
    url: '/energy/product',
    method: 'post',
    data: data
  })
}

// 修改产品
export function updateProduct(data) {
  return request({
    url: '/energy/product',
    method: 'put',
    data: data
  })
}

// 删除产品
export function delProduct(proId) {
  console.log(proId)
  return request({
    url: '/energy/product/' + proId,
    method: 'delete'
  })
}
 //导出
 export function exportfile(query) {
  return request({
    url: "/energy/product/export",
    method: 'post',
    params: query
  })
}