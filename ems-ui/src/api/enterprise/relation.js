import request from '@/utils/request'
//获取表格数据
export function getRelationList(query){
    return request({
        url:"/energy/measure/pageList",
        method:"get",
        params: query
    })
}
//能源成本中心树
export function getCcostCenter(){
    return request({
        url:"/energy/center/treeselect",
        method:"get"
    })
  }
  //能源介质获取
export function getNnergyMed(){
    return request({
        url:"/energy/energy/queryAllEnergy",
        method:"get"
    })
  }
  //查询
export function TabSearch(query) {
    console.log(query)
    return request({
      url: "/energy/measure/pageList",
      method: 'get',
      params: query
    })
  }
  //删除
export function energyDelete(data) {
    console.log(data)
    return request({
      url: "/energy/measure/"+data,
      method: 'delete',
      // data: data
    })
  }
   //导出
   export function exportfile(query) {
    return request({
      url: "/energy/measure/export",
      method: 'post',
      params: query
    })
}