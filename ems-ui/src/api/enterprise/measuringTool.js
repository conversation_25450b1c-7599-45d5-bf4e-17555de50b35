import request from '@/utils/request'

// 查询表具管理列表
export function listMeasuringTool(query) {
  return request({
    url: '/enterprise/measuringTool/list',
    method: 'get',
    params: query
  })
}

// 查询表具管理详细
export function getMeasuringTool(measuringToolId) {
  return request({
    url: '/enterprise/measuringTool/' + measuringToolId,
    method: 'get'
  })
}

// 新增表具管理
export function addMeasuringTool(data) {
  return request({
    url: '/enterprise/measuringTool',
    method: 'post',
    data: data
  })
}

// 修改表具管理
export function updateMeasuringTool(data) {
  return request({
    url: '/enterprise/measuringTool',
    method: 'put',
    data: data
  })
}

// 删除表具管理
export function delMeasuringTool(measuringToolId) {
  return request({
    url: '/enterprise/measuringTool/' + measuringToolId,
    method: 'delete'
  })
}
