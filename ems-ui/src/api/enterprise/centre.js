import request from '@/utils/request'
//获取下拉数据
export function get_department(){
    return request({
        url:"/energy/center/treeselect",
        method:"get"
    })
}
//添加
export function costAdd(data) {
    console.log(data)
    return request({
      url: "/energy/center",
      method: 'post',
      data: data
    })
  }
  //获取回显数据
  export function datacome(data) {
    console.log(data)
    return request({
      url: "/energy/center/"+data,
      method: 'get',
      // data: data
    })
  }
  //修改
  export function costChange(data) {
    console.log(data)
    return request({
      url: "/energy/center",
      method: 'put',
      data: data
    })
  }