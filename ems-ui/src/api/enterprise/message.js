import request from '@/utils/request'
//获取能源介质下拉数据
export function get_energy_med(){
    return request({
        url:"/energy/energy/queryAllEnergy",
        method:"get"
    })
}
//获取上一级计量对象下拉数据
export function get_mea_preat(){
    return request({
        url:"/energy/measure/treeselect",
        method:"get"
    })
}
//获取能源成本中心数据
export function get_cost_center(){
    return request({
        url:"/energy/center/treeselect",
        method:"get"
    })
}
//获取企业树
export function getfirmList(){
    return request({
        url:"/energy/factory/treeselect",
        method:"get"
    })
}
//提交数据
export function submitData(data){
    return request({
        url:"/energy/measure",
        method:"post",
        data:data
    })
}
//获取回显数据
export function datacome(data) {
    console.log(data)
    return request({
      url: "/energy/measure/"+data,
      method: 'get',
      // data: data
    })
  }

  //修改
  export function costChange(data) {
    console.log(data)
    return request({
      url: "/energy/measure",
      method: 'put',
      data: data
    })
  }