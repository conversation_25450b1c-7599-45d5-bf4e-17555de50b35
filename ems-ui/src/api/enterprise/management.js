import request from '@/utils/request'
//获取表格数据
export function get_Tablist(query){
    return request({
        url:"/energy/relat/pagelist",
        method:"get",
        params: query
    })
}
//能源介质获取
export function get_energy_med(){
  return request({
      url:"/energy/energy/queryAllEnergy",
      method:"get"
  })
}
//能源成本中心树
export function getCcostCenter(){
  return request({
      url:"/energy/center/treeselect",
      method:"get"
  })
}
//企业数据
export function getfirmList(){
  return request({
      url:"/energy/factory/treeselect",
      method:"get"
  })
}
//查询
export function TabSearch(query) {
    return request({
      url: "/energy/relat/pagelist",
      method: 'get',
      params: query
    })
  }
  //导出
  export function exportfile(query) {
    return request({
      url: "/energy/relat/export",
      method: 'post',
      params: query
    })
}
//删除
export function energyDelete(data) {
  console.log(data)
  return request({
    url: "/energy/relat/"+data,
    method: 'delete',
    // data: data
  })
}