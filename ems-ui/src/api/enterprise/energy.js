import request from '@/utils/request'
//获取数据
export function get_energy(query){
    return request({
        url:"/energy/center/pagelist",
        method:"get",
        params: query
    })
}
//层级关系
export function get_tree(){
    return request({
        url:"/energy/center/treeselect",
        method:"get"
    })
}
//搜索
export function TabSearch(query) {
    console.log(query)
    return request({
      url: "/energy/center/pagelist",
      method: 'get',
      params: query
    })
  }

//导出
 export function exportfile(query) {
        return request({
          url: "/energy/center/export",
          method: 'post',
          params: query
        })
 }
 //删除
 //delete
 export function energyDelete(data) {
  console.log(data)
  return request({
    url: "/energy/center/"+data,
    method: 'delete',
    // data: data
  })
}
