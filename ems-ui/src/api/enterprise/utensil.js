import request from '@/utils/request'
//电表树的数据获取
export function getAddList(){
    return request({
        url:"/energy/measure/treeselect",
        method:"get"
    })
}
//能源介质获取
export function get_energy_med(){
    return request({
        url:"/energy/energy/queryAllEnergy",
        method:"get"
    })
}
//维护类树
export function getMaintainType(){
    return request({
        url:"/energy/factory/treeselect",
        method:"get"
    })
}
//获取企业树
export function getfirmList(){
    return request({
        url:"/energy/factory/treeselect",
        method:"get"
    })
}
//能源成本中心树
export function getCcostCenter(){
    return request({
        url:"/energy/center/treeselect",
        method:"get"
    })
}
//提交数据
export function submitData(data){
    return request({
        url:"/energy/relat",
        method:"post",
        data:data
    })
}
//获取部门下的电表
export function getDepartment(data) {
    return request({
      url: "/energy/measure/costCent/"+data,
      method: 'get',
      // data: data
    })
  }
//获取回显数据
export function datacome(data) {
    return request({
      url: "/energy/relat/"+data,
      method: 'get',
      // data: data
    })
  }
 //修改
 export function costChange(data) {
     console.log(data)
        return request({
          url: "/energy/relat/update",
          method: 'put',
          data: data
        })
      }
