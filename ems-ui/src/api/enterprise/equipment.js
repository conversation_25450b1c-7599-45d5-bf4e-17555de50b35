import request from '@/utils/request'

// 查询工厂设备列表
export function listDevice(query) {
  return request({
    url: '/energy/device/list',
    method: 'get',
    params: query
  })
}
//获取计量器具数据
export function getmeasureList() {
  return request({
    url: '/energy/measure/pageList' ,
    method: 'get'
  })
}
//获取下拉框数据
export function getEnergy() {
  return request({
    url: '/energy/energy/queryAllEnergy' ,
    method: 'get'
  })
}
// 查询工厂设备详细
export function getDevice(equId) {
  return request({
    url: '/energy/device/' + equId,
    method: 'get'
  })
}

// 新增工厂设备
export function addDevice(data) {
  return request({
    url: '/energy/device',
    method: 'post',
    data: data
  })
}

// 修改工厂设备
export function updateDevice(data) {
  return request({
    url: '/energy/device',
    method: 'put',
    data: data
  })
}

// 删除工厂设备
export function delDevice(equId) {
  console.log(equId)
  return request({
    url: '/energy/device/' + equId,
    method: 'delete'
  })
}
 //导出
 export function exportfile(query) {
  return request({
    url: "/energy/device/export",
    method: 'post',
    params: query
  })
}
