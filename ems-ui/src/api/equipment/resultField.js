import request from '@/utils/request'

// 查询结果字段列表
export function listResultField(query) {
  return request({
    url: '/equipment/resultField/list',
    method: 'get',
    params: query
  })
}

// 查询结果字段详细
export function getResultField(resultFieldId) {
  return request({
    url: '/equipment/resultField/' + resultFieldId,
    method: 'get'
  })
}

// 新增结果字段
export function addResultField(data) {
  return request({
    url: '/equipment/resultField',
    method: 'post',
    data: data
  })
}

// 修改结果字段
export function updateResultField(data) {
  return request({
    url: '/equipment/resultField',
    method: 'put',
    data: data
  })
}

// 删除结果字段
export function delResultField(resultFieldId) {
  return request({
    url: '/equipment/resultField/' + resultFieldId,
    method: 'delete'
  })
}

// 导出结果字段
export function exportResultField(query) {
  return request({
    url: '/equipment/resultField/export',
    method: 'get',
    params: query
  })
}