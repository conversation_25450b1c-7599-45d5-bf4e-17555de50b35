import request from '@/utils/request'

// 查询bom列表
export function listBom(query) {
  return request({
    url: '/equipment/bom/list',
    method: 'get',
    params: query
  })
}

// 查询bom详细
export function getBom(bomId) {
  return request({
    url: '/equipment/bom/' + bomId,
    method: 'get'
  })
}

// 新增bom
export function addBom(data) {
  return request({
    url: '/equipment/bom',
    method: 'post',
    data: data
  })
}

// 修改bom
export function updateBom(data) {
  return request({
    url: '/equipment/bom',
    method: 'put',
    data: data
  })
}

// 删除bom
export function delBom(bomId) {
  return request({
    url: '/equipment/bom/' + bomId,
    method: 'delete'
  })
}

// 导出bom
export function exportBom(query) {
  return request({
    url: '/equipment/bom/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/bom/importTemplate',
    method: 'get',
    params: query
  })
}
