import request from '@/utils/request'

// 查询焊缝管理列表
export function listWeld(query) {
  return request({
    url: '/equipment/weld/list',
    method: 'get',
    params: query
  })
}

// 查询焊缝管理详细
export function getWeld(weldId) {
  return request({
    url: '/equipment/weld/' + weldId,
    method: 'get'
  })
}

// 新增焊缝管理
export function addWeld(data) {
  return request({
    url: '/equipment/weld',
    method: 'post',
    data: data
  })
}

// 修改焊缝管理
export function updateWeld(data) {
  return request({
    url: '/equipment/weld',
    method: 'put',
    data: data
  })
}

// 删除焊缝管理
export function delWeld(weldId) {
  return request({
    url: '/equipment/weld/' + weldId,
    method: 'delete'
  })
}

// 导出焊缝管理
export function exportWeld(query) {
  return request({
    url: '/equipment/weld/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/weld/importTemplate',
    method: 'get',
    params: query
  })
}
