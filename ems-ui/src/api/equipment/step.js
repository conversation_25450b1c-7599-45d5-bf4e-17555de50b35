import request from '@/utils/request'

// 查询工步管理列表
export function listStep(query) {
  return request({
    url: '/equipment/step/list',
    method: 'get',
    params: query
  })
}

// 查询工步管理详细
export function getStep(stepId) {
  return request({
    url: '/equipment/step/' + stepId,
    method: 'get'
  })
}

// 新增工步管理
export function addStep(data) {
  return request({
    url: '/equipment/step',
    method: 'post',
    data: data
  })
}

// 修改工步管理
export function updateStep(data) {
  return request({
    url: '/equipment/step',
    method: 'put',
    data: data
  })
}

// 删除工步管理
export function delStep(stepId) {
  return request({
    url: '/equipment/step/' + stepId,
    method: 'delete'
  })
}

// 导出工步管理
export function exportStep(query) {
  return request({
    url: '/equipment/step/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/step/importTemplate',
    method: 'get',
    params: query
  })
}
