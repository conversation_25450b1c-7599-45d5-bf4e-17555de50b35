import request from '@/utils/request'

// 查询工单列表
export function listOrder(query) {
  return request({
    url: '/equipment/workOrder/list',
    method: 'get',
    params: query
  })
}

// 查询工单详细
export function getOrder(workOrderId) {
  return request({
    url: '/equipment/workOrder/' + workOrderId,
    method: 'get'
  })
}

// 新增工单
export function addOrder(data) {
  return request({
    url: '/equipment/workOrder',
    method: 'post',
    data: data
  })
}

// 修改工单
export function updateOrder(data) {
  return request({
    url: '/equipment/workOrder',
    method: 'put',
    data: data
  })
}

// 删除工单
export function delOrder(workOrderId) {
  return request({
    url: '/equipment/workOrder/' + workOrderId,
    method: 'delete'
  })
}

// 导出工单
export function exportOrder(query) {
  return request({
    url: '/equipment/workOrder/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/workOrder/importTemplate',
    method: 'get',
    params: query
  })
}
