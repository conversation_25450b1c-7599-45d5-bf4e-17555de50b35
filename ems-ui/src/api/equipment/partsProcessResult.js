import request from '@/utils/request'

// 查询零件工艺结果列表
export function listPartsProcessResult(query) {
  return request({
    url: '/equipment/partsProcessResult/list',
    method: 'get',
    params: query
  })
}

// 查询零件工艺结果详细
export function getPartsProcessResult(partsProcessResultId) {
  return request({
    url: '/equipment/partsProcessResult/' + partsProcessResultId,
    method: 'get'
  })
}

// 新增零件工艺结果
export function addPartsProcessResult(data) {
  return request({
    url: '/equipment/partsProcessResult',
    method: 'post',
    data: data
  })
}

// 修改零件工艺结果
export function updatePartsProcessResult(data) {
  return request({
    url: '/equipment/partsProcessResult',
    method: 'put',
    data: data
  })
}

// 删除零件工艺结果
export function delPartsProcessResult(partsProcessResultId) {
  return request({
    url: '/equipment/partsProcessResult/' + partsProcessResultId,
    method: 'delete'
  })
}

// 导出零件工艺结果
export function exportPartsProcessResult(query) {
  return request({
    url: '/equipment/partsProcessResult/export',
    method: 'get',
    params: query
  })
}