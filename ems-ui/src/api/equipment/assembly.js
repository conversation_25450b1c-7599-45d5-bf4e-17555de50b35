import request from '@/utils/request'

// 查询总成列表
export function listAssembly(query) {
  return request({
    url: '/equipment/assembly/list',
    method: 'get',
    params: query
  })
}

// 查询总成详细
export function getAssembly(assemblyId) {
  return request({
    url: '/equipment/assembly/' + assemblyId,
    method: 'get'
  })
}

// 新增总成
export function addAssembly(data) {
  return request({
    url: '/equipment/assembly',
    method: 'post',
    data: data
  })
}

// 修改总成
export function updateAssembly(data) {
  return request({
    url: '/equipment/assembly',
    method: 'put',
    data: data
  })
}

// 删除总成
export function delAssembly(assemblyId) {
  return request({
    url: '/equipment/assembly/' + assemblyId,
    method: 'delete'
  })
}

// 导出总成
export function exportAssembly(query) {
  return request({
    url: '/equipment/assembly/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/assembly/importTemplate',
    method: 'get',
    params: query
  })
}
