import request from '@/utils/request'

// 查询焊缝明细列表
export function listWeldDetail(query) {
  return request({
    url: `/equipment/weldDetail/list`,
    method: 'get',
    params: query
  })
}

// 查询焊缝明细详细
export function getWeldDetail(weldDetailId) {
  return request({
    url: '/equipment/weldDetail/' + weldDetailId,
    method: 'get'
  })
}

// 新增焊缝明细
export function addWeldDetail(data) {
  return request({
    url: '/equipment/weldDetail',
    method: 'post',
    data: data
  })
}

// 修改焊缝明细
export function updateWeldDetail(data) {
  return request({
    url: '/equipment/weldDetail',
    method: 'put',
    data: data
  })
}

// 删除焊缝明细
export function delWeldDetail(weldDetailId) {
  return request({
    url: '/equipment/weldDetail/' + weldDetailId,
    method: 'delete'
  })
}

// 导出焊缝明细
export function exportWeldDetail(query) {
  return request({
    url: '/equipment/weldDetail/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/weldDetail/importTemplate',
    method: 'get',
    params: query
  })
}
