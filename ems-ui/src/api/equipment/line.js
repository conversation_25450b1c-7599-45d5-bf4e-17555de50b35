import request from '@/utils/request'

// 查询产线管理列表
export function listLine(query) {
  return request({
    url: '/equipment/productionLine/list',
    method: 'get',
    params: query
  })
}

// 查询产线管理详细
export function getLine(productionLineId) {
  return request({
    url: '/equipment/productionLine/' + productionLineId,
    method: 'get'
  })
}

// 新增产线管理
export function addLine(data) {
  return request({
    url: '/equipment/productionLine',
    method: 'post',
    data: data
  })
}

// 修改产线管理
export function updateLine(data) {
  return request({
    url: '/equipment/productionLine',
    method: 'put',
    data: data
  })
}

// 删除产线管理
export function delLine(productionLineId) {
  return request({
    url: '/equipment/productionLine/' + productionLineId,
    method: 'delete'
  })
}

// 导出产线管理
export function exportLine(query) {
  return request({
    url: '/equipment/productionLine/export',
    method: 'get',
    params: query
  })
}
 // 工厂list
export function factoryList(query) {
  return request({
    url: '/equipment/productionLine/list',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/productionLine/importTemplate',
    method: 'get',
    params: query
  })
}
