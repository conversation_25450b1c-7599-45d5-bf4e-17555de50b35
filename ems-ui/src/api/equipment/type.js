import request from '@/utils/request'

// 查询车型列表
export function listType(query) {
  return request({
    url: '/equipment/carType/list',
    method: 'get',
    params: query
  })
}

// 查询车型详细
export function getType(carTypeId) {
  return request({
    url: '/equipment/carType/' + carTypeId,
    method: 'get'
  })
}

// 新增车型
export function addType(data) {
  return request({
    url: '/equipment/carType',
    method: 'post',
    data: data
  })
}

// 修改车型
export function updateType(data) {
  return request({
    url: '/equipment/carType',
    method: 'put',
    data: data
  })
}

// 删除车型
export function delType(carTypeId) {
  return request({
    url: '/equipment/carType/' + carTypeId,
    method: 'delete'
  })
}

// 导出车型
export function exportType(query) {
  return request({
    url: '/equipment/carType/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/carType/importTemplate',
    method: 'get',
    params: query
  })
}
