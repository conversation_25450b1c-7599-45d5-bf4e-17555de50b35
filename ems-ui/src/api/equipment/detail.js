import request from '@/utils/request'

// 查询焊缝明细列表
export function listDetail(query) {
  return request({
    url: '/equipment/detail/list',
    method: 'get',
    params: query
  })
}

// 查询焊缝明细详细
export function getDetail(weldDetailId) {
  return request({
    url: '/equipment/detail/' + weldDetailId,
    method: 'get'
  })
}

// 新增焊缝明细
export function addDetail(data) {
  return request({
    url: '/equipment/detail',
    method: 'post',
    data: data
  })
}

// 修改焊缝明细
export function updateDetail(data) {
  return request({
    url: '/equipment/detail',
    method: 'put',
    data: data
  })
}

// 删除焊缝明细
export function delDetail(weldDetailId) {
  return request({
    url: '/equipment/detail/' + weldDetailId,
    method: 'delete'
  })
}

// 导出焊缝明细
export function exportDetail(query) {
  return request({
    url: '/equipment/detail/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/detail/importTemplate',
    method: 'get',
    params: query
  })
}
