import request from '@/utils/request'

//查询工位列表
export function listFactory(query) {
  return request({
    url: '/equipment/factory/JComboBoxMessageEndStation',
    method: 'get',
    params: query
  })
}
// 查询设备列表
export function listDevice(query) {
  return request({
    url: '/equipment/device/list',
    method: 'get',
    params: query
  })
}

// 查询设备详细
export function getDevice(deviceId) {
  return request({
    url: '/equipment/device/' + deviceId,
    method: 'get'
  })
}

// 新增设备
export function addDevice(data) {
  return request({
    url: '/equipment/device',
    method: 'post',
    data: data
  })
}

// 修改设备
export function updateDevice(data) {
  return request({
    url: '/equipment/device',
    method: 'put',
    data: data
  })
}

// 删除设备
export function delDevice(deviceId) {
  return request({
    url: '/equipment/device/' + deviceId,
    method: 'delete'
  })
}

// 导出设备
export function exportDevice(query) {
  return request({
    url: '/equipment/device/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/device/importTemplate',
    method: 'get',
    params: query
  })
}
