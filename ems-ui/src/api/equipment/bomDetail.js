import request from '@/utils/request'

// 查询bom详情列表
export function listBomDetail(query) {
  return request({
    url: '/equipment/bomDetail/list',
    method: 'get',
    params: query
  })
}

// 查询bom详情详细
export function getBomDetail(bomDetailId) {
  return request({
    url: '/equipment/bomDetail/' + bomDetailId,
    method: 'get'
  })
}

// 新增bom详情
export function addBomDetail(data) {
  return request({
    url: '/equipment/bomDetail',
    method: 'post',
    data: data
  })
}

// 修改bom详情
export function updateBomDetail(data) {
  return request({
    url: '/equipment/bomDetail',
    method: 'put',
    data: data
  })
}

// 删除bom详情
export function delBomDetail(bomDetailId) {
  return request({
    url: '/equipment/bomDetail/' + bomDetailId,
    method: 'delete'
  })
}

// 导出bom详情
export function exportBomDetail(query) {
  return request({
    url: '/equipment/bomDetail/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/bomDetail/importTemplate',
    method: 'get',
    params: query
  })
}
