import request from '@/utils/request'

// 查询工厂列表
export function listFactory(query) {
  return request({
    url: '/equipment/factory/list',
    method: 'get',
    params: query
  })
}

// 查询工厂详细
export function getFactory(factoryId) {
  return request({
    url: '/equipment/factory/' + factoryId,
    method: 'get'
  })
}

// 新增工厂
export function addFactory(data) {
  return request({
    url: '/equipment/factory',
    method: 'post',
    data: data
  })
}

// 修改工厂
export function updateFactory(data) {
  return request({
    url: '/equipment/factory',
    method: 'put',
    data: data
  })
}

// 删除工厂
export function delFactory(factoryId) {
  return request({
    url: '/equipment/factory/' + factoryId,
    method: 'delete'
  })
}

// 导出工厂
export function exportFactory(query) {
  return request({
    url: '/equipment/factory/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/factory/importTemplate',
    method: 'get',
    params: query
  })
}
