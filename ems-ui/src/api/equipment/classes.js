import request from '@/utils/request'

// 查询班次列表
export function listClasses(query) {
  return request({
    url: '/equipment/classes/list',
    method: 'get',
    params: query
  })
}

// 查询班次详细
export function getClasses(classesId) {
  return request({
    url: '/equipment/classes/' + classesId,
    method: 'get'
  })
}

// 新增班次
export function addClasses(data) {
  return request({
    url: '/equipment/classes',
    method: 'post',
    data: data
  })
}

// 修改班次
export function updateClasses(data) {
  return request({
    url: '/equipment/classes',
    method: 'put',
    data: data
  })
}

// 删除班次
export function delClasses(classesId) {
  return request({
    url: '/equipment/classes/' + classesId,
    method: 'delete'
  })
}

// 导出班次
export function exportClasses(query) {
  return request({
    url: '/equipment/classes/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/classes/importTemplate',
    method: 'get',
    params: query
  })
}
