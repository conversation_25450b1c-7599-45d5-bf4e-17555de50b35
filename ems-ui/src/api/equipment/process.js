import request from '@/utils/request'

// 查询工艺列表
export function listProcess(query) {
  return request({
    url: '/equipment/process/list',
    method: 'get',
    params: query
  })
}

// 查询工艺详细
export function getProcess(processId) {
  return request({
    url: '/equipment/process/' + processId,
    method: 'get'
  })
}

// 新增工艺
export function addProcess(data) {
  return request({
    url: '/equipment/process',
    method: 'post',
    data: data
  })
}

// 修改工艺
export function updateProcess(data) {
  return request({
    url: '/equipment/process',
    method: 'put',
    data: data
  })
}

// 删除工艺
export function delProcess(processId) {
  return request({
    url: '/equipment/process/' + processId,
    method: 'delete'
  })
}

// 导出工艺
export function exportProcess(query) {
  return request({
    url: '/equipment/process/export',
    method: 'get',
    params: query
  })
}

// 模板下载
export function importTemplate(query) {
  return request({
    url: '/equipment/process/importTemplate',
    method: 'get',
    params: query
  })
}
