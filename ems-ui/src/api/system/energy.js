import request from '@/utils/request'

// 查询能源介质列表
export function listEnergy(query) {
  return request({
    url: '/system/energy/list',
    method: 'get',
    params: query
  })
}

// 查询能源介质详细
export function getEnergy(energyId) {
  return request({
    url: '/system/energy/' + energyId,
    method: 'get'
  })
}

// 新增能源介质
export function addEnergy(data) {
  return request({
    url: '/system/energy',
    method: 'post',
    data: data
  })
}

// 修改能源介质
export function updateEnergy(data) {
  return request({
    url: '/system/energy',
    method: 'put',
    data: data
  })
}

// 删除能源介质
export function delEnergy(energyId) {
  return request({
    url: '/system/energy/' + energyId,
    method: 'delete'
  })
}
