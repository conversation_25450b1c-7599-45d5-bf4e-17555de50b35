import request from '@/utils/request'

// 查询成本中心列表
export function listCenter(query) {
  return request({
    url: '/system/center/list',
    method: 'get',
    params: query
  })
}

// 查询成本中心详细
export function getCenter(centerId) {
  return request({
    url: '/system/center/' + centerId,
    method: 'get'
  })
}

// 新增成本中心
export function addCenter(data) {
  return request({
    url: '/system/center',
    method: 'post',
    data: data
  })
}

// 修改成本中心
export function updateCenter(data) {
  return request({
    url: '/system/center',
    method: 'put',
    data: data
  })
}

// 删除成本中心
export function delCenter(centerId) {
  return request({
    url: '/system/center/' + centerId,
    method: 'delete'
  })
}
