import request from '@/utils/request'

// 查询计量器具列表
export function listMeasure(query) {
  return request({
    url: '/system/measure/list',
    method: 'get',
    params: query
  })
}

// 查询计量器具详细
export function getMeasure(meaId) {
  return request({
    url: '/system/measure/' + meaId,
    method: 'get'
  })
}

// 新增计量器具
export function addMeasure(data) {
  return request({
    url: '/system/measure',
    method: 'post',
    data: data
  })
}

// 修改计量器具
export function updateMeasure(data) {
  return request({
    url: '/system/measure',
    method: 'put',
    data: data
  })
}

// 删除计量器具
export function delMeasure(meaId) {
  return request({
    url: '/system/measure/' + meaId,
    method: 'delete'
  })
}
