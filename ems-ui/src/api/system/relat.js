import request from '@/utils/request'

// 查询计量器具和成本中心关系列表
export function listRelat(query) {
  return request({
    url: '/system/relat/list',
    method: 'get',
    params: query
  })
}

// 查询计量器具和成本中心关系详细
export function getRelat(relatId) {
  return request({
    url: '/system/relat/' + relatId,
    method: 'get'
  })
}

// 新增计量器具和成本中心关系
export function addRelat(data) {
  return request({
    url: '/system/relat',
    method: 'post',
    data: data
  })
}

// 修改计量器具和成本中心关系
export function updateRelat(data) {
  return request({
    url: '/system/relat',
    method: 'put',
    data: data
  })
}

// 删除计量器具和成本中心关系
export function delRelat(relatId) {
  return request({
    url: '/system/relat/' + relatId,
    method: 'delete'
  })
}
