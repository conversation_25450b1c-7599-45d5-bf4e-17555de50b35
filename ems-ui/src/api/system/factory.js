import request from '@/utils/request'

// 查询企业架构列表
export function listFactory(query) {
  return request({
    url: '/system/factory/list',
    method: 'get',
    params: query
  })
}

// 查询企业架构详细
export function getFactory(deptId) {
  return request({
    url: '/system/factory/' + deptId,
    method: 'get'
  })
}

// 新增企业架构
export function addFactory(data) {
  return request({
    url: '/system/factory',
    method: 'post',
    data: data
  })
}

// 修改企业架构
export function updateFactory(data) {
  return request({
    url: '/system/factory',
    method: 'put',
    data: data
  })
}

// 删除企业架构
export function delFactory(deptId) {
  return request({
    url: '/system/factory/' + deptId,
    method: 'delete'
  })
}
