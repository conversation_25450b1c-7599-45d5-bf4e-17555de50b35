import request from '@/utils/request'

// 查询电价配置列表
export function listElecPriceConfig(query) {
  return request({
    url: '/energy/elecPriceConfig/list',
    method: 'get',
    params: query
  })
}

// 查询电价配置详细
export function getElecPriceConfig(configId) {
  return request({
    url: '/energy/elecPriceConfig/' + configId,
    method: 'get'
  })
}

// 新增电价配置
export function addElecPriceConfig(data) {
  return request({
    url: '/energy/elecPriceConfig',
    method: 'post',
    data: data
  })
}

// 修改电价配置
export function updateElecPriceConfig(data) {
  return request({
    url: '/energy/elecPriceConfig',
    method: 'put',
    data: data
  })
}

// 删除电价配置
export function delElecPriceConfig(configId) {
  return request({
    url: '/energy/elecPriceConfig/' + configId,
    method: 'delete'
  })
}
