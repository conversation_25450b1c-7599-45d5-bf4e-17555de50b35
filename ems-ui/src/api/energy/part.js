import request from '@/utils/request'

// 查询产品管理列表
export function listPart(query) {
  return request({
    url: '/energy/part/list',
    method: 'get',
    params: query
  })
}

// 查询产品管理详细
export function getPart(partId) {
  return request({
    url: '/energy/part/' + partId,
    method: 'get'
  })
}

// 新增产品管理
export function addPart(data) {
  return request({
    url: '/energy/part',
    method: 'post',
    data: data
  })
}

// 修改产品管理
export function updatePart(data) {
  return request({
    url: '/energy/part',
    method: 'put',
    data: data
  })
}

// 删除产品管理
export function delPart(partId) {
  return request({
    url: '/energy/part/' + partId,
    method: 'delete'
  })
}
