import request from '@/utils/request'

// 查询表具列表
export function listMeasuringTool(query) {
  return request({
    url: '/energy/measuringTool/list',
    method: 'get',
    params: query
  })
}

// 查询真实表具列表
export function listRealMeasuringTool(query) {
  return request({
    url: '/energy/measuringTool/listReal',
    method: 'get',
    params: query
  })
}

// 查询表具详细
export function getMeasuringTool(measuringToolId) {
  return request({
    url: '/energy/measuringTool/' + measuringToolId,
    method: 'get'
  })
}

// 新增表具
export function addMeasuringTool(data) {
  return request({
    url: '/energy/measuringTool',
    method: 'post',
    data: data
  })
}

// 修改表具
export function updateMeasuringTool(data) {
  return request({
    url: '/energy/measuringTool',
    method: 'put',
    data: data
  })
}

// 删除表具
export function delMeasuringTool(measuringToolId) {
  return request({
    url: '/energy/measuringTool/' + measuringToolId,
    method: 'delete'
  })
}
