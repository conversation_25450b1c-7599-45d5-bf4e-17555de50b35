import request from '@/utils/request'

// 查询碳排放因子设置列表
export function listCarbonFactor(query) {
  return request({
    url: '/energy/carbonFactor/list',
    method: 'get',
    params: query
  })
}

// 查询碳排放因子设置详细
export function getCarbonFactor(factorId) {
  return request({
    url: '/energy/carbonFactor/' + factorId,
    method: 'get'
  })
}

// 新增碳排放因子设置
export function addCarbonFactor(data) {
  return request({
    url: '/energy/carbonFactor',
    method: 'post',
    data: data
  })
}

// 修改碳排放因子设置
export function updateCarbonFactor(data) {
  return request({
    url: '/energy/carbonFactor',
    method: 'put',
    data: data
  })
}

// 删除碳排放因子设置
export function delCarbonFactor(factorId) {
  return request({
    url: '/energy/carbonFactor/' + factorId,
    method: 'delete'
  })
}
