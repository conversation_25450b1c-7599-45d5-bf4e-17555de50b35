import request from '@/utils/request'

export function queryTopEnergyByTag(query) {
  return request({
    url: '/energy/report/queryTopEnergyByTag',
    method: 'get',
    params: query
  })
}

export function energyReportWithCategoryByEquipment(query) {
  return request({
    url: '/energy/report/energyReportWithCategoryByEquipment',
    method: 'get',
    params: query
  })
}

export function factoryEnergyReportWithCategory(query) {
  return request({
    url: '/energy/report/factoryEnergyReportWithCategory',
    method: 'get',
    params: query
  })
}

export function calendarEnergyReport(query) {
  return request({
    url: '/energy/report/calendarEnergyReport',
    method: 'get',
    params: query
  })
}

export function calendarElecAmountReport(query) {
  return request({
    url: '/energy/report/calendarElecAmountReport',
    method: 'get',
    params: query
  })
}

export function calendarCarbonReport(query) {
  return request({
    url: '/energy/report/calendarCarbonReport',
    method: 'get',
    params: query
  })
}

export function monthEnergyReport(query) {
  return request({
    url: '/energy/report/monthEnergyReport',
    method: 'get',
    params: query
  })
}

export function oneMonthEnergyCateogryReport(query) {
  return request({
    url: '/energy/report/oneMonthEnergyCateogryReport',
    method: 'get',
    params: query
  })
}

export function oneMonthCarbonCateogryReport(query) {
  return request({
    url: '/energy/report/oneMonthCarbonCateogryReport',
    method: 'get',
    params: query
  })
}

export function oneMonthElecAmountCateogryReport(query) {
  return request({
    url: '/energy/report/oneMonthElecAmountCateogryReport',
    method: 'get',
    params: query
  })
}

export function queryOneMonthTotalEnergy(query) {
  return request({
    url: '/energy/report/queryOneMonthTotalEnergy',
    method: 'get',
    params: query
  })
}

export function queryOneMonthTotalCarbon(query) {
  return request({
    url: '/energy/report/queryOneMonthTotalCarbon',
    method: 'get',
    params: query
  })
}

export function queryOneMonthTotalElecAmount(query) {
  return request({
    url: '/energy/report/queryOneMonthTotalElecAmount',
    method: 'get',
    params: query
  })
}

export function queryFactoryMd(query) {
  return request({
    url: '/energy/report/queryFactoryMd',
    method: 'get',
    params: query
  })
}

export function queryPcfReport(query) {
  return request({
    url: '/energy/report/queryPcfReport',
    method: 'get',
    params: query
  })
}

export function queryPartsEquivalentCarbon(query) {
  return request({
    url: '/energy/report/queryPartsEquivalentCarbon',
    method: 'post',
    data: query
  })
}

export function queryEquipmentCarbonDayReport(query) {
  return request({
    url: '/energy/report/queryEquipmentCarbonDayReport',
    method: 'get',
    params: query
  })
}

export function queryEquipmentsEquivalentCarbon(query) {
  return request({
    url: '/energy/report/queryEquipmentsEquivalentCarbon',
    method: 'post',
    data: query
  })
}

export function queryEquipmentsOeeReport(query) {
  return request({
    url: '/energy/report/queryEquipmentsOeeReport',
    method: 'post',
    data: query
  })
}

export function queryEquipmentStatusEnergyReport(query) {
  return request({
    url: '/energy/report/queryEquipmentStatusEnergyReport',
    method: 'get',
    params: query
  })
}
