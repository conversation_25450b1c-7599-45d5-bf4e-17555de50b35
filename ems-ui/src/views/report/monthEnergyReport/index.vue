<template>
  <div class="app-container">
    <el-form :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="年">
        <el-date-picker
          v-model="time"
          size="small"
          style="width: 240px"
          type="year"
          :clearable=false
          value-format="yyyy"
          :picker-options="{disabledDate(time){return time>Date.now()}}"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <el-divider>用电量（KWh）</el-divider>
    <el-table v-loading="loading" :data="dataList[0]" border stripe>
      <el-table-column label="分类" align="center" prop="category" fixed="left"/>
      <el-table-column label="1月" align="center" prop="m01" />
      <el-table-column label="2月" align="center" prop="m02" />
      <el-table-column label="3月" align="center" prop="m03" />
      <el-table-column label="4月" align="center" prop="m04" />
      <el-table-column label="5月" align="center" prop="m05" />
      <el-table-column label="6月" align="center" prop="m06" />
      <el-table-column label="7月" align="center" prop="m07" />
      <el-table-column label="8月" align="center" prop="m08" />
      <el-table-column label="9月" align="center" prop="m09" />
      <el-table-column label="10月" align="center" prop="m10" />
      <el-table-column label="11月" align="center" prop="m11" />
      <el-table-column label="12月" align="center" prop="m12" />
    </el-table>
    <el-divider>碳排放（Kg）</el-divider>
    <el-table v-loading="loading" :data="dataList[1]" border stripe>
      <el-table-column label="分类" align="center" prop="category" fixed="left"/>
      <el-table-column label="1月" align="center" prop="m01" />
      <el-table-column label="2月" align="center" prop="m02" />
      <el-table-column label="3月" align="center" prop="m03" />
      <el-table-column label="4月" align="center" prop="m04" />
      <el-table-column label="5月" align="center" prop="m05" />
      <el-table-column label="6月" align="center" prop="m06" />
      <el-table-column label="7月" align="center" prop="m07" />
      <el-table-column label="8月" align="center" prop="m08" />
      <el-table-column label="9月" align="center" prop="m09" />
      <el-table-column label="10月" align="center" prop="m10" />
      <el-table-column label="11月" align="center" prop="m11" />
      <el-table-column label="12月" align="center" prop="m12" />
    </el-table>
    <el-divider>用电成本（元）</el-divider>
    <el-table v-loading="loading" :data="dataList[2]" border stripe>
      <el-table-column label="分类" align="center" prop="category" fixed="left"/>
      <el-table-column label="1月" align="center" prop="m01" />
      <el-table-column label="2月" align="center" prop="m02" />
      <el-table-column label="3月" align="center" prop="m03" />
      <el-table-column label="4月" align="center" prop="m04" />
      <el-table-column label="5月" align="center" prop="m05" />
      <el-table-column label="6月" align="center" prop="m06" />
      <el-table-column label="7月" align="center" prop="m07" />
      <el-table-column label="8月" align="center" prop="m08" />
      <el-table-column label="9月" align="center" prop="m09" />
      <el-table-column label="10月" align="center" prop="m10" />
      <el-table-column label="11月" align="center" prop="m11" />
      <el-table-column label="12月" align="center" prop="m12" />
    </el-table>
  </div>

</template>

<script>
import {monthEnergyReport} from "@/api/energy/report";
import dayjs from 'dayjs';

export default {
  name: "MonthEnergyReport",
  data() {
    return {
      showSearch: true,
      time: dayjs().format('YYYY'),
      dataList: [[],[],[]],
      loading:true,
    }
  },
  created(){
    this.time = dayjs().format('YYYY');
    this.handleQuery();
  },
  methods:{
    handleQuery(){
      this.loading = true
      monthEnergyReport({year:this.time}).then(response=>{
        this.dataList = response.data;
        this.loading = false;
      });
    }
  }

}
</script>

<style scoped>

</style>
