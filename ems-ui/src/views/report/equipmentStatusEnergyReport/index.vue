<template>
  <div class="app-container">
    <el-form :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="日期">
        <el-date-picker
          v-model="dateRange"
          style="width: 280px"
          type="daterange"
          :clearable=false
          value-format="yyyy-MM-dd"
          :picker-options="{disabledDate(time){return time.getTime()>Date.now()+86400000}}"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="设备" prop="equipmentId">
        <treeselect v-model="queryParam.equipmentId" :options="equipmentOptions" :normalizer="normalizer" placeholder="请选择设备" searchable style="width: 400px"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <common-bar-chart :chart-data="dataList" height="600px"/>
  </div>

</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {
  queryEquipmentsEquivalentCarbon,
  queryEquipmentsOeeReport,
  queryEquipmentStatusEnergyReport
} from "@/api/energy/report";
import CommonBarChart from "@/views/dashboard/CommonBarChart";
import {listEquipment} from "@/api/energy/equipment";
import dayjs from "dayjs";

export default {
  name: "EquipmentsOeeReport",
  components: {
    Treeselect,
    CommonBarChart,
  },
  data() {
    return {
      showSearch: true,
      loading: false,
      dateRange: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      dataList: [],
      equipmentOptions: [],
      queryParam:{
        equipmentId:null,
        start:null,
        end:null,
      }
    }
  },
  created(){
    this.queryEquipments();
  },
  methods:{
    handleQuery(){
      if (this.dateRange.length > 1 && null != this.dateRange[0] && null != this.dateRange[1]){
        this.queryParam.start = this.dateRange[0];
        this.queryParam.end = this.dateRange[1];
        queryEquipmentStatusEnergyReport(this.queryParam).then(response=>{
          this.dataList = response.data;
        });
      }

    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.equipmentId,
        label: node.equipmentName,
        children: node.children
      };
    },
    queryEquipments(){
      listEquipment().then(response => {
        this.equipmentOptions = this.handleTree(response.data, "equipmentId", "parentId");
        if (response.data.length > 0){
          this.queryParam.equipmentId = response.data[0].equipmentId;
        }
      });
    },
  }

}
</script>

<style scoped>

</style>
