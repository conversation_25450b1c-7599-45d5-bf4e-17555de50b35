<template>
  <div class="app-container">
    <el-form :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="日期">
        <el-date-picker
          v-model="dateRange"
          style="width: 280px"
          type="daterange"
          :clearable=false
          value-format="yyyy-MM-dd"
          :picker-options="{disabledDate(time){return time.getTime()>Date.now()+86400000}}"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="产品" prop="partId">
        <treeselect v-model="queryParam.partId" :options="partOptions" :normalizer="normalizer" placeholder="请选择产品" searchable style="width: 200px" :clearable="false"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="dataList" border stripe :span-method="tableSpanMethod">
      <el-table-column label="零件号" align="center" prop="partNo" fixed="left"/>
      <el-table-column label="产品名称" align="center" prop="partName"/>
      <el-table-column label="单件能耗" align="center" prop="selfEnergy"/>
      <el-table-column label="单位" align="center">
        <template>
          KWh
        </template>
      </el-table-column>
      <el-table-column label="数量" align="center" prop="totalCount"/>
      <el-table-column label="碳排放因子" align="center" prop="carbonFactor"/>
      <el-table-column label="碳排放因子单位" align="center" prop="carbonFactorUnit"/>
      <el-table-column label="碳排放" align="center">
        <template slot-scope="scope">
          {{(scope.row.selfEnergy*scope.row.totalCount*scope.row.carbonFactor).toFixed(4)}}
        </template>
      </el-table-column>
      <el-table-column label="总碳排放" align="center">
        <template slot-scope="scope">
          {{(scope.row.totalEnergy*scope.row.carbonFactor).toFixed(4)}}
        </template>
      </el-table-column>
    </el-table>
  </div>

</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {listPart} from "@/api/energy/part";
import {queryPcfReport} from "@/api/energy/report";
import dayjs from "dayjs";

export default {
  name: "PcfReport",
  components: {
    Treeselect
  },
  data() {
    return {
      showSearch: true,
      loading: false,
      dateRange:[dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      dataList: [],
      partOptions:[],
      queryParam:{
        partId:null,
        start:null,
        end:null,
      }
    }
  },
  created(){
    this.queryParts();
  },
  methods:{
    handleQuery(){
      if (this.dateRange.length > 1 && null != this.dateRange[0] && null != this.dateRange[1]){
        this.queryParam.start = this.dateRange[0];
        this.queryParam.end = this.dateRange[1];
        this.loading = true;
        queryPcfReport(this.queryParam).then(response=>{
          this.dataList = response.data;
          this.loading = false;
        });
      }

    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.partId,
        label: node.partName,
        children: node.children
      };
    },
    queryParts(){
      listPart().then(response => {

        this.partOptions = this.handleTree(response.data, "partId", "parentId");
        if (response.data.length>0){
          this.queryParam.partId = response.data[0].partId;
        }
      });
    },
    tableSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 8) {
        if (rowIndex === 0) {
          return {
            rowspan: this.dataList.length,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    }
  }

}
</script>

<style scoped>

</style>
