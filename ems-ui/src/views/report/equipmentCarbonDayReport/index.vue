<template>
  <div class="app-container">
    <el-form :inline="true" label-width="120px" :model="queryParam">
      <el-form-item label="日期">
        <el-date-picker
          v-model="queryParam.day"
          style="width: 240px"
          type="date"
          :clearable=false
          value-format="yyyy-MM-dd"
          :picker-options="{disabledDate(time){return time.getTime()>Date.now()}}"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="设备">
        <treeselect v-model="queryParam.equipmentId" :options="equipmentOptions" :normalizer="normalizer" placeholder="请选择设备" searchable style="width: 200px" :clearable="false"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="32">
      <el-col :sm="24" :md="24" :lg="6">
        <el-card class="box-card">
          <el-descriptions class="margin-top" title="设备信息" :column="1" border>
            <el-descriptions-item>
              <template slot="label">
                设备名称
              </template>
              {{carbonData.equipmentName}}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                加工时长
              </template>
              {{carbonData.hasOwnProperty('processTime')?`${(carbonData.processTime/60000).toFixed(0)} 分钟`:''}}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                用电量
              </template>
              {{carbonData.hasOwnProperty('energy')?`${carbonData.energy} KWh`:''}}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                碳排放
              </template>
              {{carbonData.hasOwnProperty('carbon')?`${carbonData.carbon} Kg`:''}}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
      <el-col :sm="24" :md="24" :lg="18">
        <common-bar-chart :chart-data="chartData" height="500px"/>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {listEquipment} from "@/api/energy/equipment";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import CommonBarChart from "@/views/dashboard/CommonBarChart";
import {energyReportWithCategoryByEquipment, queryEquipmentCarbonDayReport} from "@/api/energy/report";
import dayjs from "dayjs";

export default {
  name: "EquipmentCarbonDayReport",
  components:{Treeselect, CommonBarChart},
  data(){
    return {
      queryParam:{
        equipmentId: null,
        day: dayjs().subtract(1,'day').format('YYYY-MM-DD'),
      },
      queryDay: null,
      carbonData:{},
      equipmentOptions: [],
    }
  },
  computed:{
    chartData(){
      let result = [['分类','数据']];
      if (this.carbonData.hasOwnProperty('energy')){
        let mins = 1440;
        let time = dayjs(this.queryDay);
        let now = dayjs();
        if (time.isSame(now, 'day')){
          mins = now.hour()*60;
          if (now.minute()>=30){
            mins = mins+30;
          }
        }
        result.push(['分钟单耗', Math.round(this.carbonData.energy/mins*10000)/10000]);
        if (this.carbonData.equivalent){
          result.push(['当量单耗', Math.round(this.carbonData.energy/this.carbonData.equivalent*10000)/10000]);
        }else{
          result.push(['当量单耗', 0]);
        }

      }
      return result;
    },
  },
  created(){
    this.queryEquipment();
  },
  methods:{
    queryEquipment(){
      listEquipment({}).then(response=>{
        this.equipmentOptions = this.handleTree(response.data, "equipmentId", "parentId");
        if(response.data.length>0){
          this.queryParam.equipmentId = response.data[0].equipmentId;
        }
      })
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.equipmentId,
        label: node.equipmentName,
        children: node.children
      };
    },
    handleQuery() {
      this.queryDay = this.queryParam.day;
      queryEquipmentCarbonDayReport(this.queryParam).then(response => {
        this.carbonData = response.data;
      });
    }
  },
}
</script>

<style scoped>

</style>
