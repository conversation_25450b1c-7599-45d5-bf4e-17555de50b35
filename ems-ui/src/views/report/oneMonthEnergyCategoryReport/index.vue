<template>
  <div class="app-container">
    <el-form :inline="true" label-width="120px" :model="queryParam">
      <el-form-item label="月">
        <el-date-picker
          v-model="queryParam.month"
          style="width: 240px"
          type="month"
          :clearable=false
          value-format="yyyy-MM"
          :picker-options="{disabledDate(time){return time.getTime()>Date.now()}}"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="32">
      <el-col :md="24" :lg="12" :xs="24">
        <CommonPieChart :chart-data="energyData" height="600px" title="用电量（KWh）"/>
      </el-col>
      <el-col :md="24" :lg="12" :xs="24">
        <CommonPieChart :chart-data="carbonData" height="600px" title="碳排放（Kg）"/>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import dayjs from "dayjs";
import {oneMonthCarbonCateogryReport, oneMonthEnergyCateogryReport} from "@/api/energy/report";
import CommonPieChart from "@/views/dashboard/CommonPieChart";

export default {
  name: "OneMonthEnergyCategoryReport",
  components: {CommonPieChart},
  data(){
    return {
      queryParam:{
        month:dayjs().format('YYYY-MM')
      },
      energyData:[[]],
      carbonData: [[]],
    }
  },
  created() {
    this.handleQuery();
  },
  methods:{
    handleQuery(){
      oneMonthEnergyCateogryReport(this.queryParam).then(response=>{
        this.energyData = response.data;
      });
      oneMonthCarbonCateogryReport(this.queryParam).then(response=>{
        this.carbonData = response.data;
      });
    }
  }
}
</script>

<style scoped>

</style>
