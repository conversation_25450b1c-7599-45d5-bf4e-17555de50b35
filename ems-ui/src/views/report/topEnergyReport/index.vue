<template>
  <div class="app-container">
    <el-form :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="日期">
        <el-date-picker
          v-model="time"
          size="small"
          style="width: 240px"
          type="date"
          :clearable=false
          value-format="yyyy-MM-dd"
          :picker-options="{disabledDate(time){return time.getTime()>Date.now()-86400000}}"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="32">
      <el-col :xs="24" :md="24" :lg="24" v-for="(category, index) in dict.type.top_category" :key="index">
        <vertical-bar-chart :chart-data="dataList.length>index?dataList[index]:[['能耗','设备']]" :title="category.label" height="500px"/>
      </el-col>
    </el-row>
  </div>

</template>

<script>
import {queryTopEnergyByTag} from "@/api/energy/report";
import VerticalBarChart from "@/views/dashboard/VerticalBarChart";
import dayjs from 'dayjs';

export default {
  name: "TopEnergyReport",
  components: {VerticalBarChart},
  dicts: ['top_category'],
  data() {
    return {
      showSearch: true,
      time: null,
      dataList: [],
    }
  },
  created(){
    this.time = dayjs().subtract(1,'day').format('YYYY-MM-DD');
  },
  methods:{
    onDictReady(dict){
      this.handleQuery();
    },
    handleQuery(){
      this.dict.type.top_category.forEach((category, index)=>{
        this.$set(this.dataList, index+'', [['能耗','设备']]);
        queryTopEnergyByTag({tag:category.label, time:this.time}).then(response=>{
          this.$set(this.dataList, index+'', response.data);
        });
      });
    }
  }

}
</script>

<style scoped>

</style>
