<template>
  <div class="app-container">
    <el-form :inline="true" label-width="120px" :model="queryParam">
      <el-form-item label="日期">
        <el-date-picker
          v-model="dateRange"
          style="width: 280px"
          type="daterange"
          :clearable=false
          value-format="yyyy-MM-dd"
          :picker-options="{disabledDate(time){return time.getTime()>Date.now()+86400000}}"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <div id="report" style="width: 100%;height:600px;"></div>
  </div>
</template>

<script>
import resize from "@/views/dashboard/mixins/resize"
import {factoryEnergyReportWithCategory} from "@/api/energy/report";
import dayjs from "dayjs";
require("@/views/dashboard/theme/default");

export default {
  name: "FactoryEnergyReport",
  mixins: [resize],
  dicts:['elec_price_period_category'],
  data(){
    return {
      dateRange:[dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      queryParam:{
        start:null,
        end:null,
      },
      chartData:[],
      equipmentOptions: [],
      categories:[],
      chart:null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.setOptions()
      }
    }
  },
  created(){
    // this.queryEquipment();
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods:{
    onDictReady(dict){
      for (let category of dict.type.elec_price_period_category){
        this.categories.push(category.label);
      }
      this.initChart();
    },
    // queryEquipment(){
    //   listEquipment({}).then(response=>{
    //     this.equipmentOptions = this.handleTree(response.data, "equipmentId", "parentId");
    //     if(response.data.length>0){
    //       this.queryParam.equipmentId = response.data[0].equipmentId;
    //     }
    //   })
    // },
    // normalizer(node) {
    //   if (node.children && !node.children.length) {
    //     delete node.children;
    //   }
    //   return {
    //     id: node.equipmentId,
    //     label: node.equipmentName,
    //     children: node.children
    //   };
    // },
    handleQuery(){
      if (this.dateRange.length>1){
        this.queryParam.start = this.dateRange[0];
        this.queryParam.end = this.dateRange[1];
        factoryEnergyReportWithCategory(this.queryParam).then(response=>{
          this.chartData = response.data;
        })
      }

    },
    initChart() {
      this.chart = this.$echarts.init(document.getElementById('report'))
      this.setOptions()
    },
    setOptions() {
      let series =[];
      for (let category of this.categories){
        series.push({
          name: category,
          type:'bar',
          tooltip: {
            valueFormatter: function (value) {
              return value + ' KWh';
            }
          }
        });
      }
      series.push({
        type:'line',
        name:'平均电价',
        yAxisIndex:1,
        tooltip: {
          valueFormatter: function (value) {
            return value + ' 元/Kwh';
          }
        }
      })
      // let series = [];
      // let count = dataSet.length>1?dataSet[0].length-1:0;
      // for (let i = 0; i < count; i++){
      //   let ser = {type:'bar',
      //     label: {
      //       show: true,
      //       position: 'right',
      //       valueAnimation: true,
      //       formatter: (params)=>{
      //         return params.value[params.encode.x[0]].toFixed(2);
      //       }
      //     }
      //   }
      //   if (this.stack){
      //     ser.stack='total';
      //   }
      //   series.push(ser);
      // }
      this.chart.setOption({
        xAxis: {
          type:'category',
          // boundaryGap: false,
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: 50,
          right: 50,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        yAxis: [
          {
            type:'value',
            name:'用电量',
            axisLabel: {
              formatter: '{value} KWh'
            }
          },
          {
            type:'value',
            name:'平均电价',
            axisLabel: {
              formatter: '{value} 元/KWh'
            }
          }
        ],
        series,
        legend:{
          show: true
        },
        dataset:{
          source: this.chartData,
        },
        title:{
          text:this.title,
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
