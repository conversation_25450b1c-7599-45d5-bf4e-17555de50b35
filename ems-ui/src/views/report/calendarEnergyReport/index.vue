<template>
  <div class="app-container">
    <el-form :inline="true" label-width="120px" :model="queryParam">
      <el-form-item label="月">
        <el-date-picker
          v-model="queryParam.month"
          style="width: 240px"
          type="month"
          :clearable=false
          value-format="yyyy-MM"
          :picker-options="{disabledDate(time){return time.getTime()>Date.now()}}"
        ></el-date-picker>
      </el-form-item>
      <el-radio-group v-model="categoryIndex" style="line-height: 45px">
        <el-radio :label="0">用电量（KWh）</el-radio>
        <el-radio :label="1">碳排放（Kg）</el-radio>
        <el-radio :label="2">用电成本（元）</el-radio>
      </el-radio-group>
<!--      <el-form-item>-->
<!--        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>-->
<!--      </el-form-item>-->
    </el-form>
    <el-row>
      <el-col :lg="1" :md="1" :xs="1">
        <el-button type="primary" icon="el-icon-arrow-left" style="height: 600px" @click="prevMonth"></el-button>
      </el-col>
      <el-col :lg="22" :md="22" :xs="22">
        <div id="report" style="width: 100%;height:600px;background-color: rgb(240, 242, 245);"></div>
      </el-col>
      <el-col :lg="1" :md="1" :xs="1">
        <el-button type="primary" icon="el-icon-arrow-right" style="height: 600px" @click="nextMonth" :disabled="isNextDisabled"></el-button>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import resize from "@/views/dashboard/mixins/resize"
import {calendarCarbonReport, calendarElecAmountReport, calendarEnergyReport} from "@/api/energy/report";
import dayjs from "dayjs";

export default {
  name: "CalendarEnergyReport",
  mixins: [resize],
  data(){
    return {
      queryParam:{
        month:dayjs().format('YYYY-MM'),
      },
      categoryIndex: 0,
      dataList:[[],[],[]],
      // chartData:[],
      chart:null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.setOptions();
      }
    },
    queryParam:{
      deep: true,
      handler() {
        this.handleQuery();
      }
    }
  },
  mounted(){
    this.$nextTick(() => {
      this.initChart();
      this.handleQuery();
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  computed:{
    dataRange(){
      let max = 0;
      let min = 999999999;
      for(let data of this.chartData){
        if (max < data[1]){
          max = data[1];
        }
        if (min > data[1]){
          min = data[1];
        }
      }
      return [min, max];
    },
    isNextDisabled(){
      return dayjs(this.queryParam.month).isSame(dayjs(), 'month');
    },
    chartData(){
      return this.dataList[this.categoryIndex];
    }
  },
  methods:{
    prevMonth(){
      this.queryParam.month = dayjs(this.queryParam.month).subtract(1,'month').format('YYYY-MM');
      // this.handleQuery();
    },
    nextMonth(){
      let nextMonth = dayjs(this.queryParam.month).add(1,'month');
      if (nextMonth.isAfter(dayjs(), 'month')){
        return;
      }
      this.queryParam.month = nextMonth.format('YYYY-MM');
      // this.handleQuery();
    },
    handleQuery(){
      calendarEnergyReport(this.queryParam).then(response=>{
        // this.chartData = response.data;
        this.$set(this.dataList, '0', response.data);
      });
      calendarElecAmountReport(this.queryParam).then(response=>{
        // this.chartData = response.data;
        this.$set(this.dataList, '2', response.data);
      });
      calendarCarbonReport(this.queryParam).then(response=>{
        // this.chartData = response.data;
        this.$set(this.dataList, '1', response.data);
      });
    },
    initChart() {
      this.chart = this.$echarts.init(document.getElementById('report'))
      this.setOptions()
    },
    setOptions() {
      this.chart.setOption({
        visualMap: {
          show: false,
          min: this.dataRange[0],
          max: this.dataRange[1],
          calculable: true,
          seriesIndex: [1],
          orient: 'horizontal',
          left: 'center',
          bottom: 20,
          inRange: {
            color: ['#e0ffff', '#006edd'],
            opacity: 0.3
          },
          controller: {
            inRange: {
              opacity: 0.5
            }
          }
        },
        calendar: [
          {
            left: 'center',
            top: 'middle',
            cellSize: 'auto',
            yearLabel: { show: false },
            orient: 'vertical',
            dayLabel: {
              show: true,
              firstDay: 1,
              nameMap: 'cn'
            },
            monthLabel: {
              show: false
            },
            range: this.queryParam.month,
            height:450,
            splitLine:{
              show:false,
            }
          }
        ],
        legend:{
          show: false
        },
        title:{
          text:this.title,
        },
        dataset:{
          source: this.chartData
        },
        series:[
          {
            type: 'scatter',
            coordinateSystem: 'calendar',
            symbolSize: 1,
            label: {
              show: true,
              formatter: params=> {
                let d = dayjs(params.value[0]).format('M月D日');
                return d + '\n\n'+params.value[1]+'\n\n';
              },
              color: '#000',
              fontSize:14,
            },
          },
          {
            type: 'heatmap',
            coordinateSystem: 'calendar'
          }
        ]
      })
    }
  }
}
</script>

<style scoped>

</style>
