<template>
  <div class="app-container">
    <el-form :inline="true" label-width="120px" :model="queryParam">
      <el-form-item label="日期">
        <el-date-picker
          v-model="dateRange"
          style="width: 280px"
          type="daterange"
          :clearable=false
          value-format="yyyy-MM-dd"
          :picker-options="{disabledDate(time){return time.getTime()>Date.now()+86400000}}"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="32">
      <el-col :sm="24" :md="24" :lg="20">
        <div id="report" style="width: 100%;height:600px;"></div>
      </el-col>
      <el-col :sm="24" :md="24" :lg="4">
        <el-table :data="tableData" border stripe>
          <el-table-column label="时间" align="center" prop="time" fixed="left">
            <template slot-scope="scope">
              {{parseTime(scope.row.time, '{y}-{m}-{d} {h}:{i}:{s}')}}
            </template>
          </el-table-column>
          <el-table-column label="MD峰值" align="center" prop="value" />
        </el-table>
      </el-col>
    </el-row>

  </div>
</template>

<script>
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import resize from "@/views/dashboard/mixins/resize"
import {queryFactoryMd} from "@/api/energy/report";
import dayjs from "dayjs";

export default {
  name: "FactoryMdReport",
  mixins: [resize],
  data(){
    return {
      dateRange:[dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      queryParam:{
        start:null,
        end:null,
      },
      chartData:[],
      chart:null,
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.setOptions()
      }
    }
  },
  computed:{
    tableData(){
      let list = [];
      if (this.chartData.length > 1){
        let value = 0;
        let lastValue = this.chartData[this.chartData.length-1][2];
        for (let i  = 1; i < this.chartData.length; i++){
          if (value < this.chartData[i][2]){
            value = this.chartData[i][2];
            list.push({time: this.chartData[i][0], value});
          }
          if (value === lastValue){
            break;
          }
        }
      }
      return list;
    }
  },
  mounted(){
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods:{
    handleQuery(){
      if (this.dateRange.length>1){
        this.queryParam.start = this.dateRange[0];
        this.queryParam.end = this.dateRange[1];
        queryFactoryMd(this.queryParam).then(response=>{
          this.chartData = response.data;
        })
      }

    },
    initChart() {
      this.chart = this.$echarts.init(document.getElementById('report'))
      this.setOptions()
    },
    setOptions() {
      console.log('abc');
      // let series = [];
      // let count = dataSet.length>1?dataSet[0].length-1:0;
      // for (let i = 0; i < count; i++){
      //   let ser = {type:'bar',
      //     label: {
      //       show: true,
      //       position: 'right',
      //       valueAnimation: true,
      //       formatter: (params)=>{
      //         return params.value[params.encode.x[0]].toFixed(2);
      //       }
      //     }
      //   }
      //   if (this.stack){
      //     ser.stack='total';
      //   }
      //   series.push(ser);
      // }
      this.chart.setOption({
        xAxis: {
          type:'time',
          // boundaryGap: false,
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: 50,
          right: 20,
          bottom: 50,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        yAxis: [
          {
            type:'value',
            axisLabel: {
              formatter: '{value} KW'
            }
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            // end: 20
          },
          {
            start: 0,
            // end: 20
          }
        ],
        series:[
          {
            name: 'MD值',
            type: 'line',
            areaStyle: {},
            encode: {
              x:0,
              y:1
            },
            showSymbol: false,
          },
          {
            name: 'MD峰值',
            type: 'line',
            connectNulls: true,
            step: 'end',
            encode: {
              x:0,
              y:2
            },
            showSymbol: false,
          }
        ],
        legend:{
          show: false,
        },
        dataset:{
          source: this.chartData,
        },
        title:{
          text:this.title,
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
