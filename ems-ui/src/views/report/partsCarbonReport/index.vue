<template>
  <div class="app-container">
    <el-form :inline="true" v-show="showSearch" label-width="120px" :rules="rules" ref="query" :model="queryParam">
      <el-form-item label="日期">
        <el-date-picker
          v-model="dateRange"
          style="width: 280px"
          type="daterange"
          :clearable=false
          value-format="yyyy-MM-dd"
          :picker-options="{disabledDate(time){return time.getTime()>Date.now()+86400000}}"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="产品" prop="partIds">
        <treeselect v-model="queryParam.partIds" :options="partOptions" :normalizer="normalizer" placeholder="请选择产品" searchable style="width: 400px" :multiple="true" :limit="5" :flat="true"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <common-bar-chart :chart-data="dataList" height="600px"/>
  </div>

</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {listPart} from "@/api/energy/part";
import {queryPartsEquivalentCarbon, queryPcfReport} from "@/api/energy/report";
import CommonBarChart from "@/views/dashboard/CommonBarChart";
import dayjs from "dayjs";

export default {
  name: "PartsCarbonReport",
  components: {
    Treeselect,
    CommonBarChart,
  },
  data() {
    return {
      showSearch: true,
      loading: false,
      dateRange:[dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      dataList: [],
      partOptions:[],
      queryParam:{
        partIds:null,
        start:null,
        end:null,
      },
      rules: {
        partIds: [
          { required: true, message: "至少选择一个产品", trigger: "blur",type:"array" } ,
          {
            message: '至少选择一个产品',
            type: 'array',
            min: 1,
          }
        ],
      }
    }
  },
  created(){
    this.queryParts();
  },
  methods:{
    handleQuery(){
      this.$refs["query"].validate(valid=>{
        if (valid){
          if (this.dateRange.length > 1 && null != this.dateRange[0] && null != this.dateRange[1]){
            this.queryParam.start = this.dateRange[0];
            this.queryParam.end = this.dateRange[1];
            queryPartsEquivalentCarbon(this.queryParam).then(response=>{
              this.dataList = response.data;
            });
          }
        }
      });


    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.partId,
        label: node.partName,
        children: node.children
      };
    },
    queryParts(){
      listPart().then(response => {
        this.partOptions = this.handleTree(response.data, "partId", "parentId");
      });
    },
  }

}
</script>

<style scoped>

</style>
