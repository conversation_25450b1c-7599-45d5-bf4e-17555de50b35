<template>
  <div class="dashboard-editor-container">

    <panel-group @handleSetLineChartData="handleSetLineChartData" :amount="amountData" :carbon="carbonData" :electricity="elecData"/>
    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <common-bar-chart :chart-data="chartData" :title="title" height="450px"/>
    </el-row>
	</div>
</template>

<script>
import PanelGroup from '@/views/home/<USER>';
import CommonBarChart from "@/views/dashboard/CommonBarChart";
import dayjs from 'dayjs';
import {
  oneMonthCarbonCateogryReport,
  oneMonthElecAmountCateogryReport,
  oneMonthEnergyCateogryReport, queryOneMonthTotalCarbon, queryOneMonthTotalElecAmount, queryOneMonthTotalEnergy
} from "@/api/energy/report";
export default {
  name: 'home',
  components: {PanelGroup, CommonBarChart},
  data(){
    return {
      elecDataList:[],
      carbonDataList: [],
      amountDataList: [],
      elecData:0,
      carbonData:0,
      amountData:0,
      chartData:[],
      title:"本月分区用电量（KWh）",
    }
  },
  created() {
    this.queryData();
  },
  methods:{
    handleSetLineChartData(category){
      switch (category){
        case 'electricity':
          this.chartData = this.elecDataList;
          this.title = "本月分区用电量（KWh）";
          break;
        case 'carbon':
          this.chartData = this.carbonDataList;
          this.title = "本月分区碳排放（Kg）";
          break;
        case 'amount':
          this.chartData = this.amountDataList;
          this.title = "本月分区电费（元）";
          break;
      }
    },
    queryData(){
      let query = {month: dayjs().format('YYYY-MM')};
      queryOneMonthTotalEnergy(query).then(response=>{
        this.elecData = response.data;
      });
      queryOneMonthTotalCarbon(query).then(response=>{
        this.carbonData = response.data;
      });
      queryOneMonthTotalElecAmount(query).then(response=>{
        this.amountData = response.data;
      });
      oneMonthEnergyCateogryReport(query).then(response=>{
        this.elecDataList = response.data;
        this.chartData = response.data;
      });
      oneMonthCarbonCateogryReport(query).then(response=>{
        this.carbonDataList = response.data;
      });
      oneMonthElecAmountCateogryReport(query).then(response=>{
        this.amountDataList = response.data;
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
