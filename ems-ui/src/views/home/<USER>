<template>
	<div class="home">
		<div class="conOne">
			<div class="conTwo">
				<div class="conTwoL">
					<p style="font-size: 12px;">市电</p>

					<div style="width: 100%;height:80px;"></div>
					<p style="font-size: 12px;">当月累计耗电:17.37万KWh</p>
					<p style="font-size: 12px;">上月累计耗电:22.85万KWh</p>
				</div>
				<div class="conTwoR">
					<p style="font-size: 16px;">负荷率</p>
					<p style="font-size: 30px;">24.2%</p>
					<p style="font-size: 16px;">容量</p>
					<p style="font-size: 16px;">1000KVA</p>
				</div>
			</div>
			<div class="conTwo">
				<div class="conTwoL">
					<p style="font-size: 12px;">光伏</p>

					<div style="width: 100%;height:80px;"></div>
					<p style="font-size: 12px;">当月累计发电:1.77万KWh</p>
					<p style="font-size: 12px;">上月累计发电:1.61万KWh</p>
				</div>
				<div class="conTwoR">
					<p style="font-size: 16px;">发电效率</p>
					<p style="font-size: 30px;">2.2%</p>
					<p style="font-size: 16px;">容量</p>
					<p style="font-size: 16px;">1500KVA</p>
				</div>
			</div>
		</div>
		<div class="conOne">
			<div id="main" style="width: 100%;height:400px;"></div>
		</div>
		<div class="conOne">
			<div id="main1" style="width: 100%;height:400px;"></div>
		</div>
	</div>
</template>

<script>
export default {
  name: 'home',
  methods:{
	myEcharts(){
		  // 基于准备好的dom，初始化echarts实例
		  var myChart = this.$echarts.init(document.getElementById('main'));

		  // 指定图表的配置项和数据
		  var option = {
			  title: {
				  text: '万元产值能耗（***工厂）'
			  },
			  tooltip: {},
			  legend: {
			  },
			  dataset: {
			      source: [
			        ['product', '去年实际', '今年实际'],
			        ['1月', 43.3, 85.8],
			        ['2月', 46.3, 88.8],
			        ['3月', 43.3, 89.8],
			        ['4月', 26.3, 58.8],
			        ['5月', 56.3, 67.8],
			        ['6月', 89.3, 99.8],
			        ['7月', 45.3, 55.8],
			        ['8月', 37.3, 46.8],
			        ['8月', 39.3, 89.8],
			        ['9月', 26.3, 85.8],
			        ['10月', 28.3, 86.8],
			        ['11月', 38.3, 76.8],
			        ['12月', 26.6, 65.8],
			      ]
			    },
			    xAxis: { type: 'category' },
			    yAxis: {},
			    // Declare several bar series, each will be mapped
			    // to a column of dataset.source by default.
			    series: [{ type: 'bar' }, { type: 'bar' }]
		  };

		  // 使用刚指定的配置项和数据显示图表。
		  myChart.setOption(option);
	},
	banYuan(){
		let xAxisData = [];
		let data1 = [];
		let data2 = [];
		let data3 = [];
		let data4 = [];
		for (let i = 0; i < 10; i++) {
		  xAxisData.push(i+1 + "月");
		  data1.push(+(Math.random() * 2).toFixed(2));
		  data2.push(+(Math.random() * 5).toFixed(2));
		  data3.push(+(Math.random() + 0.3).toFixed(2));
		  data4.push(+Math.random().toFixed(2));
		}
		var emphasisStyle = {
		  itemStyle: {
		    shadowBlur: 10,
		    shadowColor: 'rgba(0,0,0,0.3)'
		  }
		};
		var myChart = this.$echarts.init(document.getElementById('main1'));

		var option = {
			title: {
			  text: '生产设备能耗'
			},
			tooltip: {},
			legend: {
			    data: ['自动', '手自动', '手动', '纯手动'],
			    left: '10%'
			  },
			  toolbox: {
			    feature: {
			      magicType: {
			        type: ['stack']
			      },
			      dataView: {}
			    }
			  },
			  xAxis: {
			    data: xAxisData,
			    name: 'X Axis',
			    axisLine: { onZero: true },
			    splitLine: { show: false },
			    splitArea: { show: false }
			  },
			  yAxis: {
			  },
			  grid: {
			    bottom: 100
			  },
			  series: [
			    {
			      name: '自动',
			      type: 'bar',
			      stack: 'one',
			      emphasis: emphasisStyle,
			      data: data1
			    },
			    {
			      name: '手自动',
			      type: 'bar',
			      stack: 'one',
			      emphasis: emphasisStyle,
			      data: data2
			    },
			    {
			      name: '手动',
			      type: 'bar',
			      stack: 'one',
			      emphasis: emphasisStyle,
			      data: data3
			    },
			    {
			      name: '纯手动',
			      type: 'bar',
			      stack: 'one',
			      emphasis: emphasisStyle,
			      data: data4
			    }
			  ]
		};

		// 使用刚指定的配置项和数据显示图表。
		myChart.setOption(option);
	},
  },
  mounted() {
	this.myEcharts();
	this.banYuan();
  }
}
</script>

<style scoped>
	.conOne{
		width: 98%;
		margin: 20px auto;
		display: flex;
	}
	.conTwo{
		width: 48%;
		height: 200px;
		margin: 20px 1%;
		background-color: #243f5c;
		display: block;
		float: left;
		color: white;
	}
	.conTwoL{
		width: 70%;
		height: 100%;
		float: left;
	}
	.conTwoR{
		width: 30%;
		height: 100%;
		float: right;
	}
</style>
