<template>
  <div>
      <div slot="header" class="clearfix">
        <!-- <span>计量器具关系维护</span> -->
         <!-- <h3>计量器具关系维护</h3> -->
      </div>
      <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline1" label-width="250px" size="max">
          <!-- <el-form-item label="维护类别">
              <el-select v-model="formInline.keepType" placeholder="类别"  >
                <el-option  v-for="item in keepTypeList" :key="item.keepType" :label="item.label" :value="item.keepType"></el-option>
              </el-select>
          </el-form-item> -->
          <el-form-item label="自定义名称">
           <el-input v-model="formInline.custName"  placeholder="自定义名称" ></el-input>
          </el-form-item>
           <el-form-item label="企业:">
             <treeselect v-model="formInline.factoryFirmId" :options="firmList" :normalizer="normalizer"  :appendToBody="true" placeholder="企业" class="company_tree"/>
            </el-form-item>
        </el-form>
         <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="250px" size="max">
          <el-form-item label="能源成本中心">
            <treeselect v-model="formInline.costCenterId"  :options="costCenterList" :normalizer="normalizer"  placeholder="能源成本中心" class="company_tree" @input="deleinput"/>
          </el-form-item>
          <el-form-item label="能源介质">
             <el-select v-model="formInline.energyId" placeholder="能源介质">
            <el-option  v-for="item in energyMedList" :key="item.energyId" :label="item.energyName" :value="item.energyId"></el-option>
          </el-select>
          </el-form-item>
        </el-form>
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="200px" size="max">
          <el-form-item label="关系">
            <el-button type="primary"  v-for="(item, i) in symbols" :key="i" @click.stop="symbolAdd(item)">{{item}}</el-button>
            <el-button type="danger" @click="add">+电表</el-button>
          </el-form-item>
        </el-form>
         <div class="utensilBtn">
           <el-button v-for="(item, index) in list" :key="index" @click="addRecordList(item.id)">{{item.name}}</el-button>
          </div>
        <!-- 点击按钮添加出来的 -->
         <div class="utensil_production">
           <div v-for="(item, index) in formInline.recordList" :key="index" class="selefu">
             <!-- @click.native="seleadd()" -->
              <treeselect  v-model="item.data"  :value="item.id" :options="addList" :normalizer="normalizer" :appendToBody="true"  placeholder="请选择" class="company_tree" @input="seleadd(item.data,index)" />
              <!-- <span class="selectFuhao">{{item.symbol}}</span> -->
              <b class="selectFuhao">{{item.symbol}}</b>
           </div>
          </div>
         <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="200px" label-height="100px" size="max">
          <el-form-item label="说明" class="sminput">
            <el-input  v-model="formInline.description"  placeholder="说明" class="sminput"/>
          </el-form-item>
        </el-form>
      </div>
     <!-- <div class="explain">
        <span class="explain_sm">说明:</span>
        <input type="text" class="explain_input" v-model="formInline.description">
     </div> -->
       <!-- <el-button type="primary" class="message_btn"  @click="onSubmit">提交</el-button>
        <el-button v-on:click="backHistory">取消</el-button> -->
        <div style="text-align: center" class="message_btn">
         <el-button type="primary"   @click="onSubmit">提交</el-button>
        <el-button  v-on:click="backHistory">取消</el-button>
    </div>
  </div>
</template>

<script>
import {getAddList,
        get_energy_med,
        getMaintainType,
        getCcostCenter,
        submitData,
        datacome,
        costChange,
        getfirmList,
        getDepartment} from "@/api/enterprise/utensil"
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
export default {
  components: { Treeselect,IconSelect },
    data() {
      return {
        formInline: {
          //能源介质
          energyId:"",
          //自定义名称
          custName:"",
          //能源成本中心
          costCenterId:null,
          //维护类别
          // keepType:null,
          //记录数组
          numberList:[],
          //关系
          relationExpression:"",
          //说明
          description:"",
          //记录数组
          recordList:[],
          relatId:null,
          //企业
          factoryFirmId:null
        },
        //渲染按钮数组
        symbols:["-","+","-","*","/",".","(",")","0","1","2","3","4","5","6","7","8","9","sum"],
        addList: [ ],
        item:[],
        //能源介质数据
        energyMedList:[],
        //维护类别数据
        MaintainTypeList:[],

        // keepTypeList:[{label:"成本中心",keepType:1},{label:"自定义",keepType:2}],
        //能源成本中心树
        costCenterList:[],
        //验证数组
        numberarr:["0","1","2","3","4","5","6","7","8","9"],
        stringarr:["-","+","-","*","/",".","(",")","sum"],
        //企业树
        firmList:[],
        //开关(决定维护类别)
        // flag:false,
        //输入框开关
        // disabled:false,
        // distree:false,
        list:[]


      }
    },
    created(){
      // console.log(this.$route.query.relatId)
      this.$data.formInline.relatId=this.$route.query.relatId
      if(this.$route.query.relatId){
        //获取下拉数据
        this.getdata()
        //获取回显数据
        this.getDatacome(this.$route.query.relatId)
      }
      //获取下拉数据
      this.getdata()
    },
    methods: {
      //点击提交按钮
      onSubmit() {
        // console.log(this.$data.formInline);
        if(this.$route.query.relatId){
          //编辑
          console.log(this.$data.formInline)
          costChange(this.$data.formInline).then(response=>{
             if(response.code===200){
              this.$router.push({path:'/enterprise/management'})
              }
          })
        }else{
          //添加
          console.log(this.$data.formInline)
          submitData(this.$data.formInline).then(response=>{
            if(response.code===200){
              this.$router.push({path:'/enterprise/management'})
              }
          })
        }
      },
      //点击添加按钮
      symbolAdd(single) {
        // console.log(single)
        //判断之前的记录是否存在
        if(this.$data.formInline.recordList.length){
          //判断这个数组真最后是否有符号
           if(this.$data.formInline.recordList[this.$data.formInline.recordList.length-1].symbol){

              this.$data.formInline.recordList[this.$data.formInline.recordList.length-1].symbol+=single
              let result = this.$data.numberarr.find(v => v === single);
              console.log(result)
              if(result){
                this.$data.formInline.relationExpression+="%d"
                this.$data.formInline.numberList.push(single)
                }else{
                this.$data.formInline.relationExpression+=single
              }
           }else{

              this.$set(this.$data.formInline.recordList[this.$data.formInline.recordList.length-1], 'symbol', single)
               let result = this.$data.numberarr.find(v => v === single);
                if(result){
                  console.log(single)
                  this.$data.formInline.relationExpression+="%d"
                  this.$data.formInline.numberList.push(single)
                  console.log(this.$data.formInline.numberList)
                }else{
                  console.log(result,single)
                  this.$data.formInline.relationExpression+=single
                  console.log(this.$data.formInline.relationExpression)
              }
           }
        }else{
          return this.$message.error('还没有选择电表呢');
        }

      //this.$data.formInline.relationExpression+=single
      //this.$data.formInline.relationExpression+="%d"
        // console.log(this.$data.formInline.relationExpression)
        // console.log(this.$data.formInline.numberList);
      },
      add() {
        // console.log(this.$data.formInline)
        if(this.$data.formInline.recordList.length==0){
          this.$data.formInline.recordList.push({data:null,symbol:null});
        }else{
          if(this.$data.formInline.recordList[this.$data.formInline.recordList.length-1].symbol){
            this.$data.formInline.recordList.push({data:null,symbol:null});
          }else{
            return this.$message.error('请先选择关系再追加电表');
          }
        }

      },
      //能源成本中心选择
      deleinput(){
        // console.log(this.$data.flag)
      //  if(this.$data.flag){
        console.log(this.$data.formInline.costCenterId)
          getDepartment(this.$data.formInline.costCenterId).then(response=>{
           console.log(response)
          this.$data.list=response.data
          //  this.$data.formInline.numberList=response.data.numberList?response.data.numberList:[]
          //   this.$data.formInline.relationExpression=response.data.relationExpression?response.data.relationExpression:null
          //      let lists=response.data.recordList.replace("/")
          //   let  list=JSON.parse(lists);
          //   this.$data.formInline.recordList=response.data.recordList?list:[]
        })
      // }
      },
      addRecordList(id){
        console.log(id)

        if(this.$data.formInline.recordList.length==0){
          this.$data.formInline.recordList.push({data:id,symbol:null});
          this.$data.formInline.relationExpression+=id
        }else{
          if(this.$data.formInline.recordList[this.$data.formInline.recordList.length-1].symbol){
            this.$data.formInline.recordList.push({data:id,symbol:null});
            this.$data.formInline.relationExpression+=id
          }else{
            return this.$message.error('请先选择关系再追加电表');
          }
        }
      },
      seleadd(item,index){
        this.$data.formInline.relationExpression=""
        this.$data.formInline.recordList[index].data=item
        this.$data.formInline.recordList.forEach((item,index)=>{
        let  recordnumber=String(item.data)+String(item.symbol)
        var reg=new RegExp("null","g"); //创建正则RegExp对象
        // console.log(recordnumber.split("unll")[0].replace(reg,""))
          this.$data.formInline.relationExpression+=recordnumber.split("undefined")[0].replace(reg,"")
        })
        console.log(this.$data.formInline.relationExpression)
        //console.log(recordnumber)
        // console.log(index)
       // console.log(item)
       // console.log(this.$data.formInline.relationExpression)
      //  this.$data.stringarr.map((item,index)=>{
      //    if(this.$data.formInline.relationExpression.lastIndexOf(item)!==-1){
      //      var recordnumber=this.$data.formInline.relationExpression.lastIndexOf(item)
      //      this.$data.formInline.relationExpression=this.$data.formInline.relationExpression.slice(0,recordnumber+1)
      //    }
      //  })
      //  this.$data.formInline.relationExpression+=item
      },
      //获取回显数据
      getDatacome(relatId){
        // console.log(relatId)
          datacome(relatId).then(response=>{
            console.log(response)
            this.$data.formInline.energyId=response.data.energyId?response.data.energyId:null;
            this.$data.formInline.custName=response.data.custName?response.data.custName:null
            this.$data.formInline.MaintainType=response.data.MaintainType?response.data.MaintainType:null
           // this.$data.formInline.keepType=response.data.keepType?response.data.keepType:null
            this.$data.formInline.numberList=response.data.numberList?response.data.numberList:[]
            this.$data.formInline.relationExpression=response.data.relationExpression?response.data.relationExpression:null
            this.$data.formInline.description=response.data.description?response.data.description:null
            this.$data.formInline.costCenterId=response.data.costCenterId?response.data.costCenterId:null
            let lists=response.data.recordList.replace("/")
            let  list=JSON.parse(lists);
            this.$data.formInline.recordList=response.data.recordList?list:[]
             this.$data.formInline.factoryFirmId=response.data.factoryFirmId?response.data.factoryFirmId:null
          })
      },
      //获取下拉数据
      getdata(){
            //能源成本中心数据获取
        getCcostCenter().then(response=>{
          //console.log(response)
          this.$data.costCenterList=response.data
        })
        getAddList().then(response=>{
          //console.log(response)
          this.$data.addList=response.data
        })
        get_energy_med().then(response=>{
          // console.log(response)
          this.$data.energyMedList=response.data
        })
        getMaintainType().then(response=>{
          // console.log(response)
          // this.$data.MaintainTypeList=response.data
        })
         //获取企业树
        getfirmList().then(response=>{
          this.$data.firmList=response.data
          // console.log(response)
        })
      },
       /** 转换部门数据结构 */
      normalizer(node) {
        if (node.children && !node.children.length) {
          delete node.children;
        }
        return {
          id: node.id,
          label: node.label,
          children: node.children
        };
      },
       backHistory(){
        this.$router.go(-1);//返回上一层
      },
      //类型选择
      // selectChanged() {
      // console.log(this.$data.formInline.keepType)
      // if(this.$data.formInline.keepType==1){
      //    console.log("成本中心")
      //    this.$data.distree=false
      //     this.$data.flag=true
      //     this.$data.disabled=true
      //     this.$data.formInline.custName=""

      // }else{
      //   console.log("自定义")
      //   this.$data.distree=true
      //   this.$data.flag=false
      //   this.$data.formInline.costCenterId=null
      //    this.$data.disabled=false
      // }
     // }

    }
};

</script>

<style scoped>
.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

/* .clearfix {
    width: 100%;
    height: 40px;
    line-height: 60px;
    margin-left: 10px;
    border-bottom: 3px solid #ccc;
  } */
.demo-form-inline1{
  margin-top: 10px;
}
.box-card {
  width: 100%;
  height: 100%;
}
.utensil_production{
  /* border: 1px solid #ccc; */
  width: 75%;
  margin: 0 10%;
  height: 120px;
  display: flex;
  flex-wrap: wrap;
  /* overflow: hidden; */
}
.utensilBtn{
  width: 75%;
  height: 50px;
  /* border: 1px solid springgreen; */
  border-bottom: 1px  solid #000;
  margin: 0 10%;
  margin-bottom: 10px;
}
.selefu{
  display: flex;
}
.selectFuhao{
  height: 36px;
  text-align: center;
  line-height: 36px;
}
.company_tree{
    width: 220px;
    height: 50px;
}
.message_btn{
  width: 100px;
  margin-left: 40%;
  transform: translateX(-50%);
  margin-top: 20px;
}
.explain{
    width: 72%;
    height: 100px;
    margin: 0 8%;
    display:flex;
}
.explain_sm{
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.explain_input{
  flex: 1;
}
.company_tree{
    width: 220px;
  }
  .el-input__inner {
    width: 220px;
  }
  .sminput > .el-input__inner {
    width: 1200px;
    height: 100px;
  }
  .sminput >.el-form-item__label{
    height: 100px;
    line-height: 100px;
  }
  .message_btn{
  width: 100px;
  display: flex;

}
</style>
