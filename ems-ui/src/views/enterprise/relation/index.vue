<template>
  <div class="management_box">
    <!-- <div class="management_nav">能源成本中心列表</div> -->
    <div class="management_search">
      <!-- 搜索栏表单 -->
      <el-card class="box-card">
        <div class="ArticleTop">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="能源成本中心">
              <treeselect
                v-model="from.costCenterId"
                :options="costCenterList"
                :normalizer="normalizer"
                :appendToBody="true"
                placeholder="能源成本中心"
                class="company_tree"
              />
            </el-form-item>
            <el-form-item>
               <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="TabSearch()"
                v-hasPermi="['system:product:export']"
                >搜索</el-button>
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="costAdd()"
                v-hasPermi="['system:product:add']"
                >新增</el-button
              >
              <el-button
                type="warning"
                plain
                icon="el-icon-download"
                size="mini"
               @click="exportfile()"
                v-hasPermi="['system:product:export']"
                >导出</el-button
              >

              <!-- <el-button type="primary" @click="costAdd()">添加</el-button>
              <el-button type="success" @click="TabSearch()">搜索</el-button>
              <el-button type="info" @click="exportfile()">导出</el-button> -->
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
    <div class="management_table">
      <el-table
        :data="TabList"
        style="width: 100%"
        border
        highlight-current-row
      >
        <el-table-column label="计量器具编号" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.meaCode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计量对象" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.meaName }}</span>
          </template>
        </el-table-column>
        <el-table-column label=" 能源成本中心名称" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{
              scope.row.costCenterName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="能源介质" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.energyName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="安装位置" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.installAddr }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <!-- <el-button
              size="mini"
              @click="handleEdit(scope.$index, scope.row.meaId)"
              >编辑</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row.meaId)"
              >删除</el-button
            > -->
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
             @click="handleEdit(scope.$index, scope.row.meaId)"
              v-hasPermi="['system:product:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
                @click="handleDelete(scope.$index, scope.row.meaId)"
              v-hasPermi="['system:product:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import {
  getRelationList,
  getCcostCenter,
  getNnergyMed,
  TabSearch,
  energyDelete,
  exportfile,
} from "@/api/enterprise/relation";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
export default {
  components: { Treeselect, IconSelect },
  data() {
    return {
      from: {
        costCenterId: null,
        // energyId: "",
      },
      TabList: [],
      treeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobName: undefined,
        jobGroup: undefined,
        status: undefined,
      },
      //能源成本中心
      costCenterList: [],
      //能源介质数据
      // energyMedList:[]
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //编辑
    handleEdit(index, meaId) {
      this.$router.push({
        path: "/enterprise/message",
        query: { meaId: meaId },
      });
      // console.log(index, row);
    },
    //删除
    handleDelete(index, meaId) {
      energyDelete(meaId).then((response) => {
        // console.log(response)
        if (response.code === 200) {
          this.getList();
        }
      });
      // console.log(index, row);
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    handleNodeClick(data) {
      console.log(data);
    },
    //获取表格数据
    getList() {
      getRelationList().then((response) => {
        console.log(response);
        this.$data.TabList = response.rows;
      });
      //获取能源成本中心树
      getCcostCenter().then((response) => {
        this.$data.costCenterList = response.data;
        // console.log(response)
      });
      //获取能源介质
      // getNnergyMed().then(response=>{
      //   // console.log(response)
      //   this.$data.energyMedList=response.data
      // })
    },
    //搜索
    TabSearch() {
      TabSearch(this.$data.from).then((response) => {
        this.$data.TabList = response.rows;
        this.$data.from = {};
      });
    },
    //导出
    exportfile() {
      const queryParams = this.queryParams;
      this.$modal
        .confirm("是否确认导出数据项？")
        .then(() => {
          this.exportLoading = true;
          return exportfile(queryParams);
        })
        .then((response) => {
          this.$download.name(response.msg);
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    //添加
    costAdd() {
      this.$router.push({ path: "/enterprise/message" });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      };
    },
  },
  //过滤器
  filters: {
    stateType(state) {
      if (state === "0") {
        return "不合格";
      } else {
        return "合格";
      }
    },
  },
};
</script>

<style scoped>
.company_tree {
  width: 220px;
}
</style>
