<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备号" prop="deviceNum">
        <el-input
          v-model="queryParams.deviceNum"
          placeholder="请输入设备号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
          v-model="queryParams.deviceName"
          placeholder="请输入设备名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计量器具" prop="meaId">
        <!-- <el-input
          v-model="queryParams.meaId"
          placeholder="请输入计量器具"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        /> -->
         <el-select v-model="queryParams.meaId" placeholder="请输入计量器具" clearable size="small">
            <el-option  v-for="(item,index) in measureList" :key="index" :label="item.meaCode" :value="item.meaId"></el-option>
          </el-select>
      </el-form-item>
      <!-- <el-form-item label="设备编码" prop="equCode">
        <el-input
          v-model="queryParams.equCode"
          placeholder="请输入设备编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:device:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:device:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:device:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:device:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="工厂设备id" align="center" prop="equId" /> -->
      <el-table-column label="设备号" align="center" prop="deviceNum" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="计量器具" align="center" prop="meaCode" />
      <!-- <el-table-column label="设备编码" align="center" prop="equCode" /> -->
       <el-table-column label="能源介质" align="center" prop="energyName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:device:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:device:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改工厂设备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备号" prop="deviceNum">
          <el-input v-model="form.deviceNum" placeholder="请输入设备号" />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="计量器具" prop="meaId">
          <!-- <el-input v-model="form.meaId" placeholder="请输入计量器具" /> -->
            <el-select v-model="form.meaId" placeholder="请输入计量器具" class="energy">
            <el-option  v-for="(item,index) in measureList" :key="index" :label="item.meaCode" :value="item.meaId"></el-option>
          </el-select>
        </el-form-item>
         <el-form-item label="能源介质" prop="energyId">
          <!-- <el-input v-model="form.energyId" placeholder="请输入能源介质" /> -->
            <el-select v-model="form.energyId" placeholder="请输入能源介质" class="energy">
            <el-option  v-for="item in energyList" :key="item.energyId" :label="item.energyName" :value="item.energyId"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="设备编码" prop="equCode">
          <el-input v-model="form.equCode" placeholder="请输入设备编码" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {getDevice, listDevice,addDevice,updateDevice,delDevice,exportfile,getEnergy,getmeasureList} from "@/api/enterprise/equipment";

  export default {
  name: "Device",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工厂设备表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceNum: null,
        deviceName: null,
        meaId: null,
        // equCode: null,
        energyId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      //能源介质列表
      energyList:[],
      //计量器具列表
      measureList:[]
    };
  },
  created() {
    // console.log(this.$modal)
    this.getList();
  },
  methods: {
    /** 查询工厂设备列表 */
    getList() {
      this.loading = true;
      listDevice(this.queryParams).then(response => {
        console.log(response)
        this.deviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      getEnergy().then(response=>{
        // console.log(response)
        this.$data.energyList=response.data
      })
      getmeasureList().then(response=>{
        // console.log(response)
        this.$data.measureList=response.rows
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        equId: null,
        deviceNum: null,
        deviceName: null,
        meaId: null,
        // equCode: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.equId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工厂设备";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const equId = row.equId || this.ids
      getDevice(equId).then(response => {
        console.log(response)
        this.form = response.data;
        this.open = true;
        this.title = "修改工厂设备";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.equId != null) {
            // console.log(this.form)
            updateDevice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            // console.log(this.form)
            addDevice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const equIds = row.equId || this.ids;
      this.$modal.confirm('是否确认删除工厂设备编号为"' + equIds + '"的数据项？').then(function() {
        return delDevice(equIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
     const queryParams = this.queryParams;
      this.$modal
        .confirm("是否确认导出所有定时任务数据项？")
        .then(() => {
          this.exportLoading = true;
          return exportfile(queryParams);
        })
        .then((response) => {
          this.$download.name(response.msg);
          this.exportLoading = false;
        })
        .catch(() => {});
    }
  }
};
</script>
<style scoped>
  .energy{
    width: 380px;
  }
</style>
