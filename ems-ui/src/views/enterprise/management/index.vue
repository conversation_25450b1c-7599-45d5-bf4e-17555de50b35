<template>
  <div class="management_box">
    <!-- <div class="management_nav">计算器具列表</div> -->
    <div class="management_search">
      <!-- 搜索栏表单 -->
      <el-card class="box-card">
        <div class="ArticleTop">
          <el-form :inline="true" class="demo-form-inline">
            <!-- <el-form-item label="企业:">
              <treeselect
                v-model="from.keepType"
                :options="firmList"
                :normalizer="normalizer"
                :appendToBody="true"
                placeholder="企业"
                class="company_tree"
              />
            </el-form-item> -->
            <el-form-item label="成本中心">
              <treeselect
                v-model="from.costCenterId"
                :options="costCenterList"
                :appendToBody="true"
                :normalizer="normalizer"
                placeholder="能源成本中心"
                class="company_tree"
              />
            </el-form-item>
            <el-form-item label="能源介质">
              <el-select v-model="from.energyId" placeholder="请选择下拉选择" clearable :style="{width: '100%'}">
                <el-option
                  v-for="item in energyMedList"
                  :key="item.id"
                  :label="item.energyName"
                  :value="item.energyId"
                  >{{ item.energyName }}</el-option
                >
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="TabSearch()"
                v-hasPermi="['system:product:export']">搜索</el-button>
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="managementAdd()"
                v-hasPermi="['system:product:add']">新增</el-button>
              <el-button
                type="warning"
                plain
                icon="el-icon-download"
                size="mini"
                @click="exportfile()"
                v-hasPermi="['system:product:export']">导出</el-button>

              <!-- <el-button type="primary"  v-hasPermi="['system:menu:edit']" @click="managementAdd()"  >添加</el-button>
              <el-button type="info"  v-hasPermi="['system:menu:edit']"  @click="exportfile()">导出</el-button>
              <el-button type="success"  @click="TabSearch()" >搜索</el-button> -->
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
    <div class="management_table">
      <el-table
        :data="TabList"
        style="width: 100%"
        border
        highlight-current-row
      >
        <el-table-column label="编号" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.relatCode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="企业" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.deptName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="成本中心" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.centerName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="自定义名称" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.custName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="能源介质" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.energyName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="关系" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{
              scope.row.relationExpression
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="说明" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.description }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <!-- <el-button size="mini" @click="handleEdit(scope.$index, scope.row.relatId)"
             v-hasPermi="['system:menu:edit']"
              >编辑</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row.relatId)"
               v-hasPermi="['system:menu:edit']"
              >删除</el-button
            > -->
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.$index, scope.row.relatId)"
              v-hasPermi="['system:product:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.$index, scope.row.relatId)"
              v-hasPermi="['system:product:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import {
  get_Tablist,
  TabSearch,
  exportfile,
  energyDelete,
  get_energy_med,
  getCcostCenter,
  getfirmList,
} from "@/api/enterprise/management";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
export default {
  components: { Treeselect, IconSelect },
  data() {
    return {
      TabList: [],
      from: {
        //企业
        factoryId: "",
        //能源中心
        costCenterId: null,
        //能源介质
        energyId: "",
        //企业
        // keepType: null,
      },
      //能源介质数据
      energyMedList: [],
      //能源成本中心树
      costCenterList: [],
      //企业树
      firmList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //编辑
    handleEdit(index, relatId) {
      this.$router.push({
        path: "/enterprise/utensil",
        query: { relatId: relatId },
      });
    },
    handleDelete(index, relatId) {
      energyDelete(relatId).then((response) => {
        // console.log(response)
        if (response.code === 200) {
          this.getList();
        }
      });
    },
    //获取表格数据
    getList() {
      get_Tablist().then((response) => {
        console.log(response);
        this.$data.TabList = response.rows;
      });
      //获取能源介质
      get_energy_med().then((response) => {
        // console.log(response)
        this.$data.energyMedList = response.data;
      });
      //获取能源成本中心
      getCcostCenter().then((response) => {
        // console.log(response)
        this.$data.costCenterList = response.data;
      });
      //获取企业树
      // getfirmList().then((response) => {
      //   this.$data.firmList = response.data;
      //   // console.log(response)
      // });
    },
    //搜索
    TabSearch() {
      console.log(this.$data.from);
      TabSearch(this.$data.from).then((response) => {
        // console.log(response)
        this.$data.TabList = response.rows;
      });
      console.log(this.$data.from);
    },
    //新增
    managementAdd() {
      ///enterprise/utensil
      this.$router.push({ path: "/enterprise/utensil" });
      console.log(2222);
    },
    //导出
    exportfile() {
      const queryParams = this.queryParams;
      this.$modal
        .confirm("是否确认导出所有定时任务数据项？")
        .then(() => {
          this.exportLoading = true;
          return exportfile(queryParams);
        })
        .then((response) => {
          this.$download.name(response.msg);
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      };
    },
  },
};
// <!-- 分页器 -->
//       <div class="Page">
//         <el-pagination
//           :page-size="pageSize"
//           :current-page="currentPage"
//           layout="prev, pager, next"
//           @current-change="handleCurrentChange"
//           :total="list.length"
//         ></el-pagination>
//       </div>
// 分页器切换
// handleCurrentChange(val) {
//   this.currentPage = val;
// },
//  currentPage: 1, //当前页
//   pageSize: 8, //页码中的数量
// <!-- 搜索栏表单 -->
// <el-card class="box-card">
//   <div class="ArticleTop">
//     <el-form :inline="true" class="demo-form-inline">
//       <el-form-item label="账户:">
//         <el-input v-model="from.title" placeholder="请输入标题"></el-input>
//       </el-form-item>
//       <el-form-item label="状态">
//         <el-select v-model="from.state" placeholder>
//           <el-option label="已发布" value="true"></el-option>
//           <el-option label="未发布" value="false"></el-option>
//         </el-select>
//       </el-form-item>
//       <el-form-item label="分类">
//         <el-select v-model="from.classify" placeholder>
//           <el-option label="1213" value="1213"></el-option>
//           <el-option label="单身23年" value="单身23年"></el-option>
//           <el-option label="憨憨的" value="憨憨的"></el-option>
//           <el-option label="爱吃炒米" value="爱吃炒米"></el-option>
//           <el-option label="有梦想" value="有梦想"></el-option>
//         </el-select>
//       </el-form-item>
//     </el-form>
//   </div>
//   <div class="ArticleButton">
//     <el-button>
//       <a href>重置</a>
//     </el-button>
//     <el-button type="primary" @click="search">搜索</el-button>
//   </div>
// </el-card>
</script>

<style scoped>
.company_tree {
  width: 220px;
}
</style>
