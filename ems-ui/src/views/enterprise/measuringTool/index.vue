<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="表具编号" prop="measuringToolCode">
        <el-input
          v-model="queryParams.measuringToolCode"
          placeholder="请输入表具编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="表具名称" prop="measuringToolName">
        <el-input
          v-model="queryParams.measuringToolName"
          placeholder="请输入表具名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['enterprise:measuringTool:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['enterprise:measuringTool:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['enterprise:measuringTool:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['enterprise:measuringTool:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="measuringToolList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="表具ID" align="center" prop="measuringToolId" />-->
      <el-table-column label="表具编号" align="center" prop="measuringToolCode" />
      <el-table-column label="表具名称" align="center" prop="measuringToolName" />
      <el-table-column label="表具类型" align="center" prop="type" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.measuring_tool_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="标签" align="center" prop="tag" >
        <template slot-scope="scope">
          <el-tag :key="tag" v-for="tag in scope.row.tag.split(',')">{{tag}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['enterprise:measuringTool:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['enterprise:measuringTool:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改表具管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="表具编号" prop="measuringToolCode">
          <el-input v-model="form.measuringToolCode" placeholder="请输入表具编号" />
        </el-form-item>
        <el-form-item label="表具名称" prop="measuringToolName">
          <el-input v-model="form.measuringToolName" placeholder="请输入表具名称" />
        </el-form-item>
        <el-form-item label="标签" prop="tag">
          <el-input-tag v-model="form.tagList" placeholder="请输入标签" />
        </el-form-item>
        <el-form-item label="表具类型" prop="type">
          <el-select v-model="form.type" placeholder="表具类型" default-first-option size="small" >
            <el-option
              v-for="dict in dict.type.measuring_tool_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="采集器" prop="sampler" v-if="isEditShow('sampler')">
          <el-input v-model="form.sampler" placeholder="请输入采集器" />
        </el-form-item>
        <el-form-item label="成本中心" prop="costCenter" v-if="isEditShow('costCenter')">
          <treeselect v-model="form.costCenterId"  :options="costCenterList" :normalizer="normalizer"  placeholder="成本中心" class="company_tree"/>
        </el-form-item>
        <el-form-item label="检定日期" prop="checkDate" v-if="isEditShow('checkDate')">
          <el-date-picker clearable size="small"
            v-model="form.checkDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择检定日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检定单位" prop="checkOrg" v-if="isEditShow('checkOrg')">
          <el-input v-model="form.checkOrg" placeholder="请输入检定单位" />
        </el-form-item>
        <el-form-item label="证书编号" prop="certCode" v-if="isEditShow('certCode')">
          <el-input v-model="form.certCode" placeholder="请输入证书编号" />
        </el-form-item>
        <el-form-item label="安装位置" prop="position" v-if="isEditShow('position')">
          <el-input v-model="form.position" placeholder="请输入安装位置" />
        </el-form-item>
        <el-form-item label="下次检定日期" prop="nextCheckDate" v-if="isEditShow('nextCheckDate')">
          <el-date-picker clearable size="small"
            v-model="form.nextCheckDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择下次检定日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="上级表具" prop="parentId" v-if="isEditShow('parentId')">
          <el-input v-model="form.parentId" placeholder="请输入上级表具" />
        </el-form-item>
        <el-form-item label="计算公式" prop="virtualExpression" v-if="isEditShow('virtualExpression')" >
          <el-input v-model="form.virtualExpression" placeholder="请输入计算公式" readonly />
        </el-form-item>
        <el-form-item label="变量" prop="virtualVariable" v-if="isEditShow('virtualVariable')" :rules="form.type==='virtual'?rules.virtualVariable:[{required:false}]">
          <el-select v-model="form.virtualVariable" placeholder="变量" default-first-option size="small" >
            <el-option
              v-for="dict in dict.type.virtual_variable"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-card shadow="never" class="border-none margin-t24">
          <div slot="header">
            计算关系
          </div>
          <el-button type="primary"  v-for="(item, i) in symbols" :key="i" @click.stop="symbolAdd(item)" style="margin-top: 5px;margin-bottom: 5px">{{item}}</el-button>
          <el-button type="danger" @click="add">+表具</el-button>
          <el-divider/>
          <div v-for="(item, index) in form.recordList" :key="index" style="display: flex">
            <!-- @click.native="seleadd()" -->
            <treeselect  v-model="item.data"  :value="item.id" :options="addList" :normalizer="normalizer" :appendToBody="true"  placeholder="请选择" class="company_tree" @input="seleadd(item.data,index)" />
            <!-- <span class="selectFuhao">{{item.symbol}}</span> -->
            <b class="selectFuhao">{{item.symbol}}</b>
          </div>
        </el-card>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {getMeasuringTool, listMeasuringTool, addMeasuringTool, updateMeasuringTool} from "@/api/enterprise/measuringTool";
  import {getCcostCenter, getDepartment} from "@/api/enterprise/utensil";
  import Treeselect from "@riophae/vue-treeselect";
  import IconSelect from "@/components/IconSelect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
  name: "MeasuringTool",
  dicts: ['measuring_tool_type','virtual_variable'],
  components: { Treeselect,IconSelect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表具管理表格数据
      measuringToolList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      costCenterList:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        measuringToolCode: null,
        measuringToolName: null,
        type: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        measuringToolCode: [
          { required: true, message: "请输入表具编号", trigger: "blur" }
        ],
        measuringToolName: [
          { required: true, message: "请输入表具名称", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "请选择表具类型", trigger: "blur" }
        ],
        virtualVariable: [
          { required: true, message: "请选择变量", trigger: "blur" }
        ],
      },
      virtualShow: {
        virtualExpression: 1,
        virtualVariable:1
      },
      symbols:["+","-","*","/",".","(",")","0","1","2","3","4","5","6","7","8","9"],
      numberarr:["0","1","2","3","4","5","6","7","8","9"],
    };
  },
  created() {
    this.getList();
    this.getCostCenterList();
  },
  methods: {
    /** 查询表具管理列表 */
    getList() {
      this.loading = true;
      listMeasuringTool(this.queryParams).then(response => {
        this.measuringToolList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    isEditShow(property){
      if(this.form.type === 'virtual'){
        return this.virtualShow.hasOwnProperty(property) && this.virtualShow[property] === 1;
      }else{
        return !(this.virtualShow.hasOwnProperty(property) && this.virtualShow[property] === 1);
      }

    },
    getCostCenterList(){
      getCcostCenter().then(response=>{
        // console.log(response)
        this.costCenterList=response.data;
      })
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        measuringToolId: null,
        measuringToolCode: null,
        measuringToolName: null,
        tag: null,
        type: null,
        sampler: null,
        samplerModel: null,
        costCenterId: null,
        checkDate: null,
        checkOrg: null,
        certCode: null,
        position: null,
        nextCheckDate: null,
        parentId: null,
        virtualExpression: null,
        virtualJson: null,
        virtualVariable: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        tagList:[],
        recordList:[],
        numberList:[],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.measuringToolId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加表具管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const measuringToolId = row.measuringToolId || this.ids
      getMeasuringTool(measuringToolId).then(response => {
        this.form = response.data;
        this.form.tagList = this.form.tag?this.form.tag.split(','):[];
        this.form.recordList = [];
        this.form.numberList = [];
        this.open = true;
        this.title = "修改表具管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        this.form.tag = this.form.tagList.join(',');
        if (valid) {
          if (this.form.measuringToolId != null) {
            updateMeasuringTool(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMeasuringTool(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const measuringToolIds = row.measuringToolId || this.ids;
      this.$modal.confirm('是否确认删除表具管理编号为"' + measuringToolIds + '"的数据项？').then(function() {
        return delMeasuringTool(measuringToolIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('enterprise/measuringTool/export', {
        ...this.queryParams
      }, `measuringTool_${new Date().getTime()}.xlsx`)
    },
    symbolAdd(single) {
      // console.log(single)
      //判断之前的记录是否存在
      if(this.form.recordList.length){
        //判断这个数组真最后是否有符号
        if(this.form.recordList[this.form.formInline.recordList.length-1].symbol){

          this.form.recordList[this.form.recordList.length-1].symbol+=single
          let result = this.numberarr.find(v => v === single);
          console.log(result)
          if(result){
            this.form.virtualExpression+="%d"
            this.form.numberList.push(single)
          }else{
            this.form.virtualExpression+=single
          }
        }else{

          this.$set(this.form.recordList[this.form.recordList.length-1], 'symbol', single)
          let result = this.numberarr.find(v => v === single);
          if(result){
            console.log(single)
            this.form.relationExpression+="%d"
            this.form.numberList.push(single)
          }else{
            this.form.relationExpression+=single
          }
        }
      }else{
        return this.$message.error('还没有选择电表呢');
      }

      //this.$data.formInline.relationExpression+=single
      //this.$data.formInline.relationExpression+="%d"
      // console.log(this.$data.formInline.relationExpression)
      // console.log(this.$data.formInline.numberList);
    },
    add() {
      // console.log(this.$data.formInline)
      if(this.form.recordList.length===0){
        this.form.recordList.push({data:null,symbol:null});
      }else{
        if(this.form.recordList[this.form.recordList.length-1].symbol){
          this.form.recordList.push({data:null,symbol:null});
        }else{
          return this.$message.error('请先选择关系再追加电表');
        }
      }

    }
  }
};
</script>
