<template>
  <div class="management_box">
    <!-- <div class="management_nav">能源成本中心列表</div> -->
    <div class="management_search">
      <!-- 搜索栏表单 -->
      <el-card class="box-card">
        <div class="ArticleTop">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="部门编码:">
              <el-input v-model="from.centerCode" placeholder=""></el-input>
            </el-form-item>
            <el-form-item label="部门名称:">
              <el-input v-model="from.centerName" placeholder=""></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
               @click="TabSearch()"
                v-hasPermi="['system:product:export']">搜索</el-button>
                <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="costAdd()"
                v-hasPermi="['system:product:add']">新增</el-button>
              <el-button
                type="warning"
                plain
                icon="el-icon-download"
                size="mini"
               @click="exportfile()"
                v-hasPermi="['system:product:export']">导出</el-button>

                <el-button
                type="danger"
                plain
                icon="el-icon-folder-opened"
                size="mini"
                @click="tree_Btn()">层级关系</el-button>

            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
    <div class="management_table">
      <!-- <el-table
        :data="TabList"
        style="width: 100%"
        border
        highlight-current-row
      >
      <el-table-column
      type="selection"
      width="55">
    </el-table-column>
        <el-table-column label="部门编码" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.centerCode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="部门名称" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.centerName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上一级部门" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.parentCenterName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="系统类型" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.sysType | codeType}}</span>
          </template>
        </el-table-column>
        <el-table-column label="启动状态" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.useStatus | stateType }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row.centerId)" >编辑</el-button >
            <el-button
              size="mini" type="danger" @click="handleDelete(scope.$index, scope.row.centerId)">删除</el-button>
          </template>
        </el-table-column>
      </el-table> -->
      <el-table
        :data="TabList"
        style="width: 100%; margin-bottom: 20px"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="部门编码" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.code }}</span>
          </template>
        </el-table-column>
        <el-table-column label="部门名称" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上一级部门" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{
              scope.row.parentCenterName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="系统类型" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{
              scope.row.sysType | codeType
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="启动状态" width="200">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{
              scope.row.useStatus | stateType
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">

             <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.$index, scope.row.id)"
              v-hasPermi="['system:product:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.$index, scope.row.id)"
              v-hasPermi="['system:product:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="energy_dialog">
      <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
        <el-tree
          :data="treeList"
          :props="defaultProps"
          @node-click="handleNodeClick"
          icon-class="el-icon-folder"
        ></el-tree>
        <span slot="footer" class="dialog-footer"> </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  get_energy,
  get_tree,
  TabSearch,
  exportfile,
  energyDelete,
} from "@/api/enterprise/energy";
export default {
  data() {
    return {
      from: {
        centerCode: "",
        centerName: "",
      },
      dialogVisible: false,
      defaultProps: {
        children: "children",
        label: "label",
      },
      TabList: [],
      treeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobName: undefined,
        jobGroup: undefined,
        status: undefined,
      },
    };
  },
  created() {
    this.getList();
  },
  updated() {
    //  this.expandAll()
  },
  methods: {
    //获取到页面元素  模拟点击可实现让树形表格展开
    expandAll() {
      var els = document.getElementsByClassName("el-table__expand-icon"); //获取点击的箭头元素
      for (let i = 0; i < els.length; i++) {
        els[i].click();
      }
    },
    //编辑
    handleEdit(index, centerIds) {
      this.$router.push({
        path: "/enterprise/centre",
        query: { centerIds: centerIds },
      });
      // console.log(index, row);
    },
    //删除
    handleDelete(index, centerIds) {
      energyDelete(centerIds).then((response) => {
        // console.log(response)
        if (response.code === 200) {
          this.getList();
        }
      });
      // console.log(index, row);
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    handleNodeClick(data) {
      console.log(data);
    },
    //获取表格数据
    getList() {
      get_energy().then((response) => {
        // console.log(response)
        this.$data.TabList = response.data;
      });
    },
    //搜索
    TabSearch() {
      get_energy(this.$data.from).then((response) => {
        console.log(response);
        this.$data.TabList = response.data;
        this.$data.from = {};
      });
    },
    //层级关系
    tree_Btn() {
      this.$data.dialogVisible = true;
      get_tree().then((response) => {
        // console.log(response)
        this.$data.treeList = response.data;
      });
    },
    //导出
    exportfile() {
      // console.log(this.queryParams)
      const queryParams = this.queryParams;
      this.$modal
        .confirm("是否确认导出所有定时任务数据项？")
        .then(() => {
          this.exportLoading = true;
          return exportfile(queryParams);
        })
        .then((response) => {
          this.$download.name(response.msg);
          this.exportLoading = false;
        })
        .catch(() => {});
    },
    //添加
    costAdd() {
      this.$router.push({ path: "/enterprise/centre" });
    },
  },
  //过滤器
  filters: {
    codeType(code) {
      if (code === "1") {
        return "附属系统";
      } else if (code === "2") {
        return "生产系统";
      } else {
        return "辅助系统";
      }
    },
    stateType(state) {
      if (state === "0") {
        return "禁用";
      } else {
        return "启用";
      }
    },
  },
};
</script>

<style>
</style>
