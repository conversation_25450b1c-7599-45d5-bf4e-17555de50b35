<template>
    <div class="text item">
      <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
        <el-form-item label="计量对象">
          <el-input v-model="formInline.meaName" placeholder="计量对象"></el-input>
        </el-form-item>
        <el-form-item label="能源介质">
          <el-select v-model="formInline.energyMed" placeholder="能源介质">
            <el-option  v-for="item in energy_medList" :key="item.energyId" :label="item.energyName" :value="item.energyId"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
       <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
        <el-form-item label="上一级计量对象">
          <treeselect v-model="formInline.meaParentId" :options="mea_preatList" :normalizer="normalizer"  placeholder="上一级计量对象" class="company_tree"/>
        </el-form-item>
        <el-form-item label="能源成本中心">
           <treeselect v-model="formInline.costCenterId" :options="cost_centerList" :normalizer="normalizer"  placeholder="上一级计量对象" class="company_tree"/>
        </el-form-item>
      </el-form>
       <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
        <el-form-item label="型号规格">
          <el-input v-model="formInline.meaSpec" placeholder="型号规格"></el-input>
        </el-form-item>
        <el-form-item label="通讯方式">
          <el-select v-model="formInline.commMode" placeholder="通讯方式">
            <el-option  v-for="item in comm_modeList" :key="item.comm_mode" :label="item.label" :value="item.comm_mode"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
       <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
        <el-form-item label="准确度等级">
          <el-input v-model="formInline.meaAccuracy" placeholder="准确度等级"></el-input>
        </el-form-item>
         <el-form-item label="生产厂家">
          <el-input v-model="formInline.manuFact" placeholder="生产厂家"></el-input>
        </el-form-item>
      </el-form>
       <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
        <el-form-item label="检定周期/校准时间">
          <el-input v-model="formInline.veriCycle" placeholder="检定周期/校准时间"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="formInline.status" placeholder="状态">
            <el-option  v-for="item in statusList" :key="item.status" :label="item.label" :value="item.status"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
       <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
        <el-form-item label="检定单位">
          <el-input v-model="formInline.veriUnit" placeholder="检定单位"></el-input>
        </el-form-item>
           <el-form-item label="证书编号">
          <el-input v-model="formInline.certId" placeholder="证书编号"></el-input>
        </el-form-item>
      </el-form>
       <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
        <el-form-item label="检定日期">
           <el-date-picker v-model="formInline.veriDate" type="date" placeholder="选择日期">
            </el-date-picker>
        </el-form-item>
       <el-form-item label="检定结果">
          <el-input v-model="formInline.veriRes" placeholder="检定结果"></el-input>
        </el-form-item>
      </el-form>
       <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
       <el-form-item label="下一次检定日期">
           <el-date-picker
              v-model="formInline.nextVeriDate"
              type="date"
              placeholder="选择日期">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="安装位置">
          <el-input v-model="formInline.installAddr" placeholder="安装位置"></el-input>
        </el-form-item>
      </el-form>
       <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
        <el-form-item label="电流">
          <el-input v-model="formInline.curTranRatio" placeholder="电流互感器变比"></el-input>
        </el-form-item>
        <el-form-item label="电压">
          <el-input v-model="formInline.voltTranRatio" placeholder="电压互感器变比"></el-input>
        </el-form-item>
      </el-form>
       <!-- <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
        <el-form-item label="串口地址">
          <el-input v-model="formInline.serialAddress" placeholder="串口地址"></el-input>
        </el-form-item>
         <el-form-item label="串口回路号">
          <el-input v-model="formInline.serialLoopNum" placeholder="串口回路号"></el-input>
        </el-form-item>
      </el-form>
       <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
        <el-form-item label="网关地址号">
          <el-input v-model="formInline.gatewayAddrNum" placeholder="网关地址号"></el-input>
        </el-form-item>
        <el-form-item label="网关端口号">
          <el-input v-model="formInline.gatewayPort" placeholder="网关端口号"></el-input>
        </el-form-item>
      </el-form>
      <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="300px" size="max">
        <el-form-item label="网关内起始地址">
          <el-input v-model="formInline.gatewayStartAddr" placeholder="网关内起始地址"></el-input>
        </el-form-item>
         <el-form-item label="企业:">
             <treeselect v-model="formInline.factoryId" :options="firmList" :normalizer="normalizer"  :appendToBody="true" placeholder="企业" class="company_tree"/>
            </el-form-item>
      </el-form> -->
      <div style="text-align:center" class="message_btn">
         <el-button type="primary" @click="onSubmit">提交</el-button>
      <el-button v-on:click="backHistory">取消</el-button>
    </div>
    </div>
</template>

<script>
import { get_energy_med,
          get_mea_preat,
          get_cost_center,
          submitData,
          datacome,
          costChange,
          getfirmList} from "@/api/enterprise/message";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
export default {
  components: { Treeselect,IconSelect },
  data() {
      return {
        formInline: {
          //计量对象
         meaName:"",
         //能源介质
         energyMed:"",
         //上一级计量对象
         meaParentId:null,
         //能源成本中心
         costCenterId:null,
         //型号规格
         meaSpec:"",
         //通讯方式
         commMode:"",
         //准确度等级
         meaAccuracy:"",
         //生产厂家
         manuFact:"",
         //检定周期/校准时间
         veriCycle:"",
         //状态
         status:"",
         //检定单位
         veriUnit:"",
         //证书编号
         certId:"",
         //检定日期
         veriDate:"",
         //检定结果
         veriRes:"",
         //下一次检定日期
         nextVeriDate:"",
         //安装位置
         installAddr:"",
         //电流互感器变比
         curTranRatio:"",
         //电压互感器变比
         voltTranRatio:"",
        //  //串口地址
        //  serialAddress:"",
        //  //串口回路号
        //  serialLoopNum:"",
        //  //网关地址号
        //  gatewayAddrNum:"",
        //  //网关端口号
        //   gatewayPort:"",
        //  //网关内起始地址
        //  gatewayStartAddr:"",
        //  //id
          meaId:"",
        //  //企业
        //  factoryId:null
        },
        //上一级计量对象树
        mea_preatList:[],
        //能源介质下拉数据
        energy_medList:[],
        //能源成本中心树
        cost_centerList:[],
        //通讯方式下拉数据
        comm_modeList:[{label:"无",comm_mode:"0"},{label:"有",comm_mode:"1"}],
        //状态下拉数据
        statusList:[{label:"合格",status:"1"},{label:"不合格",status:"0"}],
        //企业树
        firmList:[]

      }
    },
    created(){
      this.$data.formInline.meaId=this.$route.query.meaId
      console.log(this.$route.query.meaId)
      if(this.$route.query.meaId){

      //获取回显数据
      this.getdatacome(this.$route.query.meaId)
      //获取下拉部门数据
     this.getdata()
    }
      this.getdata()
    },
    methods: {
      onSubmit() {
        if (this.$route.query.meaId) {
        console.log("我是编辑");
        console.log(this.$data.formInline)
        costChange(this.$data.formInline).then(response=>{
          console.log(response)
           if(response.code===200){
          this.$router.push({path:'/enterprise/relation'})
          }
        })
      } else {
         submitData(this.$data.formInline).then(response=>{
          if(response.code===200){
           this.$router.push({path:'/enterprise/relation'})
          }
        })
      }
      },
      //获取回显数据
      getdatacome(meaId){
        datacome(meaId).then(response=>{
          console.log(response)
            this.$data.formInline.meaName=response.data.meaName?response.data.meaName:null;
            this.$data.formInline.energyMed=response.data.energyMed?response.data.energyMed:null
            this.$data.formInline.meaParentId=response.data.meaParentId?response.data. meaParentId:null
            this.$data.formInline.costCenterId=response.data. costCenterId?response.data.costCenterId:null
            this.$data.formInline.meaSpec=response.data.meaSpec?response.data.meaSpec:null
            this.$data.formInline.commMode=response.data.commMode?response.data.commMode:null
            this.$data.formInline.meaAccuracy=response.data. meaAccuracy?response.data. meaAccuracy:null
            this.$data.formInline.manuFact=response.data.manuFact?response.data.manuFact:null;
            this.$data.formInline.veriCycle=response.data.veriCycle?response.data.veriCycle:null
            this.$data.formInline.veriRes=response.data.veriRes?response.data. veriRes:null
            this.$data.formInline.nextVeriDate=response.data.nextVeriDate?response.data.nextVeriDate:null
            this.$data.formInline.installAddr=response.data.installAddr?response.data.installAddr:null
            this.$data.formInline.curTranRatio=response.data. curTranRatio?response.data. curTranRatio:null
            this.$data.formInline.voltTranRatio=response.data. voltTranRatio?response.data. voltTranRatio:null
            // this.$data.formInline.serialAddress=response.data.serialAddress?response.data.serialAddress:null;
            //this.$data.formInline.serialLoopNum=response.data.serialLoopNum?response.data.serialLoopNum:null
            //this.$data.formInline.gatewayAddrNum=response.data.gatewayAddrNum?response.data. gatewayAddrNum:null
            // this.$data.formInline.gatewayPort=response.data. gatewayPort?response.data.gatewayPort:null
            // this.$data.formInline.gatewayStartAddr=response.data.gatewayStartAddr?response.data.gatewayStartAddr:null
            this.$data.formInline.status=response.data.status?response.data.status:null
            this.$data.formInline.veriUnit=response.data.veriUnit?response.data.veriUnit:null
            this.$data.formInline.certId=response.data.certId?response.data.certId:null
            this.$data.formInline.veriDate=response.data.veriDate?response.data.veriDate:null
            //factoryFirmId
            // this.$data.formInline.factoryId=response.data.factoryId?response.data.factoryId:null
        })
      },
    //获取下拉数据
      getdata(){
        //获取能源介质下拉数据
        get_energy_med().then(response=>{
          console.log(response)
          this.$data.energy_medList=response.data
        })
        //获取上一级计量对象下拉数据
        get_mea_preat().then(response=>{
          this.$data.mea_preatList=response.data
          // console.log(response.data)
        })
        get_cost_center().then(response=>{
          this.$data.cost_centerList=response.data
         // console.log(response)
        })
         //获取企业树
        getfirmList().then(response=>{
          this.$data.firmList=response.data
          // console.log(response)
        })
      },
       /** 转换部门数据结构 */
      normalizer(node) {
        if (node.children && !node.children.length) {
          delete node.children;
        }
        return {
          id: node.id,
          label: node.label,
          children: node.children
        };
      },
      backHistory(){
          this.$router.go(-1);//返回上一层
        },

    }
};
</script>

<style scoped>
.text {
  font-size: 20px;
}

.item {
  padding: 18px 0;
}

/* .el-form demo-form-inline{
  width: 100%;
  display: flex;
}
.el-form-item el-form-item--max{
  flex: 1;
} */
.message_btn{
  width: 100%;
  display: flex;
  /* margin: 0 30%; */
  margin-left: 30%;

}
.company_tree{
    width: 220px;
}
.el-table{
  position: relative;
}
.el-input__inner {
    width: 220px;
}

</style>
