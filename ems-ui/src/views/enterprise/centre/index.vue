<template>
  <div class="centre_box">
    <div class="centre_nav">
      <!-- <h3>能源成本中心维护</h3> -->
    </div>
    <div class="centre_from">
      <el-form :inline="true" :model="formInline" class="demo-form-inline"  label-width="300px" size="max">
        <el-form-item label="部门名称">
          <el-input v-model="formInline.centerName"></el-input>
        </el-form-item>
        <el-form-item label="上一级部门">
           <treeselect v-model="formInline.parentCenterId" :options="departmentList" :normalizer="normalizer"  placeholder="请输入部门名称" class="company_tree"/>
        </el-form-item>
      </el-form>
      <el-form :inline="true" :model="formInline" class="demo-form-inline"  label-width="300px" size="max">
        <el-form-item label="系统类别" >
          <el-select v-model="formInline.sysType" placeholder="请选择">
            <el-option  v-for="item in data" :key="item.sysType" :label="item.label" :value="item.sysType"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态">
            <el-switch
              v-model="formInline.useStatus"
              active-color="#13ce66"
              inactive-color="#ff4949"
              active-value="1"
              inactive-value="0"
              change="formInline.useStatus"
              :width=50
            >
            </el-switch>
        </el-form-item>
      </el-form>
      <el-form :inline="true" :model="formInline" class="demo-form-inline"  label-width="300px" size="max">
        <el-form-item label="部门缩写">
          <el-input v-model="formInline.subName"></el-input>
        </el-form-item>
      </el-form>
      <el-form :inline="true" :model="formInline" class="demo-form-inline"  label-width="300px" size="max">
        <el-form-item class="center_btn">
          <el-button type="primary" @click="onSubmit" >提交</el-button>
          <el-button v-on:click="backHistory">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { get_department, costAdd ,datacome,costChange} from "@/api/enterprise/centre";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
export default {
  components: { Treeselect,IconSelect },
  data() {
    return {
      formInline: {
        //上一级部门
        parentCenterId: null,
        //系统
        sysType: "",
        //部门名称
        centerName: "",
        //状态
        useStatus:"1",
        centerId:null,
        subName:""
      },
      departmentList: [],
      optionProps: {
        value: "id",
        label: "label",
        children: "children",
      },
      value:"1",
      data:[{label:"附属系统",sysType:"1"},{label:"生产系统",sysType:"2"},{label:"辅助系统",sysType:"3"}]
    };
  },
  created() {
    this.$data.formInline.centerId=this.$route.query.centerIds
    if(this.$route.query.centerIds){
      //获取回显数据
      this.get_datacome(this.$route.query.centerIds)
      //获取下拉部门数据
      this.get_department();
    }

    //获取下拉部门数据
    this.get_department();
  },
  methods: {
    onSubmit() {
      // console.log(this.$data.formInline);
      if (this.$route.query.centerIds) {
        console.log("我是编辑");
        costChange(this.$data.formInline).then(response=>{
          console.log(response)
           if(response.code===200){
          this.$router.push({path:'/enterprise/energy'})
          }
        })
      } else {
        console.log("我是添加")
        console.log(this.$data.formInline);
        costAdd(this.$data.formInline).then((response) => {
          console.log(response);
          if(response.code===200){
          this.$router.push({path:'/enterprise/energy'})
          }
        });
      }
    },
    //获取下拉列表
    get_department() {
      get_department().then((response) => {
        this.$data.departmentList = response.data;
      });
    },
    //获取回显数据
    get_datacome(centerIds){
      // console.log(centerIds)
     datacome(centerIds).then(response=>{
      //  console.log(response.data)
       this.$data.formInline.parentCenterId=response.data.parentCenterId?response.data.parentCenterId:null;
       this.$data.formInline.sysType=response.data.sysType?response.data.sysType:null
       this.$data.formInline.centerName=response.data.centerName?response.data.centerName:null
       this.$data.formInline.useStatus=response.data.useStatus?response.data.useStatus:null;
       this.$data.formInline.subName=response.data.subName
     })
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children
      };
    },
     backHistory(){
    this.$router.go(-1);//返回上一层
  },
  },
};
</script>

<style lang="scss">
.text {
  font-size: 20px;
}

.item {
  padding: 18px 0;
}
.centre_box {
  width: 100%;
  height: 100%;
  .centre_nav {
    width: 100%;
    height: 40px;
    line-height: 60px;
    margin-left: 10px;
    // border-bottom: 3px solid #ccc;
  }
  .centre_from {
    width: 100%;
    height: 100%;
    // margin: 15px 10% 0 10%;
  }
  .demo-form-inline {
    margin-top: 10px;
    display: flex;
    .el-form-item {
      flex: 1;
    }
  }
  .company_tree{
    width: 220px;
  }
  .el-input__inner {
    width: 220px;
  }
.center_btn{
    width: 100px;
    margin-left: 60%;
    transform: translateX(-50%);
  }
}
</style>
