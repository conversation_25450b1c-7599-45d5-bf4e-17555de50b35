<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="90px">
      <!-- <el-form-item label="工位主键" prop="stationId">
        <el-input
          v-model="queryParams.stationId"
          placeholder="请输入工位主键"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="条码号" prop="barCode">
        <el-input
          v-model="queryParams.barCode"
          placeholder="请输入条码号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
	  <el-form-item label="工单号" prop="workOrderCode">
	    <el-input
	      v-model="queryParams.workOrderCode"
	      placeholder="请输入工单号"
	      clearable
	      size="small"
	      @keyup.enter.native="handleQuery"
	    />
	  </el-form-item>
	  <el-form-item label="总成图号" prop="assemblyCode">
	    <el-input
	      v-model="queryParams.assemblyCode"
	      placeholder="请输入总成图号"
	      clearable
	      size="small"
	      @keyup.enter.native="handleQuery"
	    />
	  </el-form-item>
      <el-form-item label="扫描时间" prop="scanningTime">
		<el-date-picker
		  v-model="scanningTime"
		  size="small"
		  style="width: 240px"
		  value-format="yyyy-MM-dd"
		  type="daterange"
		  range-separator="-"
		  start-placeholder="开始日期"
		  end-placeholder="结束日期"
		></el-date-picker>		
      </el-form-item>
      <el-form-item label="完工时间" prop="doneTime">
		<el-date-picker
		  v-model="doneTime"
		  size="small"
		  style="width: 240px"
		  value-format="yyyy-MM-dd"
		  type="daterange"
		  range-separator="-"
		  start-placeholder="开始日期"
		  end-placeholder="结束日期"
		></el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="删除标志0正常1删除" prop="deleteFlag">
        <el-input
          v-model="queryParams.deleteFlag"
          placeholder="请输入删除标志0正常1删除"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['equipment:partsProcessResult:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['equipment:partsProcessResult:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['equipment:partsProcessResult:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
          v-hasPermi="['equipment:partsProcessResult:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="partsProcessResultList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="零件工艺结果主键" align="center" prop="partsProcessResultId" />
      <el-table-column label="工位主键" align="center" prop="stationId" /> -->
      <el-table-column label="条码号" align="center" prop="barCode" />
	  <el-table-column label="工单号" align="center" prop="workOrderCode" />
	  <el-table-column label="总成图号" align="center" prop="assemblyCode" />
      <el-table-column label="扫描时间" align="center" prop="scanningTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.scanningTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完工时间" align="center" prop="doneTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.doneTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结果" align="center" prop="result" />
      <!-- <el-table-column label="删除标志0正常1删除" align="center" prop="deleteFlag" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['equipment:partsProcessResult:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['equipment:partsProcessResult:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改零件工艺结果对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工位主键" prop="stationId">
          <el-input v-model="form.stationId" placeholder="请输入工位主键" />
        </el-form-item>
        <el-form-item label="条码号" prop="barCode">
          <el-input v-model="form.barCode" placeholder="请输入条码号" />
        </el-form-item>
        <el-form-item label="扫描时间" prop="scanningTime">
          <el-date-picker clearable size="small"
            v-model="form.scanningTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择扫描时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="完工时间" prop="doneTime">
          <el-date-picker clearable size="small"
            v-model="form.doneTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择完工时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结果" prop="result">
          <el-input v-model="form.result" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <!-- <el-form-item label="删除标志0正常1删除" prop="deleteFlag">
          <el-input v-model="form.deleteFlag" placeholder="请输入删除标志0正常1删除" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPartsProcessResult, getPartsProcessResult, delPartsProcessResult, addPartsProcessResult, updatePartsProcessResult, exportPartsProcessResult } from "@/api/equipment/partsProcessResult";

export default {
  name: "PartsProcessResult",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 零件工艺结果表格数据
      partsProcessResultList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
	  scanningTime: null,
	  doneTime: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stationId: null,
        barCode: null,
		workOrderCode: null,
		assemblyCode: null,
        result: null,
        deleteFlag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        barCode: [
          { required: true, message: "条码号不能为空", trigger: "blur" }
        ],
        scanningTime: [
          { required: true, message: "扫描时间不能为空", trigger: "blur" }
        ],
        doneTime: [
          { required: true, message: "完工时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询零件工艺结果列表 */
    getList() {
      this.loading = true;
	  if("" != this.scanningTime && null != this.scanningTime){
		this.queryParams.beginScanningTime = this.scanningTime[0]
		this.queryParams.endScanningTime = this.scanningTime[1]
	  }
	  if("" != this.doneTime && null != this.doneTime){
	  		this.queryParams.beginDonebeginTime = this.doneTime[0]
	  		this.queryParams.endDoneTime = this.doneTime[1]
	  }
      listPartsProcessResult(this.queryParams).then(response => {
        this.partsProcessResultList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        partsProcessResultId: null,
        stationId: null,
        barCode: null,
        scanningTime: null,
        doneTime: null,
        result: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deleteFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.partsProcessResultId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加零件工艺结果";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const partsProcessResultId = row.partsProcessResultId || this.ids
      getPartsProcessResult(partsProcessResultId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改零件工艺结果";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.partsProcessResultId != null) {
            updatePartsProcessResult(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPartsProcessResult(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const partsProcessResultIds = row.partsProcessResultId || this.ids;
      this.$confirm('是否确认删除零件工艺结果编号为"' + partsProcessResultIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delPartsProcessResult(partsProcessResultIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有零件工艺结果数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.exportLoading = true;
          return exportPartsProcessResult(queryParams);
        }).then(response => {
          this.download(response.msg);
          this.exportLoading = false;
        }).catch(() => {});
    }
  }
};
</script>
