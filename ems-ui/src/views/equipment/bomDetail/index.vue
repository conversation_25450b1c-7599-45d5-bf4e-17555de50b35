<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="130px">
      <el-form-item label="图号" prop="partNumber">
        <el-input
          v-model="queryParams.partNumber"
          placeholder="请输入图号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="零件名" prop="partName">
        <el-input
          v-model="queryParams.partName"
          placeholder="请输入零件名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="条码规则" prop="matchRule">
        <el-input
          v-model="queryParams.matchRule"
          placeholder="请输入条码规则"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用量" prop="dosage">
        <el-input
          v-model="queryParams.dosage"
          placeholder="请输入用量"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="零件类别" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择零件类别" clearable size="small">
          <el-option
          	v-for="item in typeList"
          	:key="item.dictCode"
          	:label="item.dictLabel"
          	:value="item.dictCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="追溯类型" prop="traceablityType">
        <el-select v-model="queryParams.traceablityType" placeholder="请选择追溯类型" clearable size="small">
          <el-option
          	v-for="item in traceablityTypeList"
          	:key="item.dictCode"
          	:label="item.dictLabel"
          	:value="item.dictCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="父零件" prop="parentId">
        <el-input
          v-model="queryParams.parentId"
          placeholder="请输入父零件"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="扫描顺序" prop="sequence">
        <el-input
          v-model="queryParams.sequence"
          placeholder="请输入扫描顺序"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="BOM ID" prop="bomId">
        <el-input
          v-model="queryParams.bomId"
          placeholder="请输入BOM ID"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="删除标志" prop="deleteFlag">
        <el-input
          v-model="queryParams.deleteFlag"
          placeholder="请输入删除标志0正常1删除"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-="['equipment:bomDetail:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['equipment:bomDetail:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['equipment:bomDetail:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['system:user:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
          v-hasPermi="['equipment:bomDetail:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bomDetailList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="BOM明细ID" align="center" prop="bomDetailId" v-if="false"/>
      <el-table-column label="图号" align="center" prop="partNumber" />
      <el-table-column label="零件名" align="center" prop="partName" />
      <el-table-column label="条码规则" align="center" prop="matchRule" />
      <el-table-column label="用量" align="center" prop="dosage" />
      <el-table-column label="零件类别" align="center">
			<template slot-scope="scope">
	  			<span v-html="tFormatter(scope.row.type)"></span>
	  		</template>
	  </el-table-column>
      <el-table-column label="追溯类型" align="center">
			<template slot-scope="scope">
	  			<span v-html="tTFormatter(scope.row.traceablityType)"></span>
	  		</template>
	  </el-table-column>
      <el-table-column label="父零件" align="center" prop="parentId" />
      <el-table-column label="扫描顺序" align="center" prop="sequence" />
      <el-table-column label="BOM ID" align="center" prop="bomId" v-if="false"/>
      <el-table-column label="描述" align="center" prop="remark" />
      <!-- <el-table-column label="删除标志0正常1删除" align="center" prop="deleteFlag" />-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['equipment:bomDetail:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['equipment:bomDetail:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改bom详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="图号" prop="partNumber">
          <el-input v-model="form.partNumber" placeholder="请输入图号" />
        </el-form-item>
        <el-form-item label="零件名" prop="partName">
          <el-input v-model="form.partName" placeholder="请输入零件名" />
        </el-form-item>
        <el-form-item label="条码规则" prop="matchRule">
          <el-input v-model="form.matchRule" placeholder="请输入条码规则" />
        </el-form-item>
        <el-form-item label="用量" prop="dosage">
          <el-input v-model="form.dosage" placeholder="请输入用量" />
        </el-form-item>
        <el-form-item label="零件类别" prop="type">
          <el-select v-model="form.type" placeholder="请选择零件类别">
            <el-option
            	v-for="item in typeList"
            	:key="item.dictCode"
            	:label="item.dictLabel"
            	:value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="追溯类型" prop="traceablityType">
          <el-select v-model="form.traceablityType" placeholder="请选择追溯类型">
            <el-option
            	v-for="item in traceablityTypeList"
            	:key="item.dictCode"
            	:label="item.dictLabel"
            	:value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="父零件" prop="parentId">
          <el-input v-model="form.parentId" placeholder="请输入父零件" />
        </el-form-item>
        <el-form-item label="扫描顺序" prop="sequence">
          <el-input v-model="form.sequence" placeholder="请输入扫描顺序" />
        </el-form-item>
        <!-- <el-form-item label="BOM ID" prop="bomId">
          <el-input v-model="form.bomId" placeholder="请输入BOM ID" />
        </el-form-item> -->
        <el-form-item label="描述" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入描述" />
        </el-form-item>
        <!-- <el-form-item label="删除标志0正常1删除" prop="deleteFlag">
          <el-input v-model="form.deleteFlag" placeholder="请输入删除标志0正常1删除" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
		:data="{bomId:queryParams.bomId}"		
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBomDetail, getBomDetail, delBomDetail, addBomDetail, updateBomDetail, exportBomDetail, importTemplate } from "@/api/equipment/bomDetail";
import { getToken } from "@/utils/auth";
import { listType } from "@/api/equipment/typeList.js"

export default {
  name: "BomDetail",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // bom详情表格数据
      bomDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
	  //bom
	  bomData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        partNumber: null,
        partName: null,
        matchRule: null,
        dosage: null,
        type: null,
        traceablityType: null,
        parentId: null,
        sequence: null,
        bomId: null,
        deleteFlag: null
      },
      // 表单参数
      form: {},
	  //零件类别list
	  typeList: [],
	  //追溯类型list
	  traceablityTypeList: [],
      // 表单校验
      rules: {
        bomId: [
          { required: true, message: "BOM ID不能为空", trigger: "blur" }
        ],
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/equipment/bomDetail/importData"
      }
    };
  },
  activated() {
    this.getList();
	this.bomData = this.$route.query
	this.queryParams.bomId = this.bomData.bomId
	this.form.bomId = this.bomData.bomId
	
	//调用查询零件类别列表
	this.getTypeList();
	//调用查询追溯类型列表
	this.getTraceablityTypeList();
  },
  methods: {
	/** 查询零件类别列表 */
	getTypeList() {
	  let data = {
		  dictType : "equ_accessory_type"
	  }
	  listType(data).then(response => {
	    this.typeList = response.rows;
	  });
	},
	tFormatter(value){
		let cyxbvalue = value;
		if (cyxbvalue == null || cyxbvalue == "" || cyxbvalue == undefined) {
			return cyxbvalue;
		 } else {
			let dycyxb = this.typeList.filter((item) => item.dictCode === cyxbvalue);//filter过滤方法（看自己的情况、需求）
			return dycyxb[0].dictLabel;//rerun的内容即为要在表格中显示的内容
		}
	},  
	/** 查询追溯类型列表 */
	getTraceablityTypeList() {
	  let data = {
		  dictType : "equ_goback_type"
	  }
	  listType(data).then(response => {
	    this.traceablityTypeList = response.rows;
	  });
	},
	tTFormatter(value){
		let cyxbvalue = value;
		if (cyxbvalue == null || cyxbvalue == "" || cyxbvalue == undefined) {
			return cyxbvalue;
		 } else {
			let dycyxb = this.traceablityTypeList.filter((item) => item.dictCode === cyxbvalue);//filter过滤方法（看自己的情况、需求）
			return dycyxb[0].dictLabel;//rerun的内容即为要在表格中显示的内容
		}
	},  
    /** 查询bom详情列表 */
    getList() {
      this.loading = true;
      this.form.weldId = this.$route.query.weldId
      this.queryParams.weldId = this.$route.query.weldId
      listBomDetail(this.queryParams).then(response => {
        this.bomDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        bomDetailId: null,
        partNumber: null,
        partName: null,
        matchRule: null,
        dosage: null,
        type: null,
        traceablityType: null,
        parentId: null,
        sequence: null,
        bomId: this.bomData.bomId,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deleteFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.bomDetailId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加bom详情";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const bomDetailId = row.bomDetailId || this.ids
      getBomDetail(bomDetailId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改bom详情";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.bomDetailId != null) {
            updateBomDetail(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBomDetail(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const bomDetailIds = row.bomDetailId || this.ids;
      this.$confirm('是否确认删除bom详情编号为"' + bomDetailIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delBomDetail(bomDetailIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有bom详情数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.exportLoading = true;
          return exportBomDetail(queryParams);
        }).then(response => {
          this.download(response.msg);
          this.exportLoading = false;
        }).catch(() => {});
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
