<template>
  <div class="app-container">
	<el-breadcrumb style="margin-bottom: 20px;" separator="/">
		<el-breadcrumb-item>{{factoryNameTemp}}</el-breadcrumb-item>
		<el-breadcrumb-item>{{workshopNameTemp}}</el-breadcrumb-item>
		<el-breadcrumb-item>{{productionLineNameTemp}}</el-breadcrumb-item>
	</el-breadcrumb>	
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="130px">
      <el-form-item label="工位编号" prop="stationCode">
        <el-input
          v-model="queryParams.stationCode"
          placeholder="请输入工位编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工位名称" prop="stationName">
        <el-input
          v-model="queryParams.stationName"
          placeholder="请输入工位名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工位类型" prop="stationType">
        <el-select v-model="queryParams.stationType" placeholder="请选择工位类型" clearable size="small">
			<el-option
				v-for="item in stationTypeList"
				:key="item.dictCode"
				:label="item.dictLabel"
				:value="item.dictCode">
			</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否扫描夹具" prop="isScanFixture">
		<el-select v-model="queryParams.isScanFixture" placeholder="请选择是否扫描夹具" clearable size="small">
		  <el-option key="0" label="是" value="0"></el-option>
		  <el-option key="1" label="否" value="1"></el-option>
		</el-select>
      </el-form-item>
      <el-form-item label="是否解绑夹具" prop="isUntieFixture">
		<el-select v-model="queryParams.isUntieFixture" placeholder="请选择是否解绑夹具" clearable size="small">
		  <el-option key="0" label="是" value="0"></el-option>
		  <el-option key="1" label="否" value="1"></el-option>
		</el-select>
      </el-form-item>
      <el-form-item label="COM口" prop="comPort">
        <el-input
          v-model="queryParams.comPort"
          placeholder="请输入COM口"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="波特率" prop="baund">
        <el-input
          v-model="queryParams.baund"
          placeholder="请输入波特率"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="PLC地址" prop="plcAddress">
        <el-input
          v-model="queryParams.plcAddress"
          placeholder="请输入PLC地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="PLC型号" prop="plcType">
        <el-select v-model="queryParams.plcType" placeholder="请选择PLC型号" clearable size="small">
          <el-option
            v-for="item in plcTypeList"
            :key="item.dictCode"
            :label="item.dictLabel"
            :value="item.dictCode">
          </el-option>
        </el-select>
      </el-form-item>
      <!--<el-form-item label="产线" prop="productionLineId">-->
        <!--<el-cascader-->
          <!--v-model="queryParams.productionLineId"-->
          <!--:options="lineList"-->
          <!--@change="handleChange"></el-cascader>-->
      <!--</el-form-item>-->
      <!--<el-form-item label="删除标识" prop="deleteFlag">
        <el-input
          v-model="queryParams.deleteFlag"
          placeholder="请输入删除标识"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item label="工艺" prop="processId">
        <el-select v-model="queryParams.processId" placeholder="请选择工艺" clearable>
          <el-option
            v-for="item in listProcess"
            :key="item.processId"
            :label="item.processName"
            :value="item.processId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['equipment:station:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['equipment:station:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['equipment:station:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['system:user:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
          v-hasPermi="['equipment:station:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="stationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="120" align="center" />
      <!--<el-table-column label="工艺id" align="center" prop="stationId" />-->
      <el-table-column label="工位编号" align="center" prop="stationCode" />
      <el-table-column label="工位名称" align="center" prop="stationName" />
      <el-table-column label="工位类型" align="center" >
		  <template slot-scope="scope">
			<span v-html="sTFormatter(scope.row.stationType)"></span>
		  </template>
	  </el-table-column>
      <el-table-column label="是否扫描夹具" align="center">
		  <template slot-scope="scope">
			<span v-if="scope.row.isScanFixture == 0">是</span>
			<span v-if="scope.row.isScanFixture == 1">否</span>
		  </template>
	  </el-table-column>
      <el-table-column label="是否解绑夹具" align="center">
		  <template slot-scope="scope">
			<span v-if="scope.row.isUntieFixture == 0">是</span>
			<span v-if="scope.row.isUntieFixture == 1">否</span>
		  </template>
	  </el-table-column>
	  <el-table-column label="工艺" align="center" prop="process.processName" />
      <el-table-column label="COM口" align="center" prop="comPort" />
      <el-table-column label="波特率" align="center" prop="baund" />
      <el-table-column label="PLC地址" align="center" prop="plcAddress" />
      <el-table-column label="PLC型号" align="center">
			  <template slot-scope="scope">
				<span v-html="pTFormatter(scope.row.plcType)"></span>
	  		  </template>
	  </el-table-column>
      <el-table-column label="产线ID" align="center" prop="productionLineId" v-if="false"/>
      <el-table-column label="描述" align="center" prop="remark" />
      <!--<el-table-column label="删除标识" align="center" prop="deleteFlag" />-->
      <el-table-column label="工艺id" align="center" prop="processId" v-if="false"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['equipment:station:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['equipment:station:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看工步</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改工位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="工位编号" prop="stationCode">
          <el-input v-model="form.stationCode" placeholder="请输入工位编号" />
        </el-form-item>
        <el-form-item label="工位名称" prop="stationName">
          <el-input v-model="form.stationName" placeholder="请输入工位名称" />
        </el-form-item>
		<el-form-item label="工艺" prop="processId">
		  <el-select v-model="form.processId" placeholder="请选择工艺" clearable>
		    <el-option
		      v-for="item in listProcess"
		      :key="item.processId"
		      :label="item.processName"
		      :value="item.processId">
		    </el-option>
		  </el-select>
		</el-form-item>
        <el-form-item label="工位类型" prop="stationType">
          <el-select v-model="form.stationType" placeholder="请选择工位类型">
            <el-option
              v-for="item in stationTypeList"
              :key="item.dictCode"
              :label="item.dictLabel"
              :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否扫描夹具" prop="isScanFixture">
		  <el-select v-model="form.isScanFixture" placeholder="请选择是否扫描夹具" clearable size="small">
		    <el-option key="0" label="是" :value="0"></el-option>
		    <el-option key="1" label="否" :value="1"></el-option>
		  </el-select>
        </el-form-item>
        <el-form-item label="是否解绑夹具" prop="isUntieFixture">
		  <el-select v-model="form.isUntieFixture" placeholder="请选择是否解绑夹具" clearable size="small">
		    <el-option key="0" label="是" :value="0"></el-option>
		    <el-option key="1" label="否" :value="1"></el-option>
		  </el-select>
        </el-form-item>
        <el-form-item label="COM口" prop="comPort">
          <el-input v-model="form.comPort" placeholder="请输入COM口" />
        </el-form-item>
        <el-form-item label="波特率" prop="baund">
          <el-input v-model="form.baund" placeholder="请输入波特率" />
        </el-form-item>
        <el-form-item label="PLC地址" prop="plcAddress">
          <el-input v-model="form.plcAddress" placeholder="请输入PLC地址" />
        </el-form-item>
        <el-form-item label="PLC型号" prop="plcType">
          <el-select v-model="form.plcType" placeholder="请选择PLC型号">
            <el-option
              v-for="item in plcTypeList"
              :key="item.dictCode"
              :label="item.dictLabel"
              :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <!--<el-form-item label="产线" prop="productionLineId">-->
          <!--<el-select v-model="form.productionLineId" placeholder="请选择产线" :props="props" clearable>-->
            <!--<el-option-->
              <!--v-for="item in lineList"-->
              <!--:key="item.productionLineId"-->
              <!--:label="item.productionLineName"-->
              <!--:value="item.productionLineId">-->
            <!--</el-option>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <el-form-item label="描述" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入描述" />
        </el-form-item>
        <!--<el-form-item label="删除标识" prop="deleteFlag">
          <el-input v-model="form.deleteFlag" placeholder="请输入删除标识" />
        </el-form-item>-->
        
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStation, getStation, delStation, addStation, updateStation, exportStation, importTemplate } from "@/api/equipment/station";
import { getToken } from "@/utils/auth";
import { listFactory } from "@/api/equipment/factory";
import { listWorkshop } from "@/api/equipment/workshop";
import { listLine } from "@/api/equipment/line";
import { listProcess } from "@/api/equipment/process";
import { listType } from "@/api/equipment/typeList.js"
export default {
  name: "Station",
  data() {
    return {
	  //展示工厂名称
	  factoryNameTemp:"",
	  //展示车间名称
	  workshopNameTemp:"",
	  //展示产线名称
	  productionLineNameTemp:"",
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工位表格数据
      stationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stationCode: null,
        stationName: null,
        stationType: null,
        isScanFixture: null,
        isUntieFixture: null,
        comPort: null,
        baund: null,
        plcAddress: null,
        plcType: null,
        productionLineId: null,
        deleteFlag: null,
        processId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        stationCode: [
          { required: true, message: "工位编号不能为空", trigger: "blur" }
        ],
      },
      lineList: [],
	  //工艺类型List
      listProcess: [],
	  //工位类型List
	  stationTypeList: [],
	  //PLC型号list
	  plcTypeList: [],
      props: {
        lazy: true,
        lazyLoad (node, resolve) {
          const { level } = node;
          setTimeout(() => {
            // const nodes = Array.from({ length: level + 1 })
            //   .map(item => ({
            //
            //     value: ++id,
            //     label: `选项${id}`,
            //     leaf: level >= 2
            //   }));
            // // 通过调用resolve将子节点数据返回，通知组件数据加载完成
            // debugger
            resolve(nodes);
          }, 1000);
        }
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/equipment/station/importData"
      },
      lineData: {}
    }
  },
  watch:{
    lineList:{//深度监听，可监听到对象、数组的变化
      handler(val, oldVal){
        // console.log("b.c: "+val.c, oldVal.c);
        this.lineList = val
      },
      deep:true //true 深度监听
    }
  },
  mounted () {},
  methods: {
	/** 查询工位类型列表 */
	getStationTypeList() {
	  let data = {
		  dictType : "equ_station_type"
	  }
	  listType(data).then(response => {
	    this.stationTypeList = response.rows;
	  });
	},
	sTFormatter(value){
		//工位类型
		let cyxbvalue = value;
		if (cyxbvalue == null || cyxbvalue == "" || cyxbvalue == undefined) {
			return cyxbvalue;
		 } else {
			let dycyxb = this.stationTypeList.filter((item) => item.dictCode === cyxbvalue);//filter过滤方法（看自己的情况、需求）
			return dycyxb[0].dictLabel;//rerun的内容即为要在表格中显示的内容
		}
	},
	/** 查询工位类型列表 */
	getPlcTypeList() {
	  let data = {
		  dictType : "equ_plc_type"
	  }
	  listType(data).then(response => {
	    this.plcTypeList = response.rows;
	  });
	},
	pTFormatter(value){
		//PLC型号
		let cyxbvalue = value;
		if (cyxbvalue == null || cyxbvalue == "" || cyxbvalue == undefined) {
			return cyxbvalue;
		 } else {
			let dycyxb = this.plcTypeList.filter((item) => item.dictCode === cyxbvalue);//filter过滤方法（看自己的情况、需求）
			return dycyxb[0].dictLabel;//rerun的内容即为要在表格中显示的内容
		}
	},
    /** 查询工位列表 */
    getList() {
      this.loading = true;
      listStation(this.queryParams).then(response => {
        this.stationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getFactoryList () {
      const arr = []
      listFactory().then(response => {
        response.rows.map(item => {
          arr.push({
            value: item.factoryId,
            label: item.factoryName
          })
        })
        this.lineList = arr
      });
    },
    getListProcess () {
      listProcess().then(response => {
        this.listProcess = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        stationId: null,
        stationCode: null,
        stationName: null,
        stationType: null,
        isScanFixture: null,
        isUntieFixture: null,
        comPort: null,
        baund: null,
        plcAddress: null,
        plcType: null,
        productionLineId: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deleteFlag: null,
        processId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.stationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.productionLineId = this.lineData.productionLineId
      this.open = true;
      this.title = "添加工位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const stationId = row.stationId || this.ids
      getStation(stationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工位";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.stationId != null) {
            updateStation(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStation(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const stationIds = row.stationId || this.ids;
      this.$confirm('是否确认删除工位编号为"' + stationIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delStation(stationIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有工位数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.exportLoading = true;
          return exportStation(queryParams);
        }).then(response => {
          this.download(response.msg);
          this.exportLoading = false;
        }).catch(() => {});
    },
    handleChange (value) {
      console.log(value)
      const pram = {factoryId: value[0]}
      listWorkshop(pram).then(response => {
        this.lineList.map(item => {
          if (item.value === value[0]) {
            const arr = []
            response.rows.map(row => {
              arr.push({value: row.workshopId, label: row.workshopNameTemp})
              item.children = arr
            })
          }
        })
        debugger
        console.log(this.lineList)
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    handleView(row) {
	  row.factoryNameTemp = this.factoryNameTemp
	  row.workshopNameTemp = this.workshopNameTemp
	  row.productionLineNameTemp = this.productionLineNameTemp
	  this.$router.push({path:'/equipment/step',query: row})
	}
  },
  activated() {
    this.lineData = this.$route.query
    this.queryParams.productionLineId = this.lineData.productionLineId
  	this.productionLineNameTemp = this.lineData.productionLineName
  	this.workshopNameTemp = this.lineData.workshopNameTemp
  	this.factoryNameTemp = this.lineData.factoryNameTemp
    this.getListProcess()
    this.getFactoryList()
	this.getStationTypeList()
	this.getPlcTypeList()
	this.getList();
  },
};
</script>
