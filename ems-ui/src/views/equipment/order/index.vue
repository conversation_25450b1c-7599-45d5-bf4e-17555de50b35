<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="130px">
      <el-form-item label="MES工单" prop="mesOrderId">
        <el-input
          v-model="queryParams.mesOrderId"
          placeholder="请输入MES工单"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工单编号" prop="workOrderCode">
        <el-input
          v-model="queryParams.workOrderCode"
          placeholder="请输入工单编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="MES工厂编号" prop="plantCode">
        <el-input
          v-model="queryParams.plantCode"
          placeholder="请输入MES工厂编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料编码" prop="itemCode">
        <el-input
          v-model="queryParams.itemCode"
          placeholder="请输入物料编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组件物料编码" prop="materialId">
        <el-input
          v-model="queryParams.materialId"
          placeholder="请输入组件物料编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="完工仓库" prop="warehouseCode">
        <el-input
          v-model="queryParams.warehouseCode"
          placeholder="请输入完工仓库"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="完工货位" prop="locatorCode">
        <el-input
          v-model="queryParams.locatorCode"
          placeholder="请输入完工货位"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="MES产线编码" prop="prodLineCode">
        <el-input
          v-model="queryParams.prodLineCode"
          placeholder="请输入MES产线编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="workOrderStatus">
        <el-select v-model="queryParams.workOrderStatus" placeholder="请选择状态" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="数量" prop="quantity">
        <el-input
          v-model="queryParams.quantity"
          placeholder="请输入数量"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划开始时间" prop="planStartTime">
        <el-date-picker clearable size="small"
          v-model="queryParams.planStartTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择计划开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划完成时间" prop="planEndTime">
        <el-date-picker clearable size="small"
          v-model="queryParams.planEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择计划完成时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="产线id" prop="productionLineId">
        <el-input
          v-model="queryParams.productionLineId"
          placeholder="请输入产线id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="总成id" prop="assemblyId">
        <el-input
          v-model="queryParams.assemblyId"
          placeholder="请输入总成id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工单类型" prop="workOrderType">
        <el-select v-model="queryParams.workOrderType" placeholder="请选择工单类型" clearable size="small">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="完工时间" prop="endTime">
        <el-date-picker clearable size="small"
          v-model="queryParams.endTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择完工时间">
        </el-date-picker>
      </el-form-item>
      <!--<el-form-item label="删除标志0正常1删除" prop="deleteFlag">
        <el-input
          v-model="queryParams.deleteFlag"
          placeholder="请输入删除标志0正常1删除"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['equipment:order:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['equipment:order:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['equipment:order:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['system:user:import']"
        >导入</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
          v-hasPermi="['equipment:order:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="工单id" align="center" prop="workOrderId" v-if="false"/>
      <el-table-column label="MES工单" align="center" prop="mesOrderId" />
      <el-table-column label="工单编号" align="center" prop="workOrderCode" />
      <el-table-column label="MES工厂编号" align="center" prop="plantCode" />
      <el-table-column label="物料编码" align="center" prop="itemCode" />
      <el-table-column label="组件物料编码" align="center" prop="materialId" />
      <el-table-column label="完工仓库" align="center" prop="warehouseCode" />
      <el-table-column label="完工货位" align="center" prop="locatorCode" />
      <el-table-column label="MES产线编码" align="center" prop="prodLineCode" />
      <el-table-column label="状态" align="center" prop="workOrderStatus" />
      <el-table-column label="数量" align="center" prop="quantity" />
      <el-table-column label="计划开始时间" align="center" prop="planStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planStartTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="计划完成时间" align="center" prop="planEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planEndTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="产线id" align="center" prop="productionLineId" />
      <el-table-column label="总成id" align="center" prop="assemblyId" />
      <el-table-column label="工单类型" align="center" prop="workOrderType" />
      <el-table-column label="完工时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="描述" align="center" prop="remark" />
      <!--<el-table-column label="删除标志0正常1删除" align="center" prop="deleteFlag" />-->
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['equipment:order:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['equipment:order:remove']"
          >删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改工单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="MES工单" prop="mesOrderId">
          <el-input v-model="form.mesOrderId" placeholder="请输入MES工单" />
        </el-form-item>
        <el-form-item label="工单编号" prop="workOrderCode">
          <el-input v-model="form.workOrderCode" placeholder="请输入工单编号" />
        </el-form-item>
        <el-form-item label="MES工厂编号" prop="plantCode">
          <el-input v-model="form.plantCode" placeholder="请输入MES工厂编号" />
        </el-form-item>
        <el-form-item label="物料编码" prop="itemCode">
          <el-input v-model="form.itemCode" placeholder="请输入物料编码" />
        </el-form-item>
        <el-form-item label="组件物料编码" prop="materialId">
          <el-input v-model="form.materialId" placeholder="请输入组件物料编码" />
        </el-form-item>
        <el-form-item label="完工仓库" prop="warehouseCode">
          <el-input v-model="form.warehouseCode" placeholder="请输入完工仓库" />
        </el-form-item>
        <el-form-item label="完工货位" prop="locatorCode">
          <el-input v-model="form.locatorCode" placeholder="请输入完工货位" />
        </el-form-item>
        <el-form-item label="MES产线编码" prop="prodLineCode">
          <el-input v-model="form.prodLineCode" placeholder="请输入MES产线编码" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.workOrderStatus">
            <el-radio label="1">请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <el-input v-model="form.quantity" placeholder="请输入数量" />
        </el-form-item>
        <el-form-item label="计划开始时间" prop="planStartTime">
          <el-date-picker clearable size="small"
            v-model="form.planStartTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择计划开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划完成时间" prop="planEndTime">
          <el-date-picker clearable size="small"
            v-model="form.planEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择计划完成时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="产线id" prop="productionLineId">
          <el-input v-model="form.productionLineId" placeholder="请输入产线id" />
        </el-form-item>
        <el-form-item label="总成id" prop="assemblyId">
          <el-input v-model="form.assemblyId" placeholder="请输入总成id" />
        </el-form-item>
        <el-form-item label="工单类型" prop="workOrderType">
          <el-select v-model="form.workOrderType" placeholder="请选择工单类型">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="完工时间" prop="endTime">
          <el-date-picker clearable size="small"
            v-model="form.endTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择完工时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入描述" />
        </el-form-item>
        <!--<el-form-item label="删除标志0正常1删除" prop="deleteFlag">
          <el-input v-model="form.deleteFlag" placeholder="请输入删除标志0正常1删除" />
        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrder, getOrder, delOrder, addOrder, updateOrder, exportOrder, importTemplate } from "@/api/equipment/order";
import { getToken } from "@/utils/auth";

export default {
  name: "Order",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工单表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mesOrderId: null,
        workOrderCode: null,
        plantCode: null,
        itemCode: null,
        materialId: null,
        warehouseCode: null,
        locatorCode: null,
        prodLineCode: null,
        workOrderStatus: null,
        quantity: null,
        planStartTime: null,
        planEndTime: null,
        productionLineId: null,
        assemblyId: null,
        workOrderType: null,
        endTime: null,
        deleteFlag: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        productionLineId: [
          { required: true, message: "产线id不能为空", trigger: "blur" }
        ],
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/equipment/workOrder/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询工单列表 */
    getList() {
      this.loading = true;
      listOrder(this.queryParams).then(response => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        workOrderId: null,
        mesOrderId: null,
        workOrderCode: null,
        plantCode: null,
        itemCode: null,
        materialId: null,
        warehouseCode: null,
        locatorCode: null,
        prodLineCode: null,
        workOrderStatus: 0,
        quantity: null,
        planStartTime: null,
        planEndTime: null,
        productionLineId: null,
        assemblyId: null,
        workOrderType: null,
        endTime: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        deleteFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.workOrderId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const workOrderId = row.workOrderId || this.ids
      getOrder(workOrderId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.workOrderId != null) {
            updateOrder(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOrder(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const workOrderIds = row.workOrderId || this.ids;
      this.$confirm('是否确认删除工单编号为"' + workOrderIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delOrder(workOrderIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有工单数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.exportLoading = true;
          return exportOrder(queryParams);
        }).then(response => {
          this.download(response.msg);
          this.exportLoading = false;
        }).catch(() => {});
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
