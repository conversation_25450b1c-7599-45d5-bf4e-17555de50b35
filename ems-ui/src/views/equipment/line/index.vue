<template>
  <div class="app-container">
	<el-breadcrumb style="margin-bottom: 20px;" separator="/">
		<el-breadcrumb-item>{{factoryNameTemp}}</el-breadcrumb-item>
		<el-breadcrumb-item>{{workshopNameTemp}}</el-breadcrumb-item>
	</el-breadcrumb>  
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="130px">
      <el-form-item label="产线名称" prop="productionLineName">
        <el-input
          v-model="queryParams.productionLineName"
          placeholder="请输入产线名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产线编号" prop="productionLineCode">
        <el-input
          v-model="queryParams.productionLineCode"
          placeholder="请输入产线编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产线主工控机IP" prop="ip">
        <el-input
          v-model="queryParams.ip"
          placeholder="请输入产线主工控机IP"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
		<el-form-item label="条码" prop="barCode">
		  <el-input
			v-model="queryParams.barCode"
			placeholder="请输入条码"
			clearable
			size="small"
			@keyup.enter.native="handleQuery"
		  />
		  </el-form-item>
      <!--<el-form-item label="工厂" prop="workshopId">-->
        <!--<el-select v-model="factoryIdS" placeholder="请选择工厂" clearable>-->
          <!--<el-option-->
            <!--v-for="item in factoryList"-->
            <!--:key="item.factoryId"-->
            <!--:label="item.factoryName"-->
            <!--:value="item.factoryId">-->
          <!--</el-option>-->
        <!--</el-select>-->
      <!--</el-form-item>-->
      <!--<el-form-item label="车间" prop="workshopId">-->
        <!--<el-select v-model="queryParams.workshopId" placeholder="请选择车间" clearable>-->
          <!--<el-option-->
            <!--v-for="item in workshopListS"-->
            <!--:key="item.workshopId"-->
            <!--:label="item.workshopNameTemp"-->
            <!--:value="item.workshopId">-->
          <!--</el-option>-->
        <!--</el-select>-->
      <!--</el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['equipment:line:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['equipment:line:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['equipment:line:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['system:user:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="handleExport"
          v-hasPermi="['equipment:line:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="lineList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="产线ID" align="center" prop="productionLineId" v-if="false"/>
      <el-table-column label="产线名称" align="center" prop="productionLineName" />
      <el-table-column label="产线编号" align="center" prop="productionLineCode" />
      <el-table-column label="产线主工控机IP" align="center" prop="ip" />
	  <el-table-column label="条码" align="center" prop="barCode" />
      <el-table-column label="车间ID" align="center" prop="workshopId" v-if="false"/>
      <el-table-column label="描述" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['equipment:line:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['equipment:line:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleView(scope.row)"
          >查看工位</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改产线管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="产线名称" prop="productionLineName">
          <el-input v-model="form.productionLineName" placeholder="请输入产线名称" />
        </el-form-item>
        <el-form-item label="产线编号" prop="productionLineCode">
          <el-input v-model="form.productionLineCode" placeholder="请输入产线编号" />
        </el-form-item>
        <el-form-item label="产线主工控机IP" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入产线主工控机IP" />
        </el-form-item>
		<el-form-item label="条码" prop="barCode">
		  <el-input v-model="form.barCode" placeholder="请输入条码" />
		</el-form-item>
		
        <!--<el-form-item label="工厂" prop="workshopId">-->
          <!--<el-select v-model="factoryId" placeholder="请选择工厂" clearable>-->
            <!--<el-option-->
              <!--v-for="item in factoryList"-->
              <!--:key="item.factoryId"-->
              <!--:label="item.factoryName"-->
              <!--:value="item.factoryId">-->
            <!--</el-option>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="车间" prop="workshopId">-->
          <!--<el-select v-model="form.workshopId" placeholder="请输入车间" clearable>-->
            <!--<el-option-->
              <!--v-for="item in workshopList"-->
              <!--:key="item.workshopId"-->
              <!--:label="item.workshopNameTemp"-->
              <!--:value="item.workshopId">-->
            <!--</el-option>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <el-form-item label="描述" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLine, getLine, delLine, addLine, updateLine, exportLine, importTemplate } from "@/api/equipment/line";
import { getToken } from "@/utils/auth";
import { listFactory } from "@/api/equipment/factory";
import { listWorkshop } from "@/api/equipment/workshop";

export default {
  name: "Line",
  data() {
    return {
	  //展示工厂名称
	  factoryNameTemp:"",
	  //展示车间名称
	  workshopNameTemp:"",
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 产线管理表格数据
      lineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productionLineName: null,
        productionLineCode: null,
		barCode: null,
        ip: null,
        workshopId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        productionLineName: [
          { required: true, message: "产线名称不能为空", trigger: "blur" }
        ],
        productionLineCode: [
          { required: true, message: "产线编号不能为空", trigger: "blur" }
        ],
        ip: [
          { required: true, message: "产线主工控机IP不能为空", trigger: "blur" }
        ],
        // remark: [
        //   { required: true, message: "描述不能为空", trigger: "blur" }
        // ],
      },
      factoryList: [],
      factoryId: '',
      workshopList: [],
      factoryIdS: '',
      workshopListS: [],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/equipment/productionLine/importData"
      }
    };
  },
  watch: {
    factoryId (ov, nv) {
      listWorkshop({factoryId: nv}).then(response => {
        this.workshopList = response.rows;
      });
    },
    factoryIdS (ov, nv) {
      listWorkshop({factoryId: nv}).then(response => {
        this.workshopListS = response.rows;
      });
    }
  },
  methods: {
    /** 查询产线管理列表 */
    getList() {
      this.loading = true;
      listLine(this.queryParams).then(response => {
        this.lineList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getFactoryList () {
      listFactory().then(response => {
        this.factoryList = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        productionLineId: null,
        productionLineName: null,
        productionLineCode: null,
		barCode: null,
        ip: null,
        workshopId: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.productionLineId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.workshopId = this.workShopData.workshopId
      this.open = true;
      this.title = "添加产线管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const productionLineId = row.productionLineId || this.ids
      getLine(productionLineId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改产线管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.productionLineId != null) {
            updateLine(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLine(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const productionLineIds = row.productionLineId || this.ids;
      this.$confirm('是否确认删除产线管理编号为"' + productionLineIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delLine(productionLineIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有产线管理数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.exportLoading = true;
          return exportLine(queryParams);
        }).then(response => {
          this.download(response.msg);
          this.exportLoading = false;
        }).catch(() => {});
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    handleView(row) {
	  row.factoryNameTemp = this.factoryNameTemp
	  row.workshopNameTemp = this.workshopNameTemp
      this.$router.push({path:'/equipment/station',query: row})
    }
  },
  activated() {
    this.workShopData = this.$route.query
    this.queryParams.workshopId = this.workShopData.workshopId
  	this.workshopNameTemp = this.workShopData.workshopName
  	this.factoryNameTemp = this.workShopData.factoryNameTemp//sessionStorage.getItem("factoryNameTemp")
    this.getList();
    this.getFactoryList()
  },
};
</script>
