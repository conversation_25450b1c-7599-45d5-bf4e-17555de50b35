<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="自定义名称" prop="custName">
        <el-input
          v-model="queryParams.custName"
          placeholder="请输入自定义名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="能源成本中心id(非空)" prop="costCenterId">
        <el-input
          v-model="queryParams.costCenterId"
          placeholder="请输入能源成本中心id(非空)"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="能源介质(非空)" prop="energyId">
        <el-input
          v-model="queryParams.energyId"
          placeholder="请输入能源介质(非空)"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="维护关系" prop="relationExpression">
        <el-input
          v-model="queryParams.relationExpression"
          placeholder="请输入维护关系"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="说明" prop="description">
        <el-input
          v-model="queryParams.description"
          placeholder="请输入说明"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="乐观锁(0禁用,1启用)" prop="optLock">
        <el-input
          v-model="queryParams.optLock"
          placeholder="请输入乐观锁(0禁用,1启用)"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:relat:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:relat:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:relat:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:relat:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="relatList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="维护关系id" align="center" prop="relatId" />
      <el-table-column label="维护类别(1成本中心,2自定义) 有成本中心名称无自定义名称" align="center" prop="keepType" />
      <el-table-column label="自定义名称" align="center" prop="custName" />
      <el-table-column label="能源成本中心id(非空)" align="center" prop="costCenterId" />
      <el-table-column label="能源介质(非空)" align="center" prop="energyId" />
      <el-table-column label="维护关系" align="center" prop="relationExpression" />
      <el-table-column label="说明" align="center" prop="description" />
      <el-table-column label="乐观锁(0禁用,1启用)" align="center" prop="optLock" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:relat:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:relat:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改计量器具和成本中心关系对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="自定义名称" prop="custName">
          <el-input v-model="form.custName" placeholder="请输入自定义名称" />
        </el-form-item>
        <el-form-item label="能源成本中心id(非空)" prop="costCenterId">
          <el-input v-model="form.costCenterId" placeholder="请输入能源成本中心id(非空)" />
        </el-form-item>
        <el-form-item label="能源介质(非空)" prop="energyId">
          <el-input v-model="form.energyId" placeholder="请输入能源介质(非空)" />
        </el-form-item>
        <el-form-item label="维护关系" prop="relationExpression">
          <el-input v-model="form.relationExpression" placeholder="请输入维护关系" />
        </el-form-item>
        <el-form-item label="说明" prop="description">
          <el-input v-model="form.description" placeholder="请输入说明" />
        </el-form-item>
        <el-form-item label="乐观锁(0禁用,1启用)" prop="optLock">
          <el-input v-model="form.optLock" placeholder="请输入乐观锁(0禁用,1启用)" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRelat, getRelat, delRelat, addRelat, updateRelat } from "@/api/system/relat";

export default {
  name: "Relat",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计量器具和成本中心关系表格数据
      relatList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keepType: null,
        custName: null,
        costCenterId: null,
        energyId: null,
        relationExpression: null,
        description: null,
        optLock: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        keepType: [
          { required: true, message: "维护类别(1成本中心,2自定义) 有成本中心名称无自定义名称不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询计量器具和成本中心关系列表 */
    getList() {
      this.loading = true;
      listRelat(this.queryParams).then(response => {
        this.relatList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        relatId: null,
        keepType: null,
        custName: null,
        costCenterId: null,
        energyId: null,
        relationExpression: null,
        description: null,
        optLock: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.relatId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加计量器具和成本中心关系";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const relatId = row.relatId || this.ids
      getRelat(relatId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改计量器具和成本中心关系";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.relatId != null) {
            updateRelat(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRelat(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const relatIds = row.relatId || this.ids;
      this.$modal.confirm('是否确认删除计量器具和成本中心关系编号为"' + relatIds + '"的数据项？').then(function() {
        return delRelat(relatIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/relat/export', {
        ...this.queryParams
      }, `relat_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
