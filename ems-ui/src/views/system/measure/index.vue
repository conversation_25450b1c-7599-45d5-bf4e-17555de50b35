<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="计量对象" prop="meaName">
        <el-input
          v-model="queryParams.meaName"
          placeholder="请输入计量对象"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="祖级列表" prop="ancestors">
        <el-input
          v-model="queryParams.ancestors"
          placeholder="请输入祖级列表"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上一级计量对象" prop="meaParentId">
        <el-input
          v-model="queryParams.meaParentId"
          placeholder="请输入上一级计量对象"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="型号规格" prop="meaSpec">
        <el-input
          v-model="queryParams.meaSpec"
          placeholder="请输入型号规格"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="准确度等级" prop="meaAccuracy">
        <el-input
          v-model="queryParams.meaAccuracy"
          placeholder="请输入准确度等级"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检定周期/校准周期" prop="veriCycle">
        <el-input
          v-model="queryParams.veriCycle"
          placeholder="请输入检定周期/校准周期"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检定单位" prop="veriUnit">
        <el-input
          v-model="queryParams.veriUnit"
          placeholder="请输入检定单位"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检定日期" prop="veriDate">
        <el-date-picker clearable size="small"
          v-model="queryParams.veriDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择检定日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="下次检定日期" prop="nextVeriDate">
        <el-date-picker clearable size="small"
          v-model="queryParams.nextVeriDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择下次检定日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="电流互感器变比" prop="curTranRatio">
        <el-input
          v-model="queryParams.curTranRatio"
          placeholder="请输入电流互感器变比"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="串口地址" prop="serialAddress">
        <el-input
          v-model="queryParams.serialAddress"
          placeholder="请输入串口地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网关地址号" prop="gatewayAddrNum">
        <el-input
          v-model="queryParams.gatewayAddrNum"
          placeholder="请输入网关地址号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网关内起始地址" prop="gatewayStartAddr">
        <el-input
          v-model="queryParams.gatewayStartAddr"
          placeholder="请输入网关内起始地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="能源介质" prop="energyMed">
        <el-input
          v-model="queryParams.energyMed"
          placeholder="请输入能源介质"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="能源成本中心" prop="costCenterId">
        <el-input
          v-model="queryParams.costCenterId"
          placeholder="请输入能源成本中心"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="通讯方式(0无,1有)" prop="commMode">
        <el-input
          v-model="queryParams.commMode"
          placeholder="请输入通讯方式(0无,1有)"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产厂家" prop="manuFact">
        <el-input
          v-model="queryParams.manuFact"
          placeholder="请输入生产厂家"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="证书编号" prop="certId">
        <el-input
          v-model="queryParams.certId"
          placeholder="请输入证书编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检定结果" prop="veriRes">
        <el-input
          v-model="queryParams.veriRes"
          placeholder="请输入检定结果"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安装位置" prop="installAddr">
        <el-input
          v-model="queryParams.installAddr"
          placeholder="请输入安装位置"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电压互感器变比" prop="voltTranRatio">
        <el-input
          v-model="queryParams.voltTranRatio"
          placeholder="请输入电压互感器变比"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="串口回路号" prop="serialLoopNum">
        <el-input
          v-model="queryParams.serialLoopNum"
          placeholder="请输入串口回路号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="网关端口号" prop="gatewayPort">
        <el-input
          v-model="queryParams.gatewayPort"
          placeholder="请输入网关端口号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="乐观锁(0删除,1正常)" prop="optLock">
        <el-input
          v-model="queryParams.optLock"
          placeholder="请输入乐观锁(0删除,1正常)"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:measure:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:measure:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:measure:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:measure:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="measureList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="计量器具id" align="center" prop="meaId" />
      <el-table-column label="计量对象" align="center" prop="meaName" />
      <el-table-column label="祖级列表" align="center" prop="ancestors" />
      <el-table-column label="上一级计量对象" align="center" prop="meaParentId" />
      <el-table-column label="型号规格" align="center" prop="meaSpec" />
      <el-table-column label="准确度等级" align="center" prop="meaAccuracy" />
      <el-table-column label="检定周期/校准周期" align="center" prop="veriCycle" />
      <el-table-column label="检定单位" align="center" prop="veriUnit" />
      <el-table-column label="检定日期" align="center" prop="veriDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.veriDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="下次检定日期" align="center" prop="nextVeriDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.nextVeriDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="电流互感器变比" align="center" prop="curTranRatio" />
      <el-table-column label="串口地址" align="center" prop="serialAddress" />
      <el-table-column label="网关地址号" align="center" prop="gatewayAddrNum" />
      <el-table-column label="网关内起始地址" align="center" prop="gatewayStartAddr" />
      <el-table-column label="能源介质" align="center" prop="energyMed" />
      <el-table-column label="能源成本中心" align="center" prop="costCenterId" />
      <el-table-column label="通讯方式(0无,1有)" align="center" prop="commMode" />
      <el-table-column label="生产厂家" align="center" prop="manuFact" />
      <el-table-column label="状态(0不合格,1合格)" align="center" prop="status" />
      <el-table-column label="证书编号" align="center" prop="certId" />
      <el-table-column label="检定结果" align="center" prop="veriRes" />
      <el-table-column label="安装位置" align="center" prop="installAddr" />
      <el-table-column label="电压互感器变比" align="center" prop="voltTranRatio" />
      <el-table-column label="串口回路号" align="center" prop="serialLoopNum" />
      <el-table-column label="网关端口号" align="center" prop="gatewayPort" />
      <el-table-column label="乐观锁(0删除,1正常)" align="center" prop="optLock" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:measure:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:measure:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改计量器具对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="计量对象" prop="meaName">
          <el-input v-model="form.meaName" placeholder="请输入计量对象" />
        </el-form-item>
        <el-form-item label="上一级计量对象" prop="meaParentId">
          <el-input v-model="form.meaParentId" placeholder="请输入上一级计量对象" />
        </el-form-item>
        <el-form-item label="型号规格" prop="meaSpec">
          <el-input v-model="form.meaSpec" placeholder="请输入型号规格" />
        </el-form-item>
        <el-form-item label="准确度等级" prop="meaAccuracy">
          <el-input v-model="form.meaAccuracy" placeholder="请输入准确度等级" />
        </el-form-item>
        <el-form-item label="检定周期/校准周期" prop="veriCycle">
          <el-input v-model="form.veriCycle" placeholder="请输入检定周期/校准周期" />
        </el-form-item>
        <el-form-item label="检定单位" prop="veriUnit">
          <el-input v-model="form.veriUnit" placeholder="请输入检定单位" />
        </el-form-item>
        <el-form-item label="检定日期" prop="veriDate">
          <el-date-picker clearable size="small"
            v-model="form.veriDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择检定日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="下次检定日期" prop="nextVeriDate">
          <el-date-picker clearable size="small"
            v-model="form.nextVeriDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择下次检定日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="电流互感器变比" prop="curTranRatio">
          <el-input v-model="form.curTranRatio" placeholder="请输入电流互感器变比" />
        </el-form-item>
        <el-form-item label="串口地址" prop="serialAddress">
          <el-input v-model="form.serialAddress" placeholder="请输入串口地址" />
        </el-form-item>
        <el-form-item label="网关地址号" prop="gatewayAddrNum">
          <el-input v-model="form.gatewayAddrNum" placeholder="请输入网关地址号" />
        </el-form-item>
        <el-form-item label="网关内起始地址" prop="gatewayStartAddr">
          <el-input v-model="form.gatewayStartAddr" placeholder="请输入网关内起始地址" />
        </el-form-item>
        <el-form-item label="能源介质" prop="energyMed">
          <el-input v-model="form.energyMed" placeholder="请输入能源介质" />
        </el-form-item>
        <el-form-item label="能源成本中心" prop="costCenterId">
          <el-input v-model="form.costCenterId" placeholder="请输入能源成本中心" />
        </el-form-item>
        <el-form-item label="通讯方式(0无,1有)" prop="commMode">
          <el-input v-model="form.commMode" placeholder="请输入通讯方式(0无,1有)" />
        </el-form-item>
        <el-form-item label="生产厂家" prop="manuFact">
          <el-input v-model="form.manuFact" placeholder="请输入生产厂家" />
        </el-form-item>
        <el-form-item label="证书编号" prop="certId">
          <el-input v-model="form.certId" placeholder="请输入证书编号" />
        </el-form-item>
        <el-form-item label="检定结果" prop="veriRes">
          <el-input v-model="form.veriRes" placeholder="请输入检定结果" />
        </el-form-item>
        <el-form-item label="安装位置" prop="installAddr">
          <el-input v-model="form.installAddr" placeholder="请输入安装位置" />
        </el-form-item>
        <el-form-item label="电压互感器变比" prop="voltTranRatio">
          <el-input v-model="form.voltTranRatio" placeholder="请输入电压互感器变比" />
        </el-form-item>
        <el-form-item label="串口回路号" prop="serialLoopNum">
          <el-input v-model="form.serialLoopNum" placeholder="请输入串口回路号" />
        </el-form-item>
        <el-form-item label="网关端口号" prop="gatewayPort">
          <el-input v-model="form.gatewayPort" placeholder="请输入网关端口号" />
        </el-form-item>
        <el-form-item label="乐观锁(0删除,1正常)" prop="optLock">
          <el-input v-model="form.optLock" placeholder="请输入乐观锁(0删除,1正常)" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMeasure, getMeasure, delMeasure, addMeasure, updateMeasure } from "@/api/system/measure";

export default {
  name: "Measure",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计量器具表格数据
      measureList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        meaName: null,
        ancestors: null,
        meaParentId: null,
        meaSpec: null,
        meaAccuracy: null,
        veriCycle: null,
        veriUnit: null,
        veriDate: null,
        nextVeriDate: null,
        curTranRatio: null,
        serialAddress: null,
        gatewayAddrNum: null,
        gatewayStartAddr: null,
        energyMed: null,
        costCenterId: null,
        commMode: null,
        manuFact: null,
        status: null,
        certId: null,
        veriRes: null,
        installAddr: null,
        voltTranRatio: null,
        serialLoopNum: null,
        gatewayPort: null,
        optLock: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询计量器具列表 */
    getList() {
      this.loading = true;
      listMeasure(this.queryParams).then(response => {
        this.measureList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        meaId: null,
        meaName: null,
        ancestors: null,
        meaParentId: null,
        meaSpec: null,
        meaAccuracy: null,
        veriCycle: null,
        veriUnit: null,
        veriDate: null,
        nextVeriDate: null,
        curTranRatio: null,
        serialAddress: null,
        gatewayAddrNum: null,
        gatewayStartAddr: null,
        energyMed: null,
        costCenterId: null,
        commMode: null,
        manuFact: null,
        status: "0",
        certId: null,
        veriRes: null,
        installAddr: null,
        voltTranRatio: null,
        serialLoopNum: null,
        gatewayPort: null,
        optLock: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.meaId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加计量器具";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const meaId = row.meaId || this.ids
      getMeasure(meaId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改计量器具";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.meaId != null) {
            updateMeasure(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMeasure(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const meaIds = row.meaId || this.ids;
      this.$modal.confirm('是否确认删除计量器具编号为"' + meaIds + '"的数据项？').then(function() {
        return delMeasure(meaIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/measure/export', {
        ...this.queryParams
      }, `measure_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
