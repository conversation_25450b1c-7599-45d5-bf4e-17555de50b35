<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="成本中心名称" prop="centerName">
        <el-input
          v-model="queryParams.centerName"
          placeholder="请输入成本中心名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="祖级列表" prop="ancestors">
        <el-input
          v-model="queryParams.ancestors"
          placeholder="请输入祖级列表"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="成本中心编码" prop="centerCode">
        <el-input
          v-model="queryParams.centerCode"
          placeholder="请输入成本中心编码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上一级成本中心id" prop="parentCenterId">
        <el-input
          v-model="queryParams.parentCenterId"
          placeholder="请输入上一级成本中心id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="乐观锁(0删除,1正常)" prop="optLock">
        <el-input
          v-model="queryParams.optLock"
          placeholder="请输入乐观锁(0删除,1正常)"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:center:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:center:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:center:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:center:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="centerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="成本中心id" align="center" prop="centerId" />
      <el-table-column label="成本中心名称" align="center" prop="centerName" />
      <el-table-column label="祖级列表" align="center" prop="ancestors" />
      <el-table-column label="成本中心编码" align="center" prop="centerCode" />
      <el-table-column label="上一级成本中心id" align="center" prop="parentCenterId" />
      <el-table-column label="系统类别(1附属系统,2生产系统,3辅助系统)" align="center" prop="sysType" />
      <el-table-column label="启用状态(0禁用,1启用)" align="center" prop="useStatus" />
      <el-table-column label="乐观锁(0删除,1正常)" align="center" prop="optLock" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:center:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:center:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改成本中心对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="成本中心名称" prop="centerName">
          <el-input v-model="form.centerName" placeholder="请输入成本中心名称" />
        </el-form-item>
        <el-form-item label="成本中心编码" prop="centerCode">
          <el-input v-model="form.centerCode" placeholder="请输入成本中心编码" />
        </el-form-item>
        <el-form-item label="上一级成本中心id" prop="parentCenterId">
          <el-input v-model="form.parentCenterId" placeholder="请输入上一级成本中心id" />
        </el-form-item>
        <el-form-item label="乐观锁(0删除,1正常)" prop="optLock">
          <el-input v-model="form.optLock" placeholder="请输入乐观锁(0删除,1正常)" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCenter, getCenter, delCenter, addCenter, updateCenter } from "@/api/system/center";

export default {
  name: "Center",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 成本中心表格数据
      centerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        centerName: null,
        ancestors: null,
        centerCode: null,
        parentCenterId: null,
        sysType: null,
        useStatus: null,
        optLock: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询成本中心列表 */
    getList() {
      this.loading = true;
      listCenter(this.queryParams).then(response => {
        this.centerList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        centerId: null,
        centerName: null,
        ancestors: null,
        centerCode: null,
        parentCenterId: null,
        sysType: null,
        useStatus: "0",
        optLock: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.centerId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加成本中心";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const centerId = row.centerId || this.ids
      getCenter(centerId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改成本中心";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.centerId != null) {
            updateCenter(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCenter(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const centerIds = row.centerId || this.ids;
      this.$modal.confirm('是否确认删除成本中心编号为"' + centerIds + '"的数据项？').then(function() {
        return delCenter(centerIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/center/export', {
        ...this.queryParams
      }, `center_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
