<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import resize from './mixins/resize';

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    stack: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(this.$el)
      this.setOptions(this.chartData)
    },
    setOptions(dataSet= [["时间"]]) {
      let series = [];
      let count = dataSet.length>1?dataSet[0].length-1:0;
      for (let i = 0; i < count; i++){
        if (this.stack){
          series.push({type:'bar', stack:'ad'});
        }else{
          series.push({type:'bar'});
        }

      }
      this.chart.setOption({
        label:{
          show: true,
          position: 'top',
        },
        xAxis: {
          type:'category',
          // boundaryGap: false,
          axisTick: {
            show: false
          }
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          axisTick: {
            show: false
          }
        },
        series: series,
        dataset:{
          source: dataSet,
        },
        legend:{
          show: series.length>1,
        },
        title:{
          text:this.title,
        }
      })
    }
  }
}
</script>
