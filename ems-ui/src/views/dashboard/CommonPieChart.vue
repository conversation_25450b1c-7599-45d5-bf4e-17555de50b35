<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    chartData: {
      required: true,
      default: [[]]
    },
    title: {
      type: String,
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch:{
    chartData:{
      handler(){
        this.setOption();
      },
      deep: true,
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    setOption(){
      this.chart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{c} ({d}%)'
        },
        legend: {
          left: 'center',
          bottom: '10',
        },
        series: [
          {
            type: 'pie',
            roseType: 'radius',
            radius: [15, '60%'],
            center: ['50%', '50%'],
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ],
        dataset:{
          source: this.chartData,
        },
        title:{
          text: this.title,
        }
      });
    },
    initChart() {
      this.chart = this.$echarts.init(this.$el)
      this.setOption();

    }
  }
}
</script>
