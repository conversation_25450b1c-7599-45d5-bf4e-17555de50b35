<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    stack: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(this.$el)
      this.setOptions(this.chartData)
    },
    setOptions(dataSet= [["时间"]]) {
      let series = [];
      let count = dataSet.length>1?dataSet[0].length-1:0;
      for (let i = 0; i < count; i++){
        let ser = {type:'bar',
          label: {
            show: true,
            position: 'right',
            valueAnimation: true,
            formatter: (params)=>{
              return params.value[params.encode.x[0]].toFixed(2);
            },

          },
          tooltip:{
            valueFormatter: function (value) {
              return value.toFixed(2);
            }
          }
        }
        if (this.stack){
          ser.stack='total';
        }
        series.push(ser);
      }
      this.chart.setOption({
        xAxis: {
          type:'value',
          // boundaryGap: false,
          axisTick: {
            show: false
          }
        },
        grid: {
          left: 10,
          right: 30,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          type:'category',
          inverse: true,
          axisTick: {
            show: false
          }
        },
        series: series,
        legend:{
          show: true
        },
        dataset:{
          source: dataSet
        },
        title:{
          text:this.title,
        }
      })
    }
  }
}
</script>
