<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="产线零件号" prop="partNo">
        <el-input
          v-model="queryParams.partNo"
          placeholder="请输入产线零件号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="partName">
        <el-input
          v-model="queryParams.partName"
          placeholder="请输入产品名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="特征码" prop="pattern">
        <el-input
          v-model="queryParams.pattern"
          placeholder="请输入特征码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标签" prop="tag">
        <el-input
          v-model="queryParams.tag"
          placeholder="请输入标签"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
	    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['energy:part:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="partList"
      row-key="partId"
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column label="产线零件号" prop="partNo" />
      <el-table-column label="产品名称" align="center" prop="partName" />
      <el-table-column label="特征码" align="center" prop="pattern" />
      <el-table-column label="用量" align="center" prop="useCount" />
      <el-table-column label="当量" align="center" prop="equivalent" />
      <el-table-column label="标准碳排放（Kg）" align="center" prop="processCount" />
      <el-table-column label="标签" align="center" prop="tag" >
        <template slot-scope="scope">
          <el-tag :key="tag" v-for="tag in scope.row.tag?scope.row.tag.split(','):[]">{{tag}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['energy:part:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
            v-hasPermi="['energy:part:add']"
          >新增</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['energy:part:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改产品管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="产线零件号" prop="partNo">
          <el-input v-model="form.partNo" placeholder="请输入产线零件号" />
        </el-form-item>
        <el-form-item label="产品名称" prop="partName">
          <el-input v-model="form.partName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="特征码" prop="pattern">
          <el-input v-model="form.pattern" placeholder="请输入特征码" />
        </el-form-item>
        <el-form-item label="用量" prop="pattern">
          <el-input-number v-model="form.useCount" placeholder="请输入用量" :controls="false"/>
        </el-form-item>
        <el-form-item label="当量" prop="pattern">
          <el-input-number v-model="form.equivalent" placeholder="请输入当量"  :controls="false"/>
        </el-form-item>
        <el-form-item label="标准碳排放（Kg）" prop="pattern">
          <el-input-number v-model="form.processCount" placeholder="请输入标准碳排放（Kg）"  :controls="false"/>
        </el-form-item>
        <el-form-item label="上级产品" prop="parentId">
          <treeselect v-model="form.parentId" :options="partOptions" :normalizer="normalizer" placeholder="请选择上级产品" searchable/>
        </el-form-item>
        <el-form-item label="标签" prop="tag">
          <el-input-tag v-model="form.tagList" placeholder="请输入标签" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {getPart, listPart, addPart, updatePart, delPart} from "@/api/energy/part";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";

  export default {
  name: "Part",
  components: {
    Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 产品管理表格数据
      partList: [],
      // 产品管理树选项
      partOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        partNo: null,
        partName: null,
        pattern: null,
        tag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询产品管理列表 */
    getList() {
      this.loading = true;
      listPart(this.queryParams).then(response => {
        this.partList = this.handleTree(response.data, "partId", "parentId");
        this.loading = false;
      });
    },
    /** 转换产品管理数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.partId,
        label: node.partName,
        children: node.children,
        isDisabled: node.partId === this.form.partId
      };
    },
	/** 查询产品管理下拉树结构 */
    getTreeselect() {
      listPart().then(response => {
        // this.partOptions = [];
        // const data = { partId: 0, partName: '产品', children: [] };
        // data.children = this.handleTree(response.data, "partId", "parentId");
        this.partOptions = this.handleTree(response.data, "partId", "parentId");
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        partId: null,
        partNo: null,
        partName: null,
        pattern: null,
        useCount: null,
        equivalent: null,
        processCount: null,
        parentId: null,
        tag: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        tagList:[]
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != null && row.partId) {
        this.form.parentId = row.partId;
      } else {
        this.form.parentId = null;
      }
      this.open = true;
      this.title = "添加产品管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      if (row != null) {
        this.form.parentId = row.partId;
      }
      getPart(row.partId).then(response => {
        this.form = response.data;
        this.form.tagList = this.form.tag?this.form.tag.split(','):[];
        this.open = true;
        this.title = "修改产品管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.tag = this.form.tagList.join(',');
          if (this.form.partId != null) {
            updatePart(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPart(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除产品管理编号为"' + row.partId + '"的数据项？').then(function() {
        return delPart(row.partId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
