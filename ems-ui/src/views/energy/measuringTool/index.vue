<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="表具编号" prop="measuringToolCode">
        <el-input
          v-model="queryParams.measuringToolCode"
          placeholder="请输入表具编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="表具名称" prop="measuringToolName">
        <el-input
          v-model="queryParams.measuringToolName"
          placeholder="请输入表具名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标签" prop="tag">
        <el-input
          v-model="queryParams.tag"
          placeholder="请输入标签"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="表具类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择表具类型" clearable size="small">
          <el-option
            v-for="dict in dict.type.measuring_tool_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
	    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['energy:measuringTool:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="measuringToolList"
      row-key="measuringToolId"
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column label="表具编号" prop="measuringToolCode" />
      <el-table-column label="表具名称" align="center" prop="measuringToolName" />
      <el-table-column label="标签" align="center" prop="tag" >
        <template slot-scope="scope">
          <el-tag :key="tag" v-for="tag in scope.row.tag?scope.row.tag.split(','):[]">{{tag}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="表具类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.measuring_tool_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['energy:measuringTool:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
            v-hasPermi="['energy:measuringTool:add']"
            v-if="scope.row.type!=='virtual'"
          >新增</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['energy:measuringTool:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改表具对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" :inline="true">
        <el-form-item label="表具编号" prop="measuringToolCode">
          <el-input v-model="form.measuringToolCode" placeholder="请输入表具编号" />
        </el-form-item>
        <el-form-item label="表具名称" prop="measuringToolName">
          <el-input v-model="form.measuringToolName" placeholder="请输入表具名称" />
        </el-form-item>
        <el-form-item label="标签" prop="tag">
          <el-input-tag v-model="form.tagList" placeholder="请输入标签" />
        </el-form-item>
        <el-form-item label="表具类型" prop="type">
          <el-select v-model="form.type" default-first-option placeholder="请选择表具类型" :disabled="null != form.measuringToolId">
            <el-option
              v-for="dict in dict.type.measuring_tool_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采集器" prop="sampler" v-if="isEditShow('sampler')">
          <el-input v-model="form.sampler" placeholder="请输入采集器" />
        </el-form-item>
        <el-form-item label="成本中心" prop="costCenter">
          <treeselect v-model="form.costCenterId"  :options="costCenterList" :normalizer="costCenterNormalizer"  placeholder="成本中心" searchable style="width: 200px"/>
        </el-form-item>
        <el-form-item label="检定日期" prop="checkDate" v-if="isEditShow('checkDate')">
          <el-date-picker clearable size="small"
                          v-model="form.checkDate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="选择检定日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检定单位" prop="checkOrg" v-if="isEditShow('checkOrg')">
          <el-input v-model="form.checkOrg" placeholder="请输入检定单位" />
        </el-form-item>
        <el-form-item label="证书编号" prop="certCode" v-if="isEditShow('certCode')">
          <el-input v-model="form.certCode" placeholder="请输入证书编号" />
        </el-form-item>
        <el-form-item label="安装位置" prop="position" v-if="isEditShow('position')">
          <el-input v-model="form.position" placeholder="请输入安装位置" />
        </el-form-item>
        <el-form-item label="下次检定日期" prop="nextCheckDate" v-if="isEditShow('nextCheckDate')">
          <el-date-picker clearable size="small"
                          v-model="form.nextCheckDate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="选择下次检定日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="上级表具" prop="parentId" >
          <treeselect v-model="form.parentId" :options="measuringToolOptions" :normalizer="normalizer" placeholder="请选择上级表具" searchable style="width: 200px"/>
        </el-form-item>
<!--        <el-form-item label="计算公式" prop="virtualExpression" v-if="isEditShow('virtualExpression')" >-->
<!--          <el-input v-model="form.virtualExpression" placeholder="请输入计算公式" readonly />-->
<!--        </el-form-item>-->
        <el-form-item label="变量" prop="virtualVariable" v-if="isEditShow('virtualVariable')" :rules="form.type==='virtual'?rules.virtualVariable:[{required:false}]">
          <el-select v-model="form.virtualVariable" placeholder="变量" default-first-option size="small" >
            <el-option
              v-for="dict in dict.type.virtual_variable"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-card shadow="never" class="border-none margin-t24" v-if="form.type==='virtual'">
          <div slot="header">
            计算关系
          </div>
          <el-button type="primary"  v-for="(item, i) in symbols" :key="i" @click.stop="symbolAdd(item)" style="margin-top: 5px;margin-bottom: 5px">{{item}}</el-button>
          <el-button type="danger" @click="toolAdd">+表具</el-button>
          <el-button type="danger" @click="backspace">退格</el-button>

        </el-card>
<!--        <el-divider/>-->
        <div style="display: flex;flex-wrap: wrap">
          <div v-for="(item, index) in this.form.recordList" :key="index" style="display: flex">
            <!-- @click.native="seleadd()" -->
            <treeselect  v-model="item.data"  :value="item.id" :options="measuringToolOptions" :normalizer="normalizer"    placeholder="请选择"  @input="seleadd(item.data,index)" style="margin-left: 10px;width:200px;margin-top:10px;" :flat="true"/>
            <!-- <span class="selectFuhao">{{item.symbol}}</span> -->
            <b style="margin-left: 10px;text-align: center; height: 50px; line-height: 50px;">{{item.symbol}}</b>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {getMeasuringTool, listMeasuringTool,addMeasuringTool, updateMeasuringTool, listRealMeasuringTool, delMeasuringTool} from "@/api/energy/measuringTool";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
  import {getCcostCenter} from "@/api/enterprise/utensil";

  export default {
  name: "MeasuringTool",
  dicts: ['measuring_tool_type', 'virtual_variable'],
  components: {
    Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表具表格数据
      measuringToolList: [],
      // 表具树选项
      measuringToolOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        measuringToolCode: null,
        measuringToolName: null,
        tag: null,
        type: null,
        virtualVariable: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        measuringToolCode: [
          { required: true, message: "请输入表具编号", trigger: "blur" }
        ],
        measuringToolName: [
          { required: true, message: "请输入表具名称", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "请选择表具类型", trigger: "blur" }
        ],
        virtualVariable: [
          { required: true, message: "请选择变量", trigger: "blur" }
        ],
      },
      virtualShow: {
        virtualExpression: 1,
        virtualVariable:1
      },
      symbols:["+","-","*","/",".","(",")","0","1","2","3","4","5","6","7","8","9"],
      numberarr:["0","1","2","3","4","5","6","7","8","9"],
      costCenterList:[],
    };
  },
  created() {
    this.getList();
    this.getCostCenterList();
  },
  methods: {
    /** 查询表具列表 */
    getList() {
      this.loading = true;
      listMeasuringTool(this.queryParams).then(response => {
        this.measuringToolList = this.handleTree(response.data, "measuringToolId", "parentId");
        this.loading = false;
      });
    },
    isEditShow(property){
      if(this.form.type === 'virtual'){
        return this.virtualShow.hasOwnProperty(property) && this.virtualShow[property] === 1;
      }else{
        return !(this.virtualShow.hasOwnProperty(property) && this.virtualShow[property] === 1);
      }

    },
    getCostCenterList(){
      getCcostCenter().then(response=>{
        // console.log(response)
        this.costCenterList=response.data;
      })
    },
    /** 转换表具数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.measuringToolId,
        label: node.measuringToolName,
        children: node.children,
        isDisabled: node.measuringToolId === this.form.measuringToolId
      };
    },
    costCenterNormalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children
      };
    },
	/** 查询表具下拉树结构 */
    getTreeselect() {
    listMeasuringTool().then(response => {
        this.measuringToolOptions = this.handleTree(response.data, "measuringToolId", "parentId");
        // const data = { measuringToolId: 0, measuringToolCode: '表具', children: [] };
        // data.children = this.handleTree(response.data, "measuringToolId", "parentId");
        // this.measuringToolOptions.push(data);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        measuringToolId: null,
        measuringToolCode: null,
        measuringToolName: null,
        tag: null,
        type: null,
        sampler: null,
        samplerModel: null,
        costCenterId: null,
        checkDate: null,
        checkOrg: null,
        certCode: null,
        position: null,
        nextCheckDate: null,
        parentId: null,
        virtualExpression: null,
        virtualJson: null,
        virtualVariable: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        tagList:[],
        recordList:[],
        numberList:[],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != null && row.measuringToolId) {
        this.form.parentId = row.measuringToolId;
      } else {
        this.form.parentId = null;
      }
      this.open = true;
      this.title = "添加表具";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      if (row != null) {
        this.form.parentId = row.measuringToolId;
      }
      getMeasuringTool(row.measuringToolId).then(response => {
        this.form = response.data;
        this.form.tagList = this.form.tag?this.form.tag.split(','):[];
        this.form.recordList = this.form.virtualJson?JSON.parse(this.form.virtualJson):[];
        this.open = true;
        this.title = "修改表具";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.tag = this.form.tagList.join(',');
          if (this.form.measuringToolId != null) {
            updateMeasuringTool(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMeasuringTool(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除表具编号为"' + row.measuringToolCode + '"的数据项？').then(function() {
        return delMeasuringTool(row.measuringToolId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    symbolAdd(single) {
      // console.log(single)
      //判断之前的记录是否存在
      if(this.form.recordList.length){
        //判断这个数组真最后是否有符号
        if(this.form.recordList[this.form.recordList.length-1].symbol){

          this.form.recordList[this.form.recordList.length-1].symbol+=single
          let result = this.numberarr.find(v => v === single);
          // console.log(result)
          if(result){
            this.form.virtualExpression+="%d"
            this.form.numberList.push(single)
          }else{
            this.form.virtualExpression+=single
          }
        }else{

          this.$set(this.form.recordList[this.form.recordList.length-1], 'symbol', single)
          let result = this.numberarr.find(v => v === single);
          if(result){
            // console.log(single)
            this.form.virtualExpression+="%d"
            this.form.numberList.push(single)
          }else{
            this.form.virtualExpression+=single
          }
        }
      }else{
        return this.$message.error('还没有选择电表呢');
      }

      //this.form.relationExpression+=single
      //this.form.relationExpression+="%d"
      // console.log(this.form.relationExpression)
      // console.log(this.form.numberList);
    },
    toolAdd() {
      // console.log(this.form)
      if(this.form.recordList.length===0){
        this.form.recordList.push({data:null,symbol:null});
      }else{
        if(this.form.recordList[this.form.recordList.length-1].symbol){
          this.form.recordList.push({data:null,symbol:null});
        }else{
          return this.$message.error('请先选择关系再追加电表');
        }
      }

    },
    seleadd(item,index){
      this.form.virtualExpression=""
      this.form.recordList[index].data=item
      this.form.recordList.forEach((item,index)=>{
        let symbolString = String(item.symbol);
        symbolString = symbolString.replace(/(\d)/g, '%d');
        console.log(symbolString);
        let  recordnumber=String(item.data)+symbolString;
        var reg=new RegExp("null","g"); //创建正则RegExp对象
        // console.log(recordnumber.split("unll")[0].replace(reg,""))
        this.form.virtualExpression+=recordnumber.split("undefined")[0].replace(reg,"")
      })
      //console.log(recordnumber)
      // console.log(index)
      // console.log(item)
      // console.log(this.form.virtualExpression);
      //  this.$data.stringarr.map((item,index)=>{
      //    if(this.form.relationExpression.lastIndexOf(item)!==-1){
      //      var recordnumber=this.form.relationExpression.lastIndexOf(item)
      //      this.form.relationExpression=this.form.relationExpression.slice(0,recordnumber+1)
      //    }
      //  })
      //  this.form.relationExpression+=item
    },
    backspace(){
      if(this.form.recordList.length>0){
        let last = this.form.recordList[this.form.recordList.length-1];
        if (last.symbol){
          let numbers = last.symbol.match(/\d/g)
          if (numbers && numbers.length>0){
            this.form.numberList = this.form.numberList.slice(0, this.form.numberList.length - numberCount);
          }
          last.symbol = null;
        }else{
          this.form.recordList.pop();
        }
        this.form.virtualExpression=""
        this.form.recordList.forEach((item,index)=>{
          let symbolString = String(item.symbol);
          symbolString = symbolString.replace(/(\d)/g, '%d');
          console.log(symbolString);
          let  recordnumber=String(item.data)+symbolString;
          var reg=new RegExp("null","g"); //创建正则RegExp对象
          // console.log(recordnumber.split("unll")[0].replace(reg,""))
          this.form.virtualExpression+=recordnumber.split("undefined")[0].replace(reg,"")
        })

      }
    }
  },

};
</script>
