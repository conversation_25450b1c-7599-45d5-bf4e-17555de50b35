<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="设备编号" prop="equipmentCode">
        <el-input
          v-model="queryParams.equipmentCode"
          placeholder="请输入设备编号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备名称" prop="equipmentName">
        <el-input
          v-model="queryParams.equipmentName"
          placeholder="请输入设备名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备型号" prop="model">
        <el-input
          v-model="queryParams.model"
          placeholder="请输入设备型号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备品牌" prop="brand">
        <el-input
          v-model="queryParams.brand"
          placeholder="请输入设备品牌"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="维护时间">
        <el-date-picker
          v-model="daterangeMaintenanceDate"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="维护周期" prop="maintenacePeriod">
        <el-input
          v-model="queryParams.maintenacePeriod"
          placeholder="请输入维护周期"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标签" prop="tag">
        <el-input
          v-model="queryParams.tag"
          placeholder="请输入标签"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备类型" prop="equipmentType">
        <el-select v-model="queryParams.equipmentType" placeholder="请选择设备类型" clearable size="small">
          <el-option
            v-for="dict in dict.type.equipment_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
	    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['energy:equipment:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="equipmentList"
      row-key="equipmentId"
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column label="设备编号" prop="equipmentCode" />
      <el-table-column label="设备名称" align="center" prop="equipmentName" />
      <el-table-column label="设备型号" align="center" prop="model" />
      <el-table-column label="设备品牌" align="center" prop="brand" />
      <el-table-column label="维护时间" align="center" prop="maintenanceDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.maintenanceDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="维护周期" align="center" prop="maintenacePeriod" />
      <el-table-column label="设备类型" align="center" prop="equipmentType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.equipment_type" :value="scope.row.equipmentType"/>
        </template>
      </el-table-column>
      <el-table-column label="标签" align="center" prop="tag" >
        <template slot-scope="scope">
          <el-tag :key="tag" v-for="tag in scope.row.tag?scope.row.tag.split(','):[]">{{tag}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['energy:equipment:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
            v-hasPermi="['energy:equipment:add']"
          >新增</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['energy:equipment:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改设备管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" :inline="true">
        <el-form-item label="设备编号" prop="equipmentCode">
          <el-input v-model="form.equipmentCode" placeholder="请输入设备编号" />
        </el-form-item>
        <el-form-item label="设备名称" prop="equipmentName">
          <el-input v-model="form.equipmentName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备型号" prop="model">
          <el-input v-model="form.model" placeholder="请输入设备型号" />
        </el-form-item>
        <el-form-item label="设备品牌" prop="brand">
          <el-input v-model="form.brand" placeholder="请输入设备品牌" />
        </el-form-item>
        <el-form-item label="维护时间" prop="maintenanceDate">
          <el-date-picker clearable size="small"
            v-model="form.maintenanceDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择维护时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="维护周期" prop="maintenacePeriod">
          <el-input v-model="form.maintenacePeriod" placeholder="请输入维护周期" />
        </el-form-item>
        <el-form-item label="设备类型" prop="equipmentType">
          <el-select v-model="form.equipmentType" placeholder="请选择设备类型" @change="getSpecConfigDict">
            <el-option
              v-for="dict in dict.type.equipment_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上级设备" prop="parentId">
          <treeselect v-model="form.parentId" :options="equipmentOptions" :normalizer="normalizer" placeholder="请选择上级设备" searchable style="width: 200px"/>
        </el-form-item>
        <el-form-item label="成本中心" prop="costCenterId">
          <treeselect v-model="form.costCenterId"  :options="costCenterList" :normalizer="costCenterNormalizer"  placeholder="成本中心" searchable style="width: 200px"/>
        </el-form-item>
        <el-form-item label="标签" prop="tag">
          <el-input-tag v-model="form.tagList" placeholder="请输入标签" />
        </el-form-item>
<!--        <el-form-item label="表具配置" prop="measuringToolConfig">x-->
<!--          <el-input v-model="form.measuringToolConfig" placeholder="请输入表具配置" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="备注" prop="remark">-->
<!--          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />-->
<!--        </el-form-item>-->
        <el-divider content-position="left">通用参数采集表具</el-divider>
        <el-form-item v-for="(config, index) in dict.type.common_measurement_config" :key="'common'+index" :label="config.label">
          <treeselect v-model="form.config[config.value]" :options="measuringToolOptions" :normalizer="measuringTollNormalizer" placeholder="请选择" searchable style="width: 200px"/>
        </el-form-item>
        <el-divider content-position="left">特别参数采集表具</el-divider>
        <el-form-item v-for="(config, m) in specConfigDict" :key="'spec'+m" :label="config.dictLabel">
          <treeselect v-model="form.config[config.dictValue]" :options="measuringToolOptions" :normalizer="measuringTollNormalizer" placeholder="请选择" searchable style="width: 200px"/>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {getEquipment, listEquipment, addEquipment, updateEquipment, delEquipment} from "@/api/energy/equipment";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
  import {getCcostCenter} from "@/api/enterprise/utensil";
  import {listMeasuringTool} from "@/api/energy/measuringTool";
  import {getDicts} from "@/api/system/dict/data";

  export default {
  name: "Equipment",
  dicts: ['equipment_type', 'common_measurement_config'],
  components: {
    Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 设备管理表格数据
      equipmentList: [],
      // 设备管理树选项
      equipmentOptions: [],
      costCenterList:[],
      measuringToolOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangeMaintenanceDate: [],
      specConfigDict:[],
      // 查询参数
      queryParams: {
        equipmentCode: null,
        equipmentName: null,
        model: null,
        brand: null,
        maintenanceDate: null,
        maintenacePeriod: null,
        equipmentType:null,
        tag: null,
      },
      // 表单参数
      form: {
        config:{}
      },
      // 表单校验
      rules: {
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getCostCenterList();
  },
  methods: {
    /** 查询设备管理列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeMaintenanceDate && '' != this.daterangeMaintenanceDate) {
        this.queryParams.params["beginMaintenanceDate"] = this.daterangeMaintenanceDate[0];
        this.queryParams.params["endMaintenanceDate"] = this.daterangeMaintenanceDate[1];
      }
      listEquipment(this.queryParams).then(response => {
        this.equipmentList = this.handleTree(response.data, "equipmentId", "parentId");
        this.loading = false;
      });
    },
    /** 转换设备管理数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.equipmentId,
        label: node.equipmentName,
        children: node.children,
        isDisabled: node.equipmentId === this.form.equipmentId
      };
    },
    measuringTollNormalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.measuringToolId,
        label: node.measuringToolName,
        children: node.children
      };
    },
	/** 查询设备管理下拉树结构 */
    getTreeselect() {
      listEquipment().then(response => {
        this.equipmentOptions = this.handleTree(response.data, "equipmentId", "parentId");
        // const data = { equipmentId: 0, equipmentName: '顶级节点', children: [] };
        // data.children = this.handleTree(response.data, "equipmentId", "equipmentId");
        // this.equipmentOptions.push(data);
      });
      listMeasuringTool().then(response=>{
        this.measuringToolOptions = this.handleTree(response.data, "measuringToolId", "parentId");
      });
    },
    getCostCenterList(){
      getCcostCenter().then(response=>{
        // console.log(response)
        this.costCenterList=response.data;
      })
    },
    costCenterNormalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        equipmentId: null,
        equipmentCode: null,
        equipmentName: null,
        model: null,
        brand: null,
        maintenanceDate: null,
        maintenacePeriod: null,
        parentId: null,
        costCenterId: null,
        tag: null,
        measuringToolConfig: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        tagList:[],
        config:{},
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeMaintenanceDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != null && row.equipmentId) {
        this.form.equipmentId = row.equipmentId;
      } else {
        this.form.equipmentId = null;
      }
      this.open = true;
      this.title = "添加设备";
    },
    getDictType(equipmentType){
      return equipmentType + '_measurement_config';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      if (row != null) {
        this.form.equipmentId = row.equipmentId;
      }
      getEquipment(row.equipmentId).then(response => {
        this.form = response.data;
        this.form.tagList = this.form.tag?this.form.tag.split(','):[];
        this.form.config = this.form.measuringToolConfig?JSON.parse(this.form.measuringToolConfig):{};
        this.getSpecConfigDict(this.form.equipmentType);
        this.open = true;
        this.title = "修改设备";
      });
    },
    getSpecConfigDict(equipmentType){
      console.log('test');
      if(equipmentType){
        let dictType = this.getDictType(equipmentType);
        getDicts(dictType).then(res => {
          this.specConfigDict = res.data;
        });
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.tag = this.form.tagList.join(',');
          this.form.measuringToolConfig = JSON.stringify(this.form.config);
          if (this.form.equipmentId != null) {
            updateEquipment(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEquipment(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除设备管理编号为"' + row.equipmentCode + '"的数据项？').then(function() {
        return delEquipment(row.equipmentId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
