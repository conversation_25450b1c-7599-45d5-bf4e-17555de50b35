<script>
export default {
  name: "grafana",
  data() {
    return {
      // iframeUrl: '/grafanaFrameF/d/TV1-eWSGk/php-all?orgId=1',
      iframeUrl: this.$route.query.iframeUrl,
      task:null,
    }
  },
  beforeCreate() {
    this.getConfigKey(this.$route.query.key).then(res=>this.iframeUrl=res.msg+this.$route.query.iframeUrl)
  },
  mounted() {
    this.initIframe()
    this.task = setInterval(() => {
      const iframeContainer = document.getElementById('iframeContainer')
      const iframeDoc = iframeContainer.contentDocument || iframeContainer.contentWindow.document;
      this.deleteGrafanaHeader(iframeDoc);
    }, 100);
    window.onresize = () => {
      this.initIframe()
    }
  },
  beforeDestroy() {
    if (this.task){
      clearInterval(this.task);
    }
  },
  methods: {
    deleteGrafanaHeader(iframeDoc) {
      let mainView = iframeDoc.getElementsByClassName('main-view');
      if (mainView && mainView.length > 0 && mainView.item(0).children.length >= 2) {
        // console.log('main view found2');
        let navBar = mainView.item(0).children.item(1).firstChild;
        if (mainView.item(0).children.item(1).children.length>1){
          navBar = mainView.item(0).children.item(1).children.item(1);
          let toolBar = navBar.children.item(2);
          if (toolBar.children.length >= 2) {
            toolBar.children.item(toolBar.children.length - 1).click();
            return;
          }
          // mainView.item(0).children.item(1).removeChild(mainView.item(0).children.item(1).firstChild);
        }
        // let navBar = mainView.item(0).children.item(1).firstChild;
        if (navBar.children.length >= 3) {
          // console.log('nav bar found');
          let toolBar = navBar.children.item(2);
          if (toolBar.children.length >= 2) {
            // console.log('tool bar found');
            // if (!toolBar.children.item(toolBar.children.length - 2).className.endsWith('button')){
            //   toolBar.children.item(toolBar.children.length - 1).click();
            // }
            toolBar.removeChild(toolBar.children.item(0));
            toolBar.removeChild(toolBar.children.item(toolBar.children.length - 1));
          }

          navBar.removeChild(navBar.children.item(1));
          navBar.removeChild(navBar.children.item(0));
        }
      }
    },
    initIframe() {
      const iframeContainer = document.getElementById('iframeContainer')
      const iframeDoc = iframeContainer.contentDocument || iframeContainer.contentWindow.document;

      this.deleteGrafanaHeader(iframeDoc);

      // 移除iframe中的特定元素
      // const elementToRemove = iframeDoc.getElementsByClassName('css-11s8juy');
      // if (elementToRemove && elementToRemove.length > 0) {
      //   elementToRemove.item(0).parentNode.removeChild(elementToRemove.item(0));
      // }
      // const deviceWidth = document.body.clientWidth
      const deviceHeight = document.body.clientHeight
      // iframeContainer.style.width =  (Number(deviceWidth) - 250) + 'px'
      iframeContainer.style.height = (Number(deviceHeight) - 110) + 'px'
    }
  }
}
</script>

<template>
  <div class="iframe-container">
    <iframe id="iframeContainer" :src="iframeUrl" style="width:100%" frameborder="0" />
  </div>
</template>

<style scoped>

</style>
