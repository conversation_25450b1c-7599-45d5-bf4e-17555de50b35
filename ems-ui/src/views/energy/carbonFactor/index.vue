<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="能源分类" prop="energyCategory">
        <el-select v-model="queryParams.energyCategory" placeholder="请选择设备类型" clearable size="small">
          <el-option
            v-for="dict in dict.type.energy_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="生效日期">
        <el-date-picker
          v-model="daterangeValidFrom"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['energy:carbonFactor:add']"
        >新增</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['energy:carbonFactor:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['energy:carbonFactor:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['energy:carbonFactor:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="carbonFactorList" @selection-change="handleSelectionChange">
<!--      <el-table-column type="selection" width="55" align="center" />-->
<!--      <el-table-column label="碳排放因子ID" align="center" prop="factorId" />-->
      <el-table-column label="能源分类" align="center" prop="energyCategory">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.energy_category" :value="scope.row.energyCategory"/>
        </template>
      </el-table-column>
      <el-table-column label="碳排放因子" align="center" prop="carbonFactor" />
      <el-table-column label="生效日期" align="center" prop="validFrom" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.validFrom, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['energy:carbonFactor:edit']"
            v-if="canEdit(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['energy:carbonFactor:remove']"
            v-if="canEdit(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改碳排放因子设置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="能源分类" prop="energyCategory">
          <el-select v-model="form.energyCategory" placeholder="请选择设备类型" clearable size="small">
            <el-option
              v-for="dict in dict.type.energy_category"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="碳排放因子" prop="carbonFactor">
          <el-input v-model="form.carbonFactor" placeholder="请输入碳排放因子" />
        </el-form-item>
        <el-form-item label="生效日期" prop="validFrom">
          <el-date-picker clearable size="small"
            v-model="form.validFrom"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择生效日期"
            :picker-options="{disabledDate(time){return time.getTime()<=Date.now()}}">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addCarbonFactor,
  delCarbonFactor,
  getCarbonFactor,
  listCarbonFactor,
  updateCarbonFactor
} from "@/api/energy/carbonFactor";
import dayjs from "dayjs";

  export default {
  name: "CarbonFactor",
  dicts:["energy_category"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 碳排放因子设置表格数据
      carbonFactorList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangeValidFrom: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        carbonFactor: null,
        validFrom: null,
        energyCategory: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    canEdit(carbonFactor){
      return dayjs().isBefore(carbonFactor.validFrom);
    },
    /** 查询碳排放因子设置列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeValidFrom && '' != this.daterangeValidFrom) {
        this.queryParams.params["beginValidFrom"] = this.daterangeValidFrom[0];
        this.queryParams.params["endValidFrom"] = this.daterangeValidFrom[1];
      }
      listCarbonFactor(this.queryParams).then(response => {
        this.carbonFactorList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        factorId: null,
        carbonFactor: null,
        energyCategory: null,
        validFrom: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeValidFrom = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.factorId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加碳排放因子设置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const factorId = row.factorId || this.ids
      getCarbonFactor(factorId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改碳排放因子设置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.factorId != null) {
            updateCarbonFactor(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCarbonFactor(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const factorIds = row.factorId || this.ids;
      this.$modal.confirm('是否确认删除碳排放因子设置编号为"' + factorIds + '"的数据项？').then(function() {
        return delCarbonFactor(factorIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('energy/carbonFactor/export', {
        ...this.queryParams
      }, `carbonFactor_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
