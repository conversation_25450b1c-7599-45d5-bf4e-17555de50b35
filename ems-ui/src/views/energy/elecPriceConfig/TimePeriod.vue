<template>
  <div>
    <el-radio-group v-model="category" v-if="!readonly">
      <el-radio-button v-for="(cate, index) in categories" :key="'category'+index" :label="cate.value">{{cate.label}}</el-radio-button>
    </el-radio-group>
    <span v-if="!readonly">{{ tips }}</span>
    <div class="hours-container" v-if="!readonly">
      <div v-for="(item, index) in hours" :key="index" class="hours-item">
        <div class="hours-item-header">{{ compItem(item) }}</div>
        <div class="hours-item-value">
          <div
            :class="compClass(2 * item)"
            @click="handleClick(2 * item)"
            @mouseover="handleHover(2 * item)"
          ></div>
          <div
            :class="compClass(2 * item + 1)"
            @click="handleClick(2 * item + 1)"
            @mouseover="handleHover(2 * item + 1)"
          ></div>
        </div>
      </div>
    </div>

    <div class="tips" v-for="(category, index) in categories" :key="'tip'+index">{{ category.label+': '+timeRangeShowMap[category.value] }}</div>
  </div>
</template>

<script>
/*
* @Description 为了实现24小时区间选择，若有星期的需求直接看 TimeRangeList组件
* <AUTHOR>
* @Date 2020年11月18日 16:33:19
*/
export default {
  model: {
    prop: "sendTimeListMap",
    event: 'change'
  },
  props: {
    sendTimeListMap:{
      required: true,
      default: ()=>{
        return {default:[]}
      }
    },
    categories:{
      required: true,
      default: ["default"]
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    categories: {
      handler(){
        this.category = this.categories[0].value;
      },
      deep: true,
    },
    timeRangeMap: {
      handler(value){
        this.$emit('change', value);
        this.$parent.$emit("el.form.change");
      },
      deep: true,
    },
    sendTimeListMap: {
      handler(){
        this.transformedIndex();
      },
      deep: true,
    }
  },
  computed: {

  },
  data() {
    return {
      hours: [0,1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22,23],// 选项
      selectStart: false,// 开始
      startIndex: '',// 开始下标
      // timeRangeList: [],// 选择的时间段
      // timeRangeListIndex: [],// 选中的下标
      timeRangeIndexListMap:{},
      timeRangeMap:{},
      timeRangeShowMap:{},
      tempRangeIndex: [],// 预选下标
      tips: '向右选中，向左取消选择',
      category:'default',
    }
  },
  methods: {
    hasIndex(index){
      for (let key in this.timeRangeIndexListMap){
        let i = this.timeRangeIndexListMap[key].indexOf(index);
        if (i > -1){
          return true;
        }
      }
      return false;
    },
    minutesToShow(min){
      let hour = Math.floor(min/60);
      let m = min%60;
      return `${hour}:${m?m:'00'}`;
    },
    // 时间区间转换成下标区间
    transformedIndex() {
      // this.timeRangeListIndex = [];
      this.timeRangeMap = this.sendTimeListMap;
      this.timeRangeIndexListMap = {};
      this.timeRangeShowMap={};
      this.categories.forEach(cate=>{
        let timeList = [...(this.sendTimeListMap[cate.value]||[])];
        let list = [];
        let showList = [];
        if (timeList){
          timeList.sort((a, b)=>{
            return a.start-b.start;
          });
          for (let period of timeList){
            for (let time = period.start; time < period.end; time+=30){
              list.push(time/30);
              // this.timeRangeListIndex.push(time/30);
            }
            showList.push(`${this.minutesToShow(period.start)}-${this.minutesToShow(period.end)}`)
          }
        }
        this.$set(this.timeRangeIndexListMap, cate.value, list);
        this.$set(this.timeRangeShowMap, cate.value, showList);
      });
      // this.timeRangeList = this.sendTimeList;
      // this.timeRangeList.forEach(element => {
      //   const [startTime, endTime] = element.match(/\d+\:\d+/g);
      //   if (startTime && endTime) {
      //     let [startHour, startMin] = startTime.split(':');
      //     let [endHour, endMin] = endTime.split(':');
      //     if (startHour && startMin && endHour && endMin) {
      //       let startNum, endNum;
      //       if (startMin === '00') {
      //         startNum = 2 * parseInt(startHour);
      //       } else {
      //         startNum = 2 * parseInt(startHour) + 1;
      //       }
      //       if (endMin === '00') {
      //         endNum = 2 * parseInt(endHour) - 1;
      //       } else {
      //         endNum = 2 * parseInt(endHour);
      //       }
      //       while (endNum >= startNum) {
      //         this.timeRangeListIndex.push(startNum);
      //         startNum++;
      //       }
      //
      //     } else {
      //       this.$message.error("时间段格式不正确");
      //     }
      //
      //   } else {
      //     this.$message.error("没有拿到开始时间或结束时间或者时间段格式不对");
      //   }
      // });
    },
    // 下标区间转换成时间区间
    transformedSection() {
      this.timeRangeMap = {};
      this.timeRangeShowMap = {};

      for (let cate of this.categories){
        let startTime = '', endTime = '', len = this.hours.length, startMinutes = -1, endMinutes = -1;
        let timeList = [];
        let timeShowList = [];
        for (let index = this.hours[0] * 2; index < 2 * (len + 1); index++) {
          if (this.timeRangeIndexListMap[cate.value].indexOf(index) > -1) {
            if (startTime) {// 如果有开始时间，直接确定结束时间
              let endHour = Math.floor((index + 1) / 2);
              let endMin = (index + 1) % 2 === 0 ? "00" : "30";
              endTime = `${endHour < 10 ? '0' + endHour : endHour}:${endMin}`;
              endMinutes = (index+1)*30;
            } else {// 没有开始时间，确定当前点为开始时间
              let startHour = Math.floor(index / 2);
              let startMin = index % 2 === 0 ? "00" : "30";
              startTime = `${startHour < 10 ? '0' + startHour : startHour}:${startMin}`;
              startMinutes = index*30;
            }
            if (index === 2 * this.hours.length + 1) { // 如果是最后一格，直接结束
              endTime = `${Math.floor((index + 1) / 2)}:00`;
              endMinutes = (index+1)*30;
              timeList.push({start: startTime?startMinutes:1410, end: endMinutes});
              timeShowList.push(`${startTime ? startTime : "23:30"}-${endTime}`);
              startTime = '';
              endTime = '';
            }
          } else { // 若这个点不在选择区间，确定一个时间段
            if (startTime && endTime) {
              timeList.push({start: startMinutes, end: endMinutes});
              timeShowList.push(`${startTime}-${endTime}`);
              startTime = '';
              endTime = '';
            } else if (startTime && !endTime) {// 这里可能只选半个小时
              let endHour = Math.floor(index / 2);
              let endMin = index % 2 === 0 ? "00" : "30";
              endTime = `${endHour < 10 ? '0' + endHour : endHour}:${endMin}`;
              endMinutes = index*30;
              timeList.push({start: startMinutes, end: endMinutes});
              timeShowList.push(`${startTime}-${endTime}`);
              startTime = '';
              endTime = '';
            }
          }
        }
        this.$set(this.timeRangeMap, cate.value, timeList);
        this.$set(this.timeRangeShowMap, cate.value, timeShowList);
      }
    },
    deleteIndex(index){
      for (let key in this.timeRangeIndexListMap){
        let i = this.timeRangeIndexListMap[key].indexOf(index);
        if (i > -1){
          this.timeRangeIndexListMap[key].splice(i, 1);
        }
      }
    },
    addIndex(index){
      let array = [];
      array.push(...this.timeRangeIndexListMap[this.category]);
      array.push(index);
      this.$set(this.timeRangeIndexListMap, this.category, array);
    },
    // 点击事件
    handleClick(index) {
      if (this.readonly){
        return;
      }
      if (this.selectStart) {
        if (index === this.startIndex) {// 双击取反
          if (this.timeRangeIndexListMap[this.category].indexOf(index)>-1) {
            this.deleteIndex(index);
          } else {
            this.deleteIndex(index);
            this.addIndex(this.startIndex);
            // this.timeRangeIndexListMap[this.category].push(this.startIndex);
          }
        } else if (index > this.startIndex) {// 选取数据--向右添加，向左取消
          while (index >= this.startIndex) {
            //已存在的先删除
            this.deleteIndex(this.startIndex);
            this.addIndex(this.startIndex);
            this.startIndex++;
          }
        } else {// 删除数据
          while (this.startIndex >= index) {
            this.deleteIndex(index);
            index++;
          }
        }
        this.startIndex = '';
        this.tempRangeIndex = [];
        this.transformedSection();
      } else {
        this.startIndex = index;
      }
      this.selectStart = !this.selectStart;
    },
    // 预选区间
    handleHover(index) {
      if (this.selectStart) {
        this.tempRangeIndex = [];
        if (index > this.startIndex) {// 选取数据--向右添加，向左取消
          while (index >= this.startIndex) {
            this.tempRangeIndex.push(index);
            index--;
          }
        } else {// 删除数据
          while (this.startIndex >= index) {
            this.tempRangeIndex.push(index);
            index++;
          }
        }
      }
    },
    // 是否选中，计算className
    compClass(index) {
      if (index === this.startIndex) {
        return 'hours-item-left preSelected';
      }
      if (index >= this.startIndex) {
        if (this.tempRangeIndex.indexOf(index) > -1) {
          return 'hours-item-left preSelected';
        }
      } else {
        if (this.tempRangeIndex.indexOf(index) > -1) {
          return 'hours-item-left unSelected';
        }
      }
      if (this.hasIndex(index)){
        if (this.timeRangeIndexListMap[this.category].indexOf(index) > -1){
          return 'hours-item-left selected';
        }else{
          return 'hours-item-left selectedOther';
        }
      }else{
        return 'hours-item-left';
      }
      // return this.timeRangeListIndex.indexOf(index) > -1 ? 'hours-item-left selected' : 'hours-item-left';
    },
    compItem(item) {// 不足10前面补0
      return item < 10 ? `0${item}` : item;
    },

  },
  mounted() {
    this.transformedIndex();
    this.category = this.categories[0].value;
  }
}
</script>

<style lang="scss" scoped>
.hours-container {
  display: flex;
  cursor: pointer;
  .hours-item {
    width: 30px;
    height: 60px;
    border: 1px solid #c2d0f3;
    border-right: none;
    text-align: center;
    &:last-child {
      border-right: 1px solid #c2d0f3;
    }
    .hours-item-header {
      width: 100%;
      height: 30px;
      line-height: 30px;
      border-bottom: 1px solid #c2d0f3;
    }
    .hours-item-value {
      width: 100%;
      height: 30px;
      box-sizing: border-box;
      display: flex;
      .hours-item-left,
      .hours-item-right {
        width: 50%;
        height: 100%;
        border-right: 1px solid #c2d0f3;
        box-sizing: border-box;
        &:last-child {
          border-right: none;
        }
      }
    }
    .selectedOther {
      background-color: #d0d0d0;
      border-bottom: 1px solid #c2d0f3;
    }
    .selected {
      background-color: #4e84fe;
      border-bottom: 1px solid #c2d0f3;
    }
    .preSelected {
      background-color: #8eaffc;
      border-bottom: 1px solid #c2d0f3;
    }
    .unSelected {
      background-color: #ffffff;
      border-bottom: 1px solid #c2d0f3;
    }
  }
}
.tips {
  width: 100%;
  line-height: 30px;
}
</style>
