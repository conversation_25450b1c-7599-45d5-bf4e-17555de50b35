<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="生效日期">
        <el-date-picker
          v-model="daterangeValidFrom"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['energy:elecPriceConfig:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="elecPriceConfigList" @selection-change="handleSelectionChange">
      <el-table-column v-for="(category, index) in dict.type.elec_price_period_category" :label="category.label+'时段价格'" :key="'tablePrice'+index" >
        <template slot-scope="scope">
          <span>{{ (scope.row.emsPriceList.find(price=>price && price.category===category.value)||{price:0}).price }}</span>
        </template>
      </el-table-column>
      <el-table-column label="生效日期" align="center" prop="validFrom" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.validFrom, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['energy:elecPriceConfig:edit']"
            v-if="canEdit(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['energy:elecPriceConfig:edit']"
            v-if="!canEdit(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['energy:elecPriceConfig:remove']"
            v-if="canEdit(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改电价配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="生效日期" prop="validFrom">
          <el-date-picker clearable size="small"
            v-model="form.validFrom"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择生效日期"
            :picker-options="{disabledDate(time){return time.getTime()<=Date.now()}}"
            :readonly="form.configId && !canEdit(form)"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item v-for="(category, index) in dict.type.elec_price_period_category" :key="'price'+index" :label="category.label+'时段价格'">
          <el-input-number v-model="emsPriceMap[category.value]" :disabled="form.configId && !canEdit(form)" :controls="false"/>
        </el-form-item>
        <el-form-item v-for="(type, index) in dict.type.elec_price_period_type" :key="'period'+index" :label="type.label+'时段'">
          <time-period v-model="emsPeriodMap[type.value]" :categories="dict.type.elec_price_period_category" :readonly="form.configId && !canEdit(form)"/>
        </el-form-item>
<!--        <time-period v-model="periodMap" :categories="dict.type.elec_price_period_category"/>-->
<!--        <el-card v-for="(category, index) in dict.type.elec_price_period_category" :key="index">-->
<!--          <div slot="header">-->
<!--            {{category.label}}时段-->
<!--          </div>-->
<!--          <el-form-item label="价格">-->
<!--            <el-input-number v-model="emsPriceMap[category.value]"/>-->
<!--          </el-form-item>-->
<!--          <el-form-item v-for="(type, index) in dict.type.elec_price_period_type" :key="index" :label="type.label">-->
<!--            <el-tag v-for="(period, index) in emsPeriodMap[category.value]?emsPeriodMap[category.value][type.value]:[]" :key="index" closable @close="deletePeriod(type.value, category.value, index)">-->
<!--              {{getShowTime(period.start)+'~'+getShowTime(period.end)}}-->
<!--            </el-tag>-->
<!--            <p/>-->
<!--            <el-time-select-->
<!--              :picker-options="{-->
<!--                start:'00:00',-->
<!--                end:'23:30'-->
<!--              }"-->
<!--            ></el-time-select>~-->
<!--            <el-time-select-->
<!--              :picker-options="{-->
<!--                start:'00:30',-->
<!--                end:'24:00'-->
<!--              }"-->
<!--            ></el-time-select>-->
<!--&lt;!&ndash;            <el-time-picker&ndash;&gt;-->
<!--&lt;!&ndash;              is-range&ndash;&gt;-->
<!--&lt;!&ndash;              range-separator="至"&ndash;&gt;-->
<!--&lt;!&ndash;              start-placeholder="开始时间"&ndash;&gt;-->
<!--&lt;!&ndash;              end-placeholder="结束时间"&ndash;&gt;-->
<!--&lt;!&ndash;              placeholder="选择时间范围"&ndash;&gt;-->
<!--&lt;!&ndash;              :picker-options="{&ndash;&gt;-->
<!--&lt;!&ndash;                start: '08:30',&ndash;&gt;-->
<!--&lt;!&ndash;                step: '00:15',&ndash;&gt;-->
<!--&lt;!&ndash;                end: '18:30'&ndash;&gt;-->
<!--&lt;!&ndash;              }">&ndash;&gt;-->
<!--&lt;!&ndash;            </el-time-picker>&ndash;&gt;-->
<!--          </el-form-item>-->
<!--        </el-card>-->

<!--        <el-divider content-position="center">价格信息</el-divider>-->
<!--        <el-row :gutter="10" class="mb8">-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddEmsPrice">添加</el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteEmsPrice">删除</el-button>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-table :data="emsPriceList" :row-class-name="rowEmsPriceIndex" @selection-change="handleEmsPriceSelectionChange" ref="emsPrice">-->
<!--          <el-table-column type="selection" width="50" align="center" />-->
<!--          <el-table-column label="序号" align="center" prop="index" width="50"/>-->
<!--          <el-table-column label="价格" prop="price">-->
<!--            <template slot-scope="scope">-->
<!--              <el-input v-model="scope.row.price" placeholder="请输入价格" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column label="分类" prop="category">-->
<!--            <template slot-scope="scope">-->
<!--              <el-input v-model="scope.row.category" placeholder="请输入分类" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column label="备注" prop="remark">-->
<!--            <template slot-scope="scope">-->
<!--              <el-input v-model="scope.row.remark" placeholder="请输入备注" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--        </el-table>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {
  addElecPriceConfig, delElecPriceConfig,
  getElecPriceConfig,
  listElecPriceConfig,
  updateElecPriceConfig
} from "@/api/energy/elecPriceConfig";
import TimePeriod from "@/views/energy/elecPriceConfig/TimePeriod";
import dayjs from "dayjs";

export default {
  name: "ElecPriceConfig",
  dicts:['elec_price_period_category', 'elec_price_period_type'],
  components:{TimePeriod},
  data() {
    return {
      // 遮罩层
      loading: true,
      selectTimeList:["02:00-03:00"],
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedEmsPrice: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 电价配置表格数据
      elecPriceConfigList: [],
      // 价格表格数据
      emsPriceMap: {},
      periodMap:{},
      emsPeriodMap: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangeValidFrom: [],
      // 备注时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        validFrom: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  computed:{
    // emsPricePeriodList:function(){
    //   let list = [];
    //   this.emsPeriodMap.forEach((typeMap, category)=>{
    //     typeMap.forEach((periodList, type)=>{
    //       periodList.forEach(period=>{
    //         let arr = period.split('~');
    //         list.push({type, category, start: arr[0], end: arr[1]});
    //       })
    //     })
    //   });
    //   return list;
    // }
  },
  created() {
    this.getList();
  },
  methods: {
    canEdit(priceConfig){
      return dayjs().isBefore(priceConfig.validFrom);
    },
    /** 查询电价配置列表 */
    onDictReady(dict) {
      this.emsPriceMap = {};
      this.emsPeriodMap = {};
      this.dict.type.elec_price_period_type.forEach((type)=>{
        let cateMap = {};
        this.dict.type.elec_price_period_category.forEach((category,index)=>{
          cateMap[category.value] = [];
          this.$set(this.emsPriceMap, category.value, 0);
        });
        this.$set(this.emsPeriodMap, type.value, cateMap);
      });
    },
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeValidFrom && '' != this.daterangeValidFrom) {
        this.queryParams.params["beginValidFrom"] = this.daterangeValidFrom[0];
        this.queryParams.params["endValidFrom"] = this.daterangeValidFrom[1];
      }
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      listElecPriceConfig(this.queryParams).then(response => {
        this.elecPriceConfigList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        configId: null,
        validFrom: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.emsPriceMap = {};
      this.emsPeriodMap = {};
      this.dict.type.elec_price_period_type.forEach((type)=>{
        let cateMap = {};
        this.dict.type.elec_price_period_category.forEach((category,index)=>{
          cateMap[category.value] = [];
          this.$set(this.emsPriceMap, category.value, 0);
        });
        this.$set(this.emsPeriodMap, type.value, cateMap);
      });
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeValidFrom = [];
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.configId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加电价配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const configId = row.configId || this.ids
      getElecPriceConfig(configId).then(response => {
        this.form = response.data;
        this.emsPriceList = response.data.emsPriceList;
        this.dict.type.elec_price_period_type.forEach((type)=>{
          let cateMap = {};
          this.dict.type.elec_price_period_category.forEach((category,index)=>{
            let list = [];
            for (let period of response.data.emsPricePeriodList){
              if (period.category === category.value && period.type === type.value){
                list.push({start: period.start, end: period.end});
              }
            }
            if (!this.emsPriceMap[category.value]){
              this.$set(this.emsPriceMap, category.value, (response.data.emsPriceList.find(emsPrice=>emsPrice&&emsPrice.category === category.value)||{price:0}).price);
            }
            cateMap[category.value] = list;
          });
          this.$set(this.emsPeriodMap, type.value, cateMap);
        });
        this.open = true;
        this.title = "修改电价配置";
      });
    },
    buildPriceList(){
      let result = [];
      for (let category in this.emsPriceMap){
        result.push({
          category,
          price: this.emsPriceMap[category],
        });
      }
      return result;
    },
    buildPeriodList(){
      let result = [];
      for (let type in this.emsPeriodMap){
        let cateMap = this.emsPeriodMap[type];
        for (let category in cateMap){
          for (let period of cateMap[category]){
            result.push({
              category,
              type,
              start: period.start,
              end: period.end
            });
          }
        }

      }
      console.log(result);
      return result;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.emsPriceList = this.buildPriceList();
          this.form.emsPricePeriodList = this.buildPeriodList();
          if (this.form.configId != null) {
            updateElecPriceConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addElecPriceConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configIds = row.configId || this.ids;
      this.$modal.confirm('是否确认删除电价配置编号为"' + configIds + '"的数据项？').then(function() {
        return delElecPriceConfig(configIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    deletePeriod(type, category, index){
      this.emsPeriodMap[category][type].splice(index, 1);
      this.$forceUpdate();
    },
	/** 价格序号 */
    rowEmsPriceIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 价格添加按钮操作 */
    handleAddEmsPrice() {
      let obj = {};
      obj.price = "";
      obj.category = "";
      obj.remark = "";
      this.emsPriceList.push(obj);
    },
    /** 价格删除按钮操作 */
    handleDeleteEmsPrice() {
      if (this.checkedEmsPrice.length == 0) {
        this.$modal.msgError("请先选择要删除的价格数据");
      } else {
        const emsPriceList = this.emsPriceList;
        const checkedEmsPrice = this.checkedEmsPrice;
        this.emsPriceList = emsPriceList.filter(function(item) {
          return checkedEmsPrice.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleEmsPriceSelectionChange(selection) {
      this.checkedEmsPrice = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('energy/elecPriceConfig/export', {
        ...this.queryParams
      }, `elecPriceConfig_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
