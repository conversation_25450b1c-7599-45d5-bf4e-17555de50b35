<template>
  <div class="app-container">
	<span>注塑机日报表</span>
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
      <el-form-item label="产线" prop="factoryName">
        <el-select v-model="queryParams.factoryName" placeholder="请选择产线" clearable>
			<el-option key="0" label="HT200注塑机2800T" :value="888"></el-option>
			<el-option key="1" label="HT202注塑机3000T" :value="777"></el-option>
			<el-option key="2" label="HT208注塑机3800T" :value="666"></el-option>
		</el-select>
      </el-form-item>
	  <el-form-item label="产品QAD" prop="assembly">
	    <el-select v-model="queryParams.assembly" placeholder="请选择产品QAD" clearable>
	  			<el-option key="0" label="QAD1" :value="0"></el-option>
	  			<el-option key="1" label="QAD2" :value="1"></el-option>
	  			<el-option key="2" label="QAD3" :value="2"></el-option>
	  		</el-select>
	  </el-form-item>
	  <el-form-item label="时间" prop="time">
		  <el-date-picker
			v-model="queryParams.time"
			size="small"
			style="width: 240px"
			value-format="yyyy-MM-dd"
			type="daterange"
			range-separator="-"
			start-placeholder="开始日期"
			end-placeholder="结束日期"
		  ></el-date-picker>		
	    </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>
	<el-tabs v-model="activeName" @tab-click="handleClick">
		<el-tab-pane label="注塑机状态耗能" name="first">
			<el-table :data="partsProcessResultList">
			  <el-table-column label="设备名称" align="center" prop="barCode" />
			  <el-table-column label="日期" align="center" prop="workOrderCode" />
			  <el-table-column label="状态" align="center" prop="assemblyCode" />
			  <el-table-column label="用时(H)" align="center" prop="result" />
			  <el-table-column label="用能(kWh)" align="center" prop="kWh" />
			</el-table>
		</el-tab-pane>
		<el-tab-pane label="注塑机日报表" name="second">
			<el-table :data="partsProcessResultList1">
			  <el-table-column label="设备名称" align="center" prop="barCode" />
			  <el-table-column label="日期" align="center" prop="workOrderCode" />
			  <el-table-column label="产品QAD代码" align="center" prop="assemblyCode" />
			  <el-table-column label="产品名称" align="center" prop="result" />
			  <el-table-column label="产量(件)" align="center" prop="kWh" />
			  <el-table-column label="产量(模)" align="center" prop="kWh1" />
			  <el-table-column label="产品总重量(KG)" align="center" prop="kWh2" />
			  <el-table-column label="生产时间(分钟)" align="center" prop="kWh3" />
			  <el-table-column label="生产耗电量(kWh)" align="center" prop="kWh4" />
			  <el-table-column label="分钟耗电量(kWh)" align="center" prop="kWh5" />
			</el-table>
		</el-tab-pane>
	</el-tabs>
	
  </div>
</template>

<script>
import { listFactory, getFactory, delFactory, addFactory, updateFactory, exportFactory, importTemplate } from "@/api/equipment/factory";
import { getToken } from "@/utils/auth";

export default {
  name: "Factory",
  data() {
    return {
		nowIndex: 0,
		activeName: 'first',
		// 查询参数
		queryParams: {
		  factoryName: null,
		  assembly: null,
		  time: null
		},
		partsProcessResultList:[],
		partsProcessResultList1:[]
	};
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询工厂列表 */
    getList() {
		let that = this
		that.partsProcessResultList = []
		that.partsProcessResultList1 = []
		if(null != that.queryParams.factoryName && "" != that.queryParams.factoryName){
			console.log(1)
			if("0" == that.nowIndex){
				console.log(3)
				that.partsProcessResultList = [
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"自动",
						result:"23",
						kWh:"90",
					}
				]
			}else{
				console.log(4)
				that.partsProcessResultList1 = [
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"689991",
						result:"ETSHJB",
						kWh:"90",
						kWh1:"909",
						kWh2:"920",
						kWh3:"690",
						kWh4:"890",
						kWh5:"390",
					}
				]
			}
		}else{
			// console.log(2)
			if("0" == that.nowIndex){
				// console.log(5)
				that.partsProcessResultList = [
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"自动",
						result:"23",
						kWh:"90",
					},
					{
						barCode:"HT202注塑机2800T",
						workOrderCode:"2021-12-02",
						assemblyCode:"自动",
						result:"25",
						kWh:"70",
					},
					{
						barCode:"HT200注塑机3800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"自动",
						result:"34",
						kWh:"60",
					},
				]
			}else{
				console.log(6)
				that.partsProcessResultList1 = [
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"689991",
						result:"ETSHJB",
						kWh:"90",
						kWh1:"909",
						kWh2:"920",
						kWh3:"690",
						kWh4:"890",
						kWh5:"390",
					},
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"689991",
						result:"ETSHJB",
						kWh:"90",
						kWh1:"909",
						kWh2:"920",
						kWh3:"690",
						kWh4:"890",
						kWh5:"390",
					},
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"689991",
						result:"ETSHJB",
						kWh:"90",
						kWh1:"909",
						kWh2:"920",
						kWh3:"690",
						kWh4:"890",
						kWh5:"390",
					},
				]
			}
		}
    },
	/** 搜索按钮操作 */
	handleQuery() {
	  this.getList();
	},
	handleClick(tab, event) {
	  this.nowIndex = tab.index
	  this.getList();
	},
  }	
};
</script>
