<template>
  <div class="app-container">
	<span>注塑机日报表</span>
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
      <el-form-item label="产线" prop="factoryName">
        <el-select v-model="queryParams.factoryName" placeholder="请选择产线" clearable>
			<el-option key="0" label="HT200注塑机2800T" :value="888"></el-option>
			<el-option key="1" label="HT202注塑机3000T" :value="777"></el-option>
			<el-option key="2" label="HT208注塑机3800T" :value="666"></el-option>
		</el-select>
      </el-form-item>
	  <el-form-item label="产品QAD" prop="assembly">
	    <el-select v-model="queryParams.assembly" placeholder="请选择产品QAD" clearable>
	  			<el-option key="0" label="QAD1" :value="0"></el-option>
	  			<el-option key="1" label="QAD2" :value="1"></el-option>
	  			<el-option key="2" label="QAD3" :value="2"></el-option>
	  		</el-select>
	  </el-form-item>
	  <el-form-item label="时间" prop="time">
		   <el-date-picker
      v-model="queryParams.time"
      type="date"
	  value-format="yyyy-MM-dd"
      placeholder="选择日期">
    </el-date-picker>
		  <!-- <el-date-picker
			v-model="queryParams.time"
			size="small"
			style="width: 240px"
			value-format="yyyy-MM-dd"
			type="daterange"
			range-separator="-"
			start-placeholder="开始日期"
			end-placeholder="结束日期"
		  ></el-date-picker>		 -->
	    </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>
	<div class="conOne">
		<div id="main" style="width: 70%;height:400px;float: left;"></div>
		<div id="main1" style="width: 30%;height:400px;float: right;"></div>
	</div>
	<div class="conOne">
		<div id="main2" style="width: 100%;height:400px;"></div>
	</div>
	<div class="conOne">
		<div id="main3" style="width: 100%;height:400px;"></div>
	</div>
  </div>
</template>

<script>

import {getOnePictureData,getTwoPictureData} from "@/api/new/diagram";

export default {
  name: "Factory",
  data() {
    return {
		nowIndex: 0,
		activeName: 'first',
		// 查询参数
		queryParams: {
		  factoryName: null,
		  assembly: null,
		  time: null
		},
		partsProcessResultList:[],
		partsProcessResultList1:[],
		onePicture:[],
		twoPicture:[],
		threePicture:[ ['product', '每模单耗', '分钟单耗','公斤单耗'],
		          ['HT101', 3, 8, 8.8],
		          ['HT102', 6, 8, 8],
		          ['HT103', 3, 8, 7.8],
		          ['HT104', 6, 5, 6],
		          ['HT105', 3, 6, 5],
		          ['HT106', 8.3, 8, 6.8],
		          ['HT107', 4, 5, 6],
		          ['HT108', 7, 8, 7],
		          ['HT109', 9, 9, 5.8],
		          ['HT201', 6, 5, 6],],
		fourPicture:[ ['product', '能源有效利用率（运行电量/总电量*100%）', '设备有效利用率（运行时间/日历时间*100%）'],
		          ['HT101', 93, 58],
		          ['HT102', 96, 82],
		          ['HT103', 93, 58.8],
		          ['HT104', 96, 56],
		          ['HT105', 93, 65],
		          ['HT106', 98.3, 69],
		          ['HT107', 94, 56],
		          ['HT108', 97, 87],
		          ['HT109', 99, 67.8],
		          ['HT201', 96, 56],]
	};

  },
  created() {
	   var that = this;
		    let yy = new Date().getFullYear();
		    let mm = new Date().getMonth()+1;
		    let dd = new Date().getDate()-1;
		    that.gettime = yy+'-'+mm+'-'+dd;
	//   console.log(this.gettime)
    //this.getList(this.gettime);
	this.getList("2022-1-13");
  },
  mounted() {

  	// this.myEcharts();
	//this.myEcharts1();
	this.myEcharts2();
	this.myEcharts3();
  },
  methods: {
	myEcharts(){
		  // 基于准备好的dom，初始化echarts实例
		  var myChart = this.$echarts.init(document.getElementById('main'));
		  const colors = ['#5470C6','#5470C6', '#91CC75','#91CC75', '#EE6666',];
		  var option = {
			  color: colors,
			title: {
				text : "峰平谷生产时间利用率&平均电价",

			},
		    tooltip: {},
		    legend: {
				right: '10%'
		    },
			 dataZoom: [
          {
            show: true,
            realtime: true,
            height: 24, //这里可以设置dataZoom的尺寸
            bottom: 8,	//滚动体距离底部的距离
            start: 0,  //初始化时，滑动条宽度开始标度
            end: 20, //初始化时，滑动条宽度结束标度
          },
          {
            type: "inside", //内置滑动，随鼠标滚轮展示
            realtime: true,
            start: 0,
            end: 20,
          },
        ],
		 dataset: {
		        source: this.$data.onePicture
		      },
		      xAxis: { type: 'category' },
		      yAxis: {},
		      // Declare several bar series, each will be mapped
		      // to a column of dataset.source by default.{ type: 'line' }
		      series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' },{ type: 'bar' }, { type: 'bar' }]
		  };

		  // 使用刚指定的配置项和数据显示图表。
		  myChart.setOption(option);
	},
	myEcharts1(){
		  // 基于准备好的dom，初始化echarts实例
		  var myChart = this.$echarts.init(document.getElementById('main1'));
		const colors = ['#5470C6', '#91CC75', '#EE6666'];
		  var option = {
			  color: colors,
			  title: {
			  	text : "峰平谷用电量&占比"
			  },
			  tooltip: {
				trigger: 'item'
			  },
			  legend: {
				top: '5%',
				left: 'center'
			  },
			  series: [
				{
				  name: '提示',
				  type: 'pie',
				  radius: ['40%', '70%'],
				  avoidLabelOverlap: false,
				  label: {
					show: false,
					formatter: '{b}\n{c}%',
					position: 'center'
				  },
				  emphasis: {
					label: {
					  show: true,
					  fontSize: '40',
					  fontWeight: 'bold'
					}
				  },
				  labelLine: {
					show: false
				  },
				  data: this.$data.twoPicture
				}
			  ]
			};

		  // 使用刚指定的配置项和数据显示图表。
		  myChart.setOption(option);
	},
	myEcharts2(){
		  // 基于准备好的dom，初始化echarts实例
		  var myChart = this.$echarts.init(document.getElementById('main2'));
			const colors = ['#5470C6', '#91CC75', '#EE6666'];
		  var option = {
			color: colors,
			title: {
				text : "设备单耗"
			},
		    tooltip: {},
		    legend: {
				right: '10%'
		    },
		    dataset: {
		        source:this.$data.threePicture
		      },
		      xAxis: { type: 'category' },
		      yAxis: {},
		      // Declare several bar series, each will be mapped
		      // to a column of dataset.source by default.
		      series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
		  };

		  // 使用刚指定的配置项和数据显示图表。
		  myChart.setOption(option);
	},
	myEcharts3(){
		  // 基于准备好的dom，初始化echarts实例
		  var myChart = this.$echarts.init(document.getElementById('main3'));
		const colors = ['#5470C6', '#91CC75', '#EE6666'];
		  var option = {
			  color: colors,
			title: {
				text : "设备有效利用率"
			},
		    tooltip: {},
		    legend: {
				right: '10%'
		    },
		    dataset: {
		        source:this.$data.fourPicture
		      },
		      xAxis: { type: 'category' },
		      yAxis: {},
		      // Declare several bar series, each will be mapped
		      // to a column of dataset.source by default.
		      series: [{ type: 'bar' }, { type: 'bar' }]
		  };

		  // 使用刚指定的配置项和数据显示图表。
		  myChart.setOption(option);
	},
    /** 查询工厂列表 */
    getList(time) {
		// let that = this
		 getOnePictureData(time).then(response=>{
			// console.log(response)
			this.$data.onePicture=response.data
			this.myEcharts();
			// console.log(this.$data.onePicture)
		})
		 getTwoPictureData(time).then(response=>{
			console.log(response)
			this.$data.twoPicture=response.data
			this.myEcharts1();
		})

    },
	/** 搜索按钮操作 */
	handleQuery() {
		console.log(this.$data.queryParams.time)
	  this.getList(this.$data.queryParams.time);
	},
	handleClick(tab, event) {
	  this.nowIndex = tab.index
	  this.getList();
	},
  }
};

</script>
<style scoped>
	.conOne{
		width: 98%;
		margin: 20px auto;
		display: flex;
	}
</style>
