<template>
  <div class="app-container">
	<span>注塑机日报表</span>
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
      <el-form-item label="产线" prop="factoryName">
        <el-select v-model="queryParams.factoryName" placeholder="请选择产线" clearable>
			<el-option key="0" label="HT200注塑机2800T" :value="888"></el-option>
			<el-option key="1" label="HT202注塑机3000T" :value="777"></el-option>
			<el-option key="2" label="HT208注塑机3800T" :value="666"></el-option>
		</el-select>
      </el-form-item>
	  <el-form-item label="产品QAD" prop="assembly">
	    <el-select v-model="queryParams.assembly" placeholder="请选择产品QAD" clearable>
	  			<el-option key="0" label="QAD1" :value="0"></el-option>
	  			<el-option key="1" label="QAD2" :value="1"></el-option>
	  			<el-option key="2" label="QAD3" :value="2"></el-option>
	  		</el-select>
	  </el-form-item>
	  <el-form-item label="时间" prop="time">
		  <el-date-picker
			v-model="queryParams.time"
			size="small"
			style="width: 240px"
			value-format="yyyy-MM-dd"
			type="daterange"
			range-separator="-"
			start-placeholder="开始日期"
			end-placeholder="结束日期"
		  ></el-date-picker>
	    </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>
	<!-- <el-tabs v-model="activeName" @tab-click="handleClick">
		<el-tab-pane label="注塑机状态耗能" name="first">
			<el-table :data="partsProcessResultList">
			  <el-table-column label="设备名称" align="center" prop="barCode" />
			  <el-table-column label="日期" align="center" prop="workOrderCode" />
			  <el-table-column label="状态" align="center" prop="assemblyCode" />
			  <el-table-column label="用时(H)" align="center" prop="result" />
			  <el-table-column label="用能(kWh)" align="center" prop="kWh" />
			</el-table>
		</el-tab-pane>
		<el-tab-pane label="注塑机日报表" name="second">
			<el-table :data="partsProcessResultList1">
			  <el-table-column label="设备名称" align="center" prop="barCode" />
			  <el-table-column label="日期" align="center" prop="workOrderCode" />
			  <el-table-column label="产品QAD代码" align="center" prop="assemblyCode" />
			  <el-table-column label="产品名称" align="center" prop="result" />
			  <el-table-column label="产量(件)" align="center" prop="kWh" />
			  <el-table-column label="产量(模)" align="center" prop="kWh1" />
			  <el-table-column label="产品总重量(KG)" align="center" prop="kWh2" />
			  <el-table-column label="生产时间(分钟)" align="center" prop="kWh3" />
			  <el-table-column label="生产耗电量(kWh)" align="center" prop="kWh4" />
			  <el-table-column label="分钟耗电量(kWh)" align="center" prop="kWh5" />
			</el-table>
		</el-tab-pane>
	</el-tabs> -->
	<div class="conOne">
		<div id="main" style="width: 70%;height:400px;float: left;"></div>
		<div id="main1" style="width: 30%;height:400px;float: right;"></div>
	</div>
	<div class="conOne">
		<div id="main2" style="width: 100%;height:400px;"></div>
	</div>
	<div class="conOne">
		<div id="main3" style="width: 100%;height:400px;"></div>
	</div>
  </div>
</template>

<script>
import { listFactory, getFactory, delFactory, addFactory, updateFactory, exportFactory, importTemplate } from "@/api/equipment/factory";
import { getToken } from "@/utils/auth";

export default {
  name: "Factory",
  data() {
    return {
		nowIndex: 0,
		activeName: 'first',
		// 查询参数
		queryParams: {
		  factoryName: null,
		  assembly: null,
		  time: null
		},
		partsProcessResultList:[],
		partsProcessResultList1:[]
	};
  },
  created() {
    // this.getList();
  },
  mounted() {
  	this.myEcharts();
	this.myEcharts1();
	this.myEcharts2();
	this.myEcharts3();
  },
  methods: {
	myEcharts(){
		  // 基于准备好的dom，初始化echarts实例
		  var myChart = this.$echarts.init(document.getElementById('main'));

		  var option = {
			title: {
				text : "峰平谷生产时间利用率&平均电价"
			},
		    tooltip: {},
		    legend: {
				right: '10%'
		    },
		    dataset: {
		        source: [
		          ['product', '峰', '平','谷'],
		          ['HT101', 43.3, 85.8, 58.8],
		          ['HT102', 46.3, 88.8, 85.8],
		          ['HT103', 43.3, 89.8, 67.8],
		          ['HT104', 26.3, 58.8, 46.8],
		          ['HT105', 56.3, 67.8, 85.8],
		          ['HT106', 89.3, 99.8, 46.8],
		          ['HT107', 45.3, 55.8, 58.8],
		          ['HT108', 37.3, 46.8, 67.8],
		          ['HT109', 39.3, 89.8, 85.8],
		          ['HT201', 26.3, 85.8, 46.8],
		        ]
		      },
		      xAxis: { type: 'category' },
		      yAxis: {},
		      // Declare several bar series, each will be mapped
		      // to a column of dataset.source by default.
		      series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }, { type: 'line' }]
		  };

		  // 使用刚指定的配置项和数据显示图表。
		  myChart.setOption(option);
	},
	myEcharts1(){
		  // 基于准备好的dom，初始化echarts实例
		  var myChart = this.$echarts.init(document.getElementById('main1'));

		  var option = {
			  title: {
			  	text : "峰平谷用电量&占比"
			  },
			  tooltip: {
				trigger: 'item'
			  },
			  legend: {
				top: '5%',
				left: 'center'
			  },
			  series: [
				{
				  name: '提示',
				  type: 'pie',
				  radius: ['40%', '70%'],
				  avoidLabelOverlap: false,
				  label: {
					show: true,
					formatter: '{b}\n{c}%',
					position: ''
				  },
				  emphasis: {
					label: {
					  show: true,
					  fontSize: '40',
					  fontWeight: 'bold'
					}
				  },
				  labelLine: {
					show: false
				  },
				  data: [
					{ value: 77.58, name: '峰' },
					{ value: 13.61, name: '平'},
					{ value: 8.81, name: '谷'},
				  ]
				}
			  ]
			};

		  // 使用刚指定的配置项和数据显示图表。
		  myChart.setOption(option);
	},
	myEcharts2(){
		  // 基于准备好的dom，初始化echarts实例
		  var myChart = this.$echarts.init(document.getElementById('main2'));

		  var option = {
			title: {
				text : "设备单耗"
			},
		    tooltip: {},
		    legend: {
				right: '10%'
		    },
		    dataset: {
		        source: [
		          ['product', '每模单耗', '分钟单耗','公斤单耗'],
		          ['HT101', 3, 8, 8.8],
		          ['HT102', 6, 8, 8],
		          ['HT103', 3, 8, 7.8],
		          ['HT104', 6, 5, 6],
		          ['HT105', 3, 6, 5],
		          ['HT106', 8.3, 8, 6.8],
		          ['HT107', 4, 5, 6],
		          ['HT108', 7, 8, 7],
		          ['HT109', 9, 9, 5.8],
		          ['HT201', 6, 5, 6],
		        ]
		      },
		      xAxis: { type: 'category' },
		      yAxis: {},
		      // Declare several bar series, each will be mapped
		      // to a column of dataset.source by default.
		      series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
		  };

		  // 使用刚指定的配置项和数据显示图表。
		  myChart.setOption(option);
	},
	myEcharts3(){
		  // 基于准备好的dom，初始化echarts实例
		  var myChart = this.$echarts.init(document.getElementById('main3'));

		  var option = {
			title: {
				text : "设备有效利用率"
			},
		    tooltip: {},
		    legend: {
				right: '10%'
		    },
		    dataset: {
		        source: [
		          ['product', '能源有效利用率（运行电量/总电量*100%）', '设备有效利用率（运行时间/日历时间*100%）'],
		          ['HT101', 93, 58],
		          ['HT102', 96, 82],
		          ['HT103', 93, 58.8],
		          ['HT104', 96, 56],
		          ['HT105', 93, 65],
		          ['HT106', 98.3, 69],
		          ['HT107', 94, 56],
		          ['HT108', 97, 87],
		          ['HT109', 99, 67.8],
		          ['HT201', 96, 56],
		        ]
		      },
		      xAxis: { type: 'category' },
		      yAxis: {},
		      // Declare several bar series, each will be mapped
		      // to a column of dataset.source by default.
		      series: [{ type: 'bar' }, { type: 'bar' }]
		  };

		  // 使用刚指定的配置项和数据显示图表。
		  myChart.setOption(option);
	},
    /** 查询工厂列表 */
    getList() {
		let that = this
		that.partsProcessResultList = []
		that.partsProcessResultList1 = []
		if(null != that.queryParams.factoryName && "" != that.queryParams.factoryName){
			console.log(1)
			if("0" == that.nowIndex){
				console.log(3)
				that.partsProcessResultList = [
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"自动",
						result:"23",
						kWh:"90",
					}
				]
			}else{
				console.log(4)
				that.partsProcessResultList1 = [
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"689991",
						result:"ETSHJB",
						kWh:"90",
						kWh1:"909",
						kWh2:"920",
						kWh3:"690",
						kWh4:"890",
						kWh5:"390",
					}
				]
			}
		}else{
			console.log(2)
			if("0" == that.nowIndex){
				console.log(5)
				that.partsProcessResultList = [
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"自动",
						result:"23",
						kWh:"90",
					},
					{
						barCode:"HT202注塑机2800T",
						workOrderCode:"2021-12-02",
						assemblyCode:"自动",
						result:"25",
						kWh:"70",
					},
					{
						barCode:"HT200注塑机3800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"自动",
						result:"34",
						kWh:"60",
					},
				]
			}else{
				console.log(6)
				that.partsProcessResultList1 = [
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"689991",
						result:"ETSHJB",
						kWh:"90",
						kWh1:"909",
						kWh2:"920",
						kWh3:"690",
						kWh4:"890",
						kWh5:"390",
					},
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"689991",
						result:"ETSHJB",
						kWh:"90",
						kWh1:"909",
						kWh2:"920",
						kWh3:"690",
						kWh4:"890",
						kWh5:"390",
					},
					{
						barCode:"HT200注塑机2800T",
						workOrderCode:"2021-12-01",
						assemblyCode:"689991",
						result:"ETSHJB",
						kWh:"90",
						kWh1:"909",
						kWh2:"920",
						kWh3:"690",
						kWh4:"890",
						kWh5:"390",
					},
				]
			}
		}
    },
	/** 搜索按钮操作 */
	handleQuery() {
	  this.getList();
	},
	handleClick(tab, event) {
	  this.nowIndex = tab.index
	  this.getList();
	},
  }
};
</script>
<style scoped>
	.conOne{
		width: 98%;
		margin: 20px auto;
		display: flex;
	}
</style>
