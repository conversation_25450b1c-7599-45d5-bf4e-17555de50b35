package com.ems.energy.utils;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * SQL安全工具类测试
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@SpringBootTest
public class SqlSecurityUtilsTest {

    private final SqlSecurityUtils sqlSecurityUtils = new SqlSecurityUtils();

    @Test
    public void testSqlSecurityValidation() {
        // 测试包含字段名的SQL是否能通过验证
        String sqlWithCreateTime = "SELECT * FROM ems_energy WHERE create_time >= '2025-01-01' AND create_time <= '2025-01-31'";
        String sqlWithUpdateTime = "SELECT * FROM ems_energy WHERE update_time >= '2025-01-01'";
        String sqlWithInsertField = "SELECT insert_user FROM ems_energy";
        String sqlWithDeleteFlag = "SELECT * FROM ems_energy WHERE del_flag = '1'";
        
        // 危险的SQL语句
        String dangerousCreateSql = "CREATE TABLE test (id INT)";
        String dangerousDropSql = "DROP TABLE ems_energy";
        String dangerousAlterSql = "ALTER TABLE ems_energy ADD COLUMN test VARCHAR(50)";
        String dangerousTruncateSql = "TRUNCATE TABLE ems_energy";

        // 这些应该通过验证（包含字段名但不是DDL操作）
        System.out.println("包含create_time字段的SQL验证: " + sqlSecurityUtils.validateSql(sqlWithCreateTime));
        System.out.println("包含update_time字段的SQL验证: " + sqlSecurityUtils.validateSql(sqlWithUpdateTime));
        System.out.println("包含insert_user字段的SQL验证: " + sqlSecurityUtils.validateSql(sqlWithInsertField));
        System.out.println("包含del_flag字段的SQL验证: " + sqlSecurityUtils.validateSql(sqlWithDeleteFlag));
        
        // 这些应该被拒绝（真正的DDL操作）
        System.out.println("CREATE语句验证: " + sqlSecurityUtils.validateSql(dangerousCreateSql));
        System.out.println("DROP语句验证: " + sqlSecurityUtils.validateSql(dangerousDropSql));
        System.out.println("ALTER语句验证: " + sqlSecurityUtils.validateSql(dangerousAlterSql));
        System.out.println("TRUNCATE语句验证: " + sqlSecurityUtils.validateSql(dangerousTruncateSql));

        // 测试DML操作（应该通过）
        String selectSql = "SELECT * FROM ems_energy";
        String insertSql = "INSERT INTO ems_energy (energy_name) VALUES ('test')";
        String updateSql = "UPDATE ems_energy SET energy_name = 'test' WHERE energy_id = 1";
        String deleteSql = "DELETE FROM ems_energy WHERE energy_id = 1";

        System.out.println("SELECT语句验证: " + sqlSecurityUtils.validateSql(selectSql));
        System.out.println("INSERT语句验证: " + sqlSecurityUtils.validateSql(insertSql));
        System.out.println("UPDATE语句验证: " + sqlSecurityUtils.validateSql(updateSql));
        System.out.println("DELETE语句验证: " + sqlSecurityUtils.validateSql(deleteSql));
    }

    @Test
    public void testFluxQueryValidation() {
        // 测试InfluxDB Flux查询验证
        String validFlux = "from(bucket: \"test\") |> range(start: -1h) |> filter(fn: (r) => r[\"_measurement\"] == \"energy\")";
        String invalidFlux = "import \"system\"";
        
        System.out.println("有效Flux查询验证: " + sqlSecurityUtils.validateFluxQuery(validFlux));
        System.out.println("无效Flux查询验证: " + sqlSecurityUtils.validateFluxQuery(invalidFlux));
    }
}
