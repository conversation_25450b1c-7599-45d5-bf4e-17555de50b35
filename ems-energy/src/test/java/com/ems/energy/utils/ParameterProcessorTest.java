package com.ems.energy.utils;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * 参数处理器测试
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@SpringBootTest
public class ParameterProcessorTest {

    private final ParameterProcessor parameterProcessor = new ParameterProcessor();

    @Test
    public void testInfluxDBParameterProcessing() {
        // 测试InfluxDB参数处理
        String influxScript = "from(bucket: \"#{bucket}\") |> range(start: #{startTime}, stop: #{endTime}) |> filter(fn: (r) => r[\"_measurement\"] == \"#{measurement}\") |> filter(fn: (r) => r[\"device\"] == \"#{deviceId}\") |> aggregateWindow(every: #{interval}, fn: mean)";

        Map<String, Object> params = new HashMap<>();
        params.put("bucket", "EMS");
        params.put("startTime", "2025-07-31T08:00:00+08:00");
        params.put("endTime", "2025-07-31T09:00:00+08:00");
        params.put("measurement", "Carbon");
        params.put("deviceId", "B160_D1097bd32bff0");
        params.put("interval", "1m");

        String result = parameterProcessor.processParameters(influxScript, params, "INFLUXDB");
        System.out.println("InfluxDB处理结果:");
        System.out.println(result);

        // 验证结果不包含单引号包围的时间
        assert !result.contains("'2025-07-31T08:00:00+08:00'");
        assert result.contains("2025-07-31T08:00:00+08:00");

        // 验证duration参数不被引号包围
        assert !result.contains("\"1m\"");
        assert result.contains("every: 1m");
    }

    @Test
    public void testMySQLParameterProcessing() {
        // 测试MySQL参数处理
        String mysqlScript = "SELECT * FROM ems_energy WHERE create_time >= #{startDate} AND create_time <= #{endDate} AND energy_name = #{energyName}";
        
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", "2025-01-01");
        params.put("endDate", "2025-01-31");
        params.put("energyName", "电力");

        String result = parameterProcessor.processParameters(mysqlScript, params, "MYSQL");
        System.out.println("MySQL处理结果:");
        System.out.println(result);
        
        // 验证结果包含单引号包围的字符串
        assert result.contains("'2025-01-01'");
        assert result.contains("'2025-01-31'");
        assert result.contains("'电力'");
    }

    @Test
    public void testSpecialStringDetection() {
        ParameterProcessor processor = new ParameterProcessor();

        // 测试时间字符串检测
        String[] timeStrings = {
            "2025-07-31T08:00:00+08:00",
            "2025-07-31T08:00:00Z",
            "2025-01-01 10:30:00",
            "now()",
            "-1h",
            "-30m"
        };

        // 测试duration字符串检测
        String[] durationStrings = {
            "1m",
            "30s",
            "1h",
            "5ms",
            "1d",
            "2w",
            "1mo",
            "1y"
        };

        String[] normalStrings = {
            "Carbon",
            "B160_D1097bd32bff0",
            "EMS",
            "device_001"
        };

        System.out.println("时间字符串测试:");
        for (String timeStr : timeStrings) {
            System.out.println(timeStr + " -> 应该被识别为时间字符串");
        }

        System.out.println("Duration字符串测试:");
        for (String durationStr : durationStrings) {
            System.out.println(durationStr + " -> 应该被识别为duration字符串");
        }

        System.out.println("普通字符串测试:");
        for (String normalStr : normalStrings) {
            System.out.println(normalStr + " -> 应该被识别为普通字符串");
        }
    }
}
