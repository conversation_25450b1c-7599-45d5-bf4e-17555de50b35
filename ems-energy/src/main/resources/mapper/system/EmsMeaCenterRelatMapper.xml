<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsMeaCenterRelatMapper">

    <resultMap type="EmsMeaCenterRelat" id="EmsMeaCenterRelatResult">
        <result property="relatId"    column="relat_id"    />
        <result property="keepType"    column="keep_type"    />
        <result property="custName"    column="cust_name"    />
        <result property="costCenterId"    column="cost_center_id"    />
        <result property="energyId"    column="energy_id"    />
        <result property="relationExpression"    column="relation_expression"    />
        <result property="description"    column="description"    />
        <result property="recordList"    column="record_list"    />
        <result property="factoryFirmId"    column="factory_firm_id"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEmsMeaCenterRelatVo">
        select relat_id, keep_type, cust_name, cost_center_id, energy_id, relation_expression, description, del_flag,factory_firm_id from ems_mea_center_relat
    </sql>

    <select id="selectEmsMeaCenterRelatList" parameterType="EmsMeaCenterRelat" resultMap="EmsMeaCenterRelatResult">
        <include refid="selectEmsMeaCenterRelatVo"/>
        <where>
            del_flag=1
            <if test="keepType != null  and keepType != ''"> and keep_type = #{keepType}</if>
            <if test="custName != null  and custName != ''"> and cust_name like concat('%', #{custName}, '%')</if>
            <if test="costCenterId != null "> and cost_center_id = #{costCenterId}</if>
            <if test="energyId != null "> and energy_id = #{energyId}</if>
            <if test="relationExpression != null  and relationExpression != ''"> and relation_expression = #{relationExpression}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="delFlag != null  and delFlag != ''"> and del_flag = #{delFlag}</if>
        </where>
    </select>

    <select id="selectEmsMeaCenterRelatByRelatId" parameterType="Long" resultMap="EmsMeaCenterRelatResult">
        SELECT * FROM ems_mea_center_relat
        where relat_id = #{relatId}
        and del_flag=1
    </select>
    <select id="selectEmsMeaCenterRelatpagelist" resultType="com.ems.energy.domain.vo.EmsMeaCenterRelatVo">
        SELECT
        a1.relat_id AS relat_id,
        a1.cust_name AS cust_name,
        a1.description AS description,
        a1.relat_code AS relat_code,
        a1.relation_expression AS relation_expression,
        a3.center_name AS center_name,
        a4.mea_code AS mea_code,
        a5.energy_name AS energy_name,
        factory.dept_name AS dept_name
        FROM
        ems_mea_center_relat a1
        LEFT JOIN ems_cost_center a3 ON a1.cost_center_id = a3.center_id
        LEFT JOIN ems_measure a4 ON a1.relation_expression = a4.mea_id
        LEFT JOIN ems_energy a5 ON a1.energy_id = a5.energy_id
        LEFT JOIN ems_factory factory ON factory_firm_id = factory.dept_id
        <where>
            a1.del_flag=1
            <if test="dta.keepType != null  and  dta.keepType != ''"> and a1.keep_type = #{dta.keepType}</if>
            <if test="dta.costCenterId != null  and dta.costCenterId != ''"> and a1.cost_center_id = #{dta.costCenterId}</if>
            <if test="dta.energyId != null  and dta.energyId != ''"> and a1.energy_id = #{dta.energyId}</if>
        </where>

    </select>
    <select id="selectEmscenterRealt" resultType="java.lang.String">
        SELECT mea_name FROM ems_measure WHERE mea_id= #{integer} and del_flag=1
    </select>
    <select id="selectCount" resultType="int">
        select count(*) from ems_measure
        where del_flag=1
    </select>

    <insert id="insertEmsMeaCenterRelat" parameterType="EmsMeaCenterRelat">
        insert into ems_mea_center_relat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dto.relatId != null">relat_id,</if>
            <if test="dto.keepType != null and dto.keepType != ''">keep_type,</if>
            <if test="dto.custName != null">cust_name,</if>
            <if test="dto.relatCode != null">relat_code,</if>
            <if test="dto.costCenterId != null">cost_center_id,</if>
            <if test="dto.energyId != null">energy_id,</if>
            <if test="dto.relationExpression != null">relation_expression,</if>
            <if test="dto.description != null">description,</if>
            <if test="dto.recordList != null">record_list,</if>
            <if test="dto.factoryFirmId != null">factory_firm_id,</if>
            <if test="dto.delFlag != null">del_flag,</if>
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dto.relatId != null">#{dto.relatId},</if>
            <if test="dto.keepType != null and dto.keepType != ''">#{dto.keepType},</if>
            <if test="dto.custName != null">#{dto.custName},</if>
             <if test="dto.relatCode != null">#{dto.relatCode},</if>
            <if test="dto.costCenterId != null">#{dto.costCenterId},</if>
            <if test="dto.energyId != null">#{dto.energyId},</if>
            <if test="dto.relationExpression != null">#{dto.relationExpression},</if>
            <if test="dto.description != null">#{dto.description},</if>
            <if test="dto.recordList != null">#{dto.recordList},</if>
            <if test="dto.factoryFirmId != null">#{dto.factoryFirmId},</if>
            <if test="dto.delFlag != null">#{dto.delFlag},</if>
         </trim>
    </insert>

    <update id="updateEmsMeaCenterRelat" parameterType="EmsMeaCenterRelatListDTO">
        update ems_mea_center_relat
        <trim prefix="SET" suffixOverrides=",">
            <if test="dto.keepType != null and dto.keepType != ''">keep_type = #{dto.keepType},</if>
            <if test="dto.custName != null">cust_name = #{dto.custName},</if>
            <if test="dto.costCenterId != null">cost_center_id = #{dto.costCenterId},</if>
            <if test="dto.energyId != null">energy_id = #{dto.energyId},</if>
            <if test="dto.relationExpression != null">relation_expression = #{dto.relationExpression},</if>
            <if test="dto.description != null">description = #{dto.description},</if>
            <if test="dto.recordList != null">record_list = #{dto.recordList},</if>
            <if test="dto.factoryFirmId != null">factory_firm_id = #{dto.factoryFirmId},</if>
            <if test="dto.delFlag != null">del_flag = #{dto.delFlag},</if>
        </trim>
        where relat_id = #{dto.relatId}
    </update>

    <update id="deleteEmsMeaCenterRelatByRelatId" parameterType="Long">
        update  ems_mea_center_relat set del_flag=0 where relat_id = #{relatId}
    </update>

    <update id="deleteEmsMeaCenterRelatByRelatIds" parameterType="String">
        update  ems_mea_center_relat
        set del_flag=0
        where relat_id in
        <foreach item="relatId" collection="array" open="(" separator="," close=")">
            #{relatId}
        </foreach>
    </update>
</mapper>
