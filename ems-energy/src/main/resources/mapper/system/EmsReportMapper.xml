<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsReportMapper">

    <resultMap type="EmsReport" id="EmsReportResult">
        <result property="reportId"    column="report_id"    />
        <result property="relatId"    column="relat_id"    />
        <result property="dockTime"    column="dock_time"    />
        <result property="deviceStatus"    column="device_status"    />
        <result property="dataJson"    column="data_json"    />
        <result property="runTime"    column="run_time"    />
        <result property="powerWasting"    column="power_wasting"    />
    </resultMap>

    <sql id="selectEmsReportVo">
        select report_id, relat_id, dock_time, device_status, data_json, run_time, power_wasting from ems_report
    </sql>

    <select id="selectEmsReportList" parameterType="EmsReport" resultMap="EmsReportResult">
        <include refid="selectEmsReportVo"/>
        <where>
            <if test="relatId != null "> and relat_id = #{relatId}</if>
            <if test="dockTime != null "> and dock_time = #{dockTime}</if>
            <if test="deviceStatus != null  and deviceStatus != ''"> and device_status = #{deviceStatus}</if>
            <if test="dataJson != null  and dataJson != ''"> and data_json = #{dataJson}</if>
            <if test="runTime != null "> and run_time = #{runTime}</if>
            <if test="powerWasting != null "> and power_wasting = #{powerWasting}</if>
        </where>
    </select>

    <select id="selectEmsReportByReportId" parameterType="Long" resultMap="EmsReportResult">
        <include refid="selectEmsReportVo"/>
        where report_id = #{reportId}
    </select>
    <select id="selectEmsDailyReport" resultType="com.ems.energy.domain.EmsReport">
        <include refid="selectEmsReportVo"/>
         where dock_time like concat('%', #{yesterday}, '%')
    </select>

    <insert id="insertEmsReport" parameterType="EmsReport" useGeneratedKeys="true" keyProperty="reportId">
        insert into ems_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="relatId != null">relat_id,</if>
            <if test="dockTime != null">dock_time,</if>
            <if test="deviceStatus != null">device_status,</if>
            <if test="dataJson != null">data_json,</if>
            <if test="runTime != null">run_time,</if>
            <if test="powerWasting != null">power_wasting,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="relatId != null">#{relatId},</if>
            <if test="dockTime != null">#{dockTime},</if>
            <if test="deviceStatus != null">#{deviceStatus},</if>
            <if test="dataJson != null">#{dataJson},</if>
            <if test="runTime != null">#{runTime},</if>
            <if test="powerWasting != null">#{powerWasting},</if>
         </trim>
    </insert>

    <update id="updateEmsReport" parameterType="EmsReport">
        update ems_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="relatId != null">relat_id = #{relatId},</if>
            <if test="dockTime != null">dock_time = #{dockTime},</if>
            <if test="deviceStatus != null">device_status = #{deviceStatus},</if>
            <if test="dataJson != null">data_json = #{dataJson},</if>
            <if test="runTime != null">run_time = #{runTime},</if>
            <if test="powerWasting != null">power_wasting = #{powerWasting},</if>
        </trim>
        where report_id = #{reportId}
    </update>

    <delete id="deleteEmsReportByReportId" parameterType="Long">
        delete from ems_report where report_id = #{reportId}
    </delete>

    <delete id="deleteEmsReportByReportIds" parameterType="String">
        delete from ems_report where report_id in
        <foreach item="reportId" collection="array" open="(" separator="," close=")">
            #{reportId}
        </foreach>
    </delete>
</mapper>
