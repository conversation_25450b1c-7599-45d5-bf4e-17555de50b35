<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsMonthlyMapper">
    
    <resultMap type="EmsMonthly" id="EmsMonthlyResult">
        <result property="dailyId"    column="daily_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="prodCode"    column="prod_code"    />
        <result property="prodName"    column="prod_name"    />
        <result property="outputPiece"    column="output_piece"    />
        <result property="outputModel"    column="output_model"    />
        <result property="allWeight"    column="all_weight"    />
        <result property="prodTime"    column="prod_time"    />
        <result property="powerCons"    column="power_cons"    />
        <result property="minuteCons"    column="minute_cons"    />
        <result property="deviceId"    column="device_id"    />
    </resultMap>

    <sql id="selectEmsMonthlyVo">
        select daily_id, create_time, prod_code, prod_name, output_piece, output_model, all_weight, prod_time, power_cons, minute_cons, device_id from ems_monthly
    </sql>

    <select id="selectEmsMonthlyList" parameterType="EmsMonthly" resultMap="EmsMonthlyResult">
        <include refid="selectEmsMonthlyVo"/>
        <where>  
            <if test="prodCode != null  and prodCode != ''"> and prod_code = #{prodCode}</if>
            <if test="prodName != null  and prodName != ''"> and prod_name like concat('%', #{prodName}, '%')</if>
            <if test="outputPiece != null "> and output_piece = #{outputPiece}</if>
            <if test="outputModel != null "> and output_model = #{outputModel}</if>
            <if test="allWeight != null "> and all_weight = #{allWeight}</if>
            <if test="prodTime != null "> and prod_time = #{prodTime}</if>
            <if test="powerCons != null "> and power_cons = #{powerCons}</if>
            <if test="minuteCons != null "> and minute_cons = #{minuteCons}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
        </where>
    </select>
    
    <select id="selectEmsMonthlyByDailyId" parameterType="Long" resultMap="EmsMonthlyResult">
        <include refid="selectEmsMonthlyVo"/>
        where daily_id = #{dailyId}
    </select>
        
    <insert id="insertEmsMonthly" parameterType="EmsMonthly" useGeneratedKeys="true" keyProperty="dailyId">
        insert into ems_monthly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="prodCode != null">prod_code,</if>
            <if test="prodName != null">prod_name,</if>
            <if test="outputPiece != null">output_piece,</if>
            <if test="outputModel != null">output_model,</if>
            <if test="allWeight != null">all_weight,</if>
            <if test="prodTime != null">prod_time,</if>
            <if test="powerCons != null">power_cons,</if>
            <if test="minuteCons != null">minute_cons,</if>
            <if test="deviceId != null">device_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="prodCode != null">#{prodCode},</if>
            <if test="prodName != null">#{prodName},</if>
            <if test="outputPiece != null">#{outputPiece},</if>
            <if test="outputModel != null">#{outputModel},</if>
            <if test="allWeight != null">#{allWeight},</if>
            <if test="prodTime != null">#{prodTime},</if>
            <if test="powerCons != null">#{powerCons},</if>
            <if test="minuteCons != null">#{minuteCons},</if>
            <if test="deviceId != null">#{deviceId},</if>
         </trim>
    </insert>

    <update id="updateEmsMonthly" parameterType="EmsMonthly">
        update ems_monthly
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="prodCode != null">prod_code = #{prodCode},</if>
            <if test="prodName != null">prod_name = #{prodName},</if>
            <if test="outputPiece != null">output_piece = #{outputPiece},</if>
            <if test="outputModel != null">output_model = #{outputModel},</if>
            <if test="allWeight != null">all_weight = #{allWeight},</if>
            <if test="prodTime != null">prod_time = #{prodTime},</if>
            <if test="powerCons != null">power_cons = #{powerCons},</if>
            <if test="minuteCons != null">minute_cons = #{minuteCons},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
        </trim>
        where daily_id = #{dailyId}
    </update>

    <delete id="deleteEmsMonthlyByDailyId" parameterType="Long">
        delete from ems_monthly where daily_id = #{dailyId}
    </delete>

    <delete id="deleteEmsMonthlyByDailyIds" parameterType="String">
        delete from ems_monthly where daily_id in 
        <foreach item="dailyId" collection="array" open="(" separator="," close=")">
            #{dailyId}
        </foreach>
    </delete>
</mapper>