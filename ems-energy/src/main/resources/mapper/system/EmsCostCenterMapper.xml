<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsCostCenterMapper">

    <resultMap type="EmsCostCenter" id="EmsCostCenterResult">
        <result property="centerId" column="center_id"/>
        <result property="centerName" column="center_name"/>
        <result property="ancestors" column="ancestors"/>
        <result property="centerCode" column="center_code"/>
        <result property="parentCenterId" column="parent_center_id"/>
        <result property="sysType" column="sys_type"/>
        <result property="useStatus" column="use_status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="orderNum" column="order_num"/>
        <result property="subName" column="sub_name"/>
    </resultMap>

    <sql id="selectEmsCostCenterVo">
        select center_id,
               center_name,
               ancestors,
               order_num,
               center_code,
               parent_center_id,
               sys_type,
               use_status,
               sub_name,
               del_flag
        from ems_cost_center
    </sql>

    <select id="selectEmsCostCenterList" parameterType="EmsCostCenter" resultMap="EmsCostCenterResult">
        <include refid="selectEmsCostCenterVo"/>
        <where>
            del_flag=1
            <if test="centerName != null  and centerName != ''">and center_name like concat('%', #{centerName}, '%')
            </if>
            <if test="ancestors != null  and ancestors != ''">and ancestors = #{ancestors}</if>
            <if test="centerCode != null  and centerCode != ''">and center_code = #{centerCode}</if>
            <if test="parentCenterId != null ">and parent_center_id = #{parentCenterId}</if>
            <if test="sysType != null  and sysType != ''">and sys_type = #{sysType}</if>
            <if test="useStatus != null  and useStatus != ''">and use_status = #{useStatus}</if>
        </where>
    </select>

    <select id="selectEmsCostCenterByCenterId" parameterType="Long" resultMap="EmsCostCenterResult">
        <include refid="selectEmsCostCenterVo"/>
        where center_id = #{centerId}
        and del_flag=1
    </select>
    <select id="checkCostcentNameUnique" resultType="com.ems.energy.domain.EmsFactory">
        <include refid="selectEmsCostCenterVo"/>
        where center_name = #{centerName} and parent_center_id = #{parentCenterId} limit 1
    </select>
    <select id="selectFactoryById" resultType="com.ems.energy.domain.EmsCostCenter">
        <include refid="selectEmsCostCenterVo"/>
        where center_id = #{parentCenterId}
    </select>

    <select id="selectEmsCostCenterByPage" resultType="com.ems.energy.domain.vo.EmsCostCenterVo">
        select e1.*,e2.center_name parent_center_name from ems_cost_center e1 left join ems_cost_center e2
        on e1.parent_center_id=e2.center_id and e2.del_flag = 1
        <where>
            e1.del_flag=1
            <if test="e1.centerCode!=null and e1.centerCode!=''">and e1.center_code like concat('%', #{e1.centerCode}, '%')</if>
            <if test="e1.centerName!=null and e1.centerName!=''">and e1.center_name like concat('%', #{e1.centerName}, '%')</if>
        </where>
  </select>
    <select id="checkDeptNameUnique" resultType="com.ems.energy.domain.EmsCostCenter">
            <include refid="selectEmsCostCenterVo"/>
            where center_name=#{centerName} and parent_center_id = #{parentCenterId} limit 1
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(*) from ems_cost_center
    </select>
    <insert id="EmsCostCenter" parameterType="EmsCostCenter">
         into ems_cost_center
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="centerId != null">center_id,</if>
            <if test="centerName != null">center_name,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="centerCode != null">center_code,</if>
            <if test="parentCenterId != null">parent_center_id,</if>
            <if test="sysType != null">sys_type,</if>
            <if test="useStatus != null">use_status,</if>
            <if test="subName != null">sub_name,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="centerId != null">#{centerId},</if>
            <if test="centerName != null">#{centerName},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="centerCode != null">#{centerCode},</if>
            <if test="parentCenterId != null">#{parentCenterId},</if>
            <if test="sysType != null">#{sysType},</if>
            <if test="useStatus != null">#{useStatus},</if>
            <if test="subName != null">#{subName},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>
    <insert id="insertCostent" parameterType="EmsCostCenter">
        insert into ems_cost_center
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="centerName != null">center_name,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="centerCode != null">center_code,</if>
            <if test="parentCenterId != null">parent_center_id,</if>
            <if test="sysType != null">sys_type,</if>
            <if test="useStatus != null">use_status,</if>
            <if test="subName != null">sub_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="centerName != null">#{centerName},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="centerCode != null">#{centerCode},</if>
            <if test="parentCenterId != null">#{parentCenterId},</if>
            <if test="sysType != null">#{sysType},</if>
            <if test="useStatus != null">#{useStatus},</if>
            <if test="subName != null">#{subName},</if>
        </trim>
    </insert>
    <insert id="insertEmsCostCenter">
        insert into ems_cost_center
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="centerName != null">center_name,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="centerCode != null">center_code,</if>
            <if test="parentCenterId != null">parent_center_id,</if>
            <if test="sysType != null">sys_type,</if>
            <if test="useStatus != null">use_status,</if>
            <if test="subName != null">sub_name,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="centerName != null">#{centerName},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="centerCode != null">#{centerCode},</if>
            <if test="parentCenterId != null">#{parentCenterId},</if>
            <if test="sysType != null">#{sysType},</if>
            <if test="useStatus != null">#{useStatus},</if>
            <if test="subName != null">#{subName},</if>
            <if test="delFlag != null">1,</if>
        </trim>
    </insert>

    <update id="updateEmsCostCenter" parameterType="EmsCostCenter">
        update ems_cost_center
        <trim prefix="SET" suffixOverrides=",">
            <if test="centerName != null">center_name = #{centerName},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="centerCode != null">center_code = #{centerCode},</if>
            <if test="parentCenterId != null">parent_center_id = #{parentCenterId},</if>
            <if test="sysType != null">sys_type = #{sysType},</if>
            <if test="useStatus != null">use_status = #{useStatus},</if>
            <if test="subName != null">sub_name = #{subName},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where center_id = #{centerId}
    </update>

    <update id="deleteEmsCostCenterByCenterId" parameterType="Long">
        update
         ems_cost_center
        set del_flag=0
        where center_id = #{centerId}
    </update>

    <update id="deleteEmsCostCenterByCenterIds" parameterType="String">
        update ems_cost_center
        set del_flag=0
        where center_id in
        <foreach item="centerId" collection="array" open="(" separator="," close=")">
            #{centerId}
        </foreach>
    </update>
</mapper>
