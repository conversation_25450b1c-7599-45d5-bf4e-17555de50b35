<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsDailyDataMapper">

    <resultMap type="EmsDailyData" id="EmsDailyDataResult">
        <result property="dailyId"    column="daily_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="prodCode"    column="prod_code"    />
        <result property="prodName"    column="prod_name"    />
        <result property="outputPiece"    column="output_piece"    />
        <result property="outputModel"    column="output_model"    />
        <result property="allWeight"    column="all_weight"    />
        <result property="prodTime"    column="prod_time"    />
        <result property="powerCons"    column="power_cons"    />
        <result property="minuteCons"    column="minute_cons"    />
        <result property="deviceId"    column="device_id"    />
    </resultMap>

    <sql id="selectEmsDailyDataVo">
        select daily_id, create_time, prod_code, prod_name, output_piece, output_model, all_weight, prod_time, power_cons, minute_cons, device_id from ems_daily_data
    </sql>

    <select id="selectEmsDailyDataList" parameterType="EmsDailyData" resultMap="EmsDailyDataResult">
        <include refid="selectEmsDailyDataVo"/>
        <where>
            <if test="prodCode != null  and prodCode != ''"> and prod_code = #{prodCode}</if>
            <if test="prodName != null  and prodName != ''"> and prod_name like concat('%', #{prodName}, '%')</if>
            <if test="outputPiece != null "> and output_piece = #{outputPiece}</if>
            <if test="outputModel != null "> and output_model = #{outputModel}</if>
            <if test="allWeight != null "> and all_weight = #{allWeight}</if>
            <if test="prodTime != null "> and prod_time = #{prodTime}</if>
            <if test="powerCons != null "> and power_cons = #{powerCons}</if>
            <if test="minuteCons != null "> and minute_cons = #{minuteCons}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
        </where>
    </select>

    <select id="selectEmsDailyDataByDailyId" parameterType="Long" resultMap="EmsDailyDataResult">
        <include refid="selectEmsDailyDataVo"/>
        where daily_id = #{dailyId}
    </select>
    <select id="selectEmsDailyDatapageList" resultType="com.ems.energy.domain.EmsDailyDataDTO">
        SELECT
            *
        FROM
            ems_daily
        WHERE
            DATE_FORMAT( create_time, '%Y-%m' ) = #{format}
    </select>

    <insert id="insertEmsDailyData" parameterType="EmsDailyData" useGeneratedKeys="true" keyProperty="dailyId">
        insert into ems_daily_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="prodCode != null">prod_code,</if>
            <if test="prodName != null">prod_name,</if>
            <if test="outputPiece != null">output_piece,</if>
            <if test="outputModel != null">output_m odel,</if>
            <if test="allWeight != null">all_weight,</if>
            <if test="prodTime != null">prod_time,</if>
            <if test="powerCons != null">power_cons,</if>
            <if test="minuteCons != null">minute_cons,</if>
            <if test="deviceId != null">device_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="prodCode != null">#{prodCode},</if>
            <if test="prodName != null">#{prodName},</if>
            <if test="outputPiece != null">#{outputPiece},</if>
            <if test="outputModel != null">#{outputModel},</if>
            <if test="allWeight != null">#{allWeight},</if>
            <if test="prodTime != null">#{prodTime},</if>
            <if test="powerCons != null">#{powerCons},</if>
            <if test="minuteCons != null">#{minuteCons},</if>
            <if test="deviceId != null">#{deviceId},</if>
         </trim>
    </insert>

    <update id="updateEmsDailyData" parameterType="EmsDailyData">
        update ems_daily_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="prodCode != null">prod_code = #{prodCode},</if>
            <if test="prodName != null">prod_name = #{prodName},</if>
            <if test="outputPiece != null">output_piece = #{outputPiece},</if>
            <if test="outputModel != null">output_model = #{outputModel},</if>
            <if test="allWeight != null">all_weight = #{allWeight},</if>
            <if test="prodTime != null">prod_time = #{prodTime},</if>
            <if test="powerCons != null">power_cons = #{powerCons},</if>
            <if test="minuteCons != null">minute_cons = #{minuteCons},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
        </trim>
        where daily_id = #{dailyId}
    </update>

    <delete id="deleteEmsDailyDataByDailyId" parameterType="Long">
        delete from ems_daily_data where daily_id = #{dailyId}
    </delete>

    <delete id="deleteEmsDailyDataByDailyIds" parameterType="String">
        delete from ems_daily_data where daily_id in
        <foreach item="dailyId" collection="array" open="(" separator="," close=")">
            #{dailyId}
        </foreach>
    </delete>
</mapper>
