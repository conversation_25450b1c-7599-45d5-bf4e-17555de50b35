<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsEnergyMapper">

    <resultMap type="EmsEnergy" id="EmsEnergyResult">
        <result property="energyId"    column="energy_id"    />
        <result property="energyCode"    column="energy_code"    />
        <result property="energyName"    column="energy_name"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>
    <sql id="selectEmsEnergyVo">
        select energy_id, energy_code, energy_name, del_flag from ems_energy
    </sql>

    <select id="selectEmsEnergyList" parameterType="EmsEnergy" resultMap="EmsEnergyResult">
        <include refid="selectEmsEnergyVo"/>
        <where>
            <if test="energyCode != null  and energyCode != ''"> and energy_code = #{energyCode}</if>
            <if test="energyName != null  and energyName != ''"> and energy_name like concat('%', #{energyName}, '%')</if>
            <if test="delFlag != null  and delFlag != ''"> and del_flag = #{delFlag}</if>
        </where>
    </select>

    <select id="selectEmsEnergyByEnergyId" parameterType="Long" resultMap="EmsEnergyResult">
        <include refid="selectEmsEnergyVo"/>
        where energy_id = #{energyId}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(*) from ems_energy
    </select>

    <insert id="insertEmsEnergy" parameterType="EmsEnergy">
        insert into ems_energy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="energyId != null">energy_id,</if>
            <if test="energyCode != null">energy_code,</if>
            <if test="energyName != null">energy_name,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="energyId != null">#{energyId},</if>
            <if test="energyCode != null">#{energyCode},</if>
            <if test="energyName != null">#{energyName},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEmsEnergy" parameterType="EmsEnergy">
        update ems_energy
        <trim prefix="SET" suffixOverrides=",">
            <if test="energyCode != null">energy_code = #{energyCode},</if>
            <if test="energyName != null">energy_name = #{energyName},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where energy_id = #{energyId}
    </update>

    <update id="deleteEmsEnergyByEnergyId" parameterType="Long">
        update  ems_energy set del_flag=0 where energy_id = #{energyId}
    </update>

    <update id="deleteEmsEnergyByEnergyIds" parameterType="String">
        update ems_energy
        set del_flag=0
        where energy_id in
        <foreach item="energyId" collection="array" open="(" separator="," close=")">
            #{energyId}
        </foreach>
    </update>
</mapper>
