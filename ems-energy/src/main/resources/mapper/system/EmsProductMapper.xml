<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsProductMapper">
    
    <resultMap type="EmsProduct" id="EmsProductResult">
        <result property="proId"    column="pro_id"    />
        <result property="proName"    column="pro_name"    />
        <result property="erpId"    column="erp_id"    />
        <result property="equiva"    column="equiva"    />
        <result property="unit"    column="unit"    />
        <result property="proCode"    column="pro_code"    />
    </resultMap>

    <sql id="selectEmsProductVo">
        select pro_id, pro_name, erp_id, equiva, unit, pro_code from ems_product
    </sql>

    <select id="selectEmsProductList" parameterType="EmsProduct" resultMap="EmsProductResult">
        <include refid="selectEmsProductVo"/>
        <where>  
            <if test="proName != null  and proName != ''"> and pro_name like concat('%', #{proName}, '%')</if>
            <if test="erpId != null "> and erp_id = #{erpId}</if>
            <if test="equiva != null "> and equiva = #{equiva}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="proCode != null  and proCode != ''"> and pro_code = #{proCode}</if>
        </where>
    </select>
    
    <select id="selectEmsProductByProId" parameterType="Long" resultMap="EmsProductResult">
        <include refid="selectEmsProductVo"/>
        where pro_id = #{proId}
    </select>
        
    <insert id="insertEmsProduct" parameterType="EmsProduct" useGeneratedKeys="true" keyProperty="proId">
        insert into ems_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="proName != null">pro_name,</if>
            <if test="erpId != null">erp_id,</if>
            <if test="equiva != null">equiva,</if>
            <if test="unit != null">unit,</if>
            <if test="proCode != null">pro_code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="proName != null">#{proName},</if>
            <if test="erpId != null">#{erpId},</if>
            <if test="equiva != null">#{equiva},</if>
            <if test="unit != null">#{unit},</if>
            <if test="proCode != null">#{proCode},</if>
         </trim>
    </insert>

    <update id="updateEmsProduct" parameterType="EmsProduct">
        update ems_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="proName != null">pro_name = #{proName},</if>
            <if test="erpId != null">erp_id = #{erpId},</if>
            <if test="equiva != null">equiva = #{equiva},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="proCode != null">pro_code = #{proCode},</if>
        </trim>
        where pro_id = #{proId}
    </update>

    <delete id="deleteEmsProductByProId" parameterType="Long">
        delete from ems_product where pro_id = #{proId}
    </delete>

    <delete id="deleteEmsProductByProIds" parameterType="String">
        delete from ems_product where pro_id in 
        <foreach item="proId" collection="array" open="(" separator="," close=")">
            #{proId}
        </foreach>
    </delete>
</mapper>