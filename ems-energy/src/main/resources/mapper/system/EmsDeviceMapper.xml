<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsDeviceMapper">

    <resultMap type="EmsDevice" id="EmsDeviceResult">
        <result property="equId"    column="equ_id"    />
        <result property="deviceNum"    column="device_num"    />
        <result property="deviceName"    column="device_name"    />
        <result property="meaId"    column="mea_id"    />
        <result property="energyId"    column="energy_id"    />
    </resultMap>

    <sql id="selectEmsDeviceVo">
        select equ_id, device_num, device_name, mea_id, energy_id from ems_device
    </sql>

    <select id="selectEmsDeviceList" parameterType="EmsDeviceVo" resultType="com.ems.energy.domain.vo.EmsDeviceVo">
        SELECT device.*,energy.energy_name,measure.mea_code FROM `ems_device` device
        left join ems_energy energy
        on device.energy_id=energy.energy_id
        left join ems_measure measure
        on measure.mea_id=device.mea_id
        <where>
            <if test="deviceNum != null  and deviceNum != ''"> and device.device_num like concat('%', #{deviceNum}, '%')</if>
            <if test="deviceName != null  and deviceName != ''"> and device.device_name like concat('%', #{deviceName}, '%')</if>
            <if test="meaId != null "> and device.mea_id = #{meaId}</if>
            <if test="energyId != null "> and device.energy_id = #{energyId}</if>
            <if test="meaCode != null and meaCode != ''"> and measure.mea_code like concat('%', #{meaCode}, '%')</if>
        </where>
    </select>

    <select id="selectEmsDeviceByEquId" parameterType="Long" resultMap="EmsDeviceResult">
        <include refid="selectEmsDeviceVo"/>
        where equ_id = #{equId}
    </select>

    <insert id="insertEmsDevice" parameterType="EmsDevice" useGeneratedKeys="true" keyProperty="equId">
        insert into ems_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceNum != null">device_num,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="meaId != null">mea_id,</if>
            <if test="energyId != null">energy_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceNum != null">#{deviceNum},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="meaId != null">#{meaId},</if>
            <if test="energyId != null">#{energyId},</if>
         </trim>
    </insert>

    <update id="updateEmsDevice" parameterType="EmsDevice">
        update ems_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceNum != null">device_num = #{deviceNum},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="meaId != null">mea_id = #{meaId},</if>
            <if test="energyId != null">energy_id = #{energyId},</if>
        </trim>
        where equ_id = #{equId}
    </update>

    <delete id="deleteEmsDeviceByEquId" parameterType="Long">
        delete from ems_device where equ_id = #{equId}
    </delete>

    <delete id="deleteEmsDeviceByEquIds" parameterType="String">
        delete from ems_device where equ_id in
        <foreach item="equId" collection="array" open="(" separator="," close=")">
            #{equId}
        </foreach>
    </delete>
</mapper>
