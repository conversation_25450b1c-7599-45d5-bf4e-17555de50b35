<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsFactoryMapper">

    <resultMap type="EmsFactory" id="EmsFactoryResult">
        <result property="deptId"    column="dept_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="deptName"    column="dept_name"    />
        <result property="orderNum"    column="order_num"    />
        <result property="leader"    column="leader"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEmsFactoryVo">
        select dept_id, parent_id, ancestors,sub_name, dept_name, order_num, leader, phone, email, status, del_flag, create_by, create_time, update_by, update_time from ems_factory
    </sql>

    <select id="selectEmsFactoryList" parameterType="EmsFactory" resultMap="EmsFactoryResult">
        <include refid="selectEmsFactoryVo"/>
        <where>
            del_flag=0
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
            <if test="leader != null  and leader != ''"> and leader = #{leader}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectEmsFactoryByDeptId" parameterType="Long" resultMap="EmsFactoryResult">
        <include refid="selectEmsFactoryVo"/>
        where dept_id = #{deptId}
        and del_flag=1
    </select>
    <select id="checkDeptNameUnique" resultType="com.ems.energy.domain.EmsFactory">
        <include refid="selectEmsFactoryVo"/>
       where dept_name=#{deptName} and parent_id = #{parentId} limit 1
    </select>
    <select id="selectFactoryById" resultType="com.ems.energy.domain.EmsFactory">
        <include refid="selectEmsFactoryVo"/>
        where dept_id = #{deptId}
    </select>
    <insert id="insertEmsFactory" parameterType="EmsFactory" useGeneratedKeys="true" keyProperty="deptId">
        insert into ems_factory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="leader != null">leader,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="leader != null">#{leader},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">sysdate(),</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEmsFactory" parameterType="EmsFactory">
        update ems_factory
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where dept_id = #{deptId}
    </update>

    <update id="deleteEmsFactoryByDeptId" parameterType="Long">
        update  ems_factory set del_flag=0 where dept_id = #{deptId}
    </update>

    <update id="deleteEmsFactoryByDeptIds" parameterType="String">
        update ems_factory
        set del_flag=0
        where dept_id in
        <foreach item="deptId" collection="array" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </update>
</mapper>
