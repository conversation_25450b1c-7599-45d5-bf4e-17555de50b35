<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsCostMeaMapper">

    <resultMap type="EmsCostMea" id="EmsCostMeaResult">
        <result property="id"    column="id"    />
        <result property="costCenterId"    column="cost_center_id"    />
        <result property="meaId"    column="mea_id"    />
    </resultMap>

    <sql id="selectEmsCostMeaVo">
        select id, cost_center_id, mea_id from ems_cost_mea
    </sql>

    <select id="selectEmsCostMeaList" parameterType="EmsCostMea" resultMap="EmsCostMeaResult">
        <include refid="selectEmsCostMeaVo"/>
        <where>
            <if test="costCenterId != null "> and cost_center_id = #{costCenterId}</if>
            <if test="meaId != null "> and mea_id = #{meaId}</if>
        </where>
    </select>

    <select id="selectEmsCostMeaById" parameterType="Long" resultMap="EmsCostMeaResult">
        <include refid="selectEmsCostMeaVo"/>
        where id = #{id}
    </select>

    <insert id="insertEmsCostMea" parameterType="EmsCostMea" useGeneratedKeys="true" keyProperty="id">
        insert into ems_cost_mea
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="costCenterId != null">cost_center_id,</if>
            <if test="meaId != null">mea_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="costCenterId != null">#{costCenterId},</if>
            <if test="meaId != null">#{meaId},</if>
         </trim>
    </insert>

    <update id="updateEmsCostMea" parameterType="EmsCostMea">
        update ems_cost_mea
        <trim prefix="SET" suffixOverrides=",">
            <if test="costCenterId != null">cost_center_id = #{costCenterId},</if>
            <if test="meaId != null">mea_id = #{meaId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmsCostMeaById" parameterType="Long">
        delete from ems_cost_mea where id = #{id}
    </delete>

    <delete id="deleteEmsCostMeaByIds" parameterType="String">
        delete from ems_cost_mea where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteEmsCostMeaByCostCenterIds">
        delete from ems_cost_mea where cost_center_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
