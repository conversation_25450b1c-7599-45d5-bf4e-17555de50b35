<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsMeasureMapper">

    <resultMap type="EmsMeasure" id="EmsMeasureResult">
        <result property="meaId"    column="mea_id"    />
        <result property="meaName"    column="mea_name"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="meaParentId"    column="mea_parent_id"    />
        <result property="meaSpec"    column="mea_spec"    />
        <result property="meaAccuracy"    column="mea_accuracy"    />
        <result property="veriCycle"    column="veri_cycle"    />
        <result property="veriUnit"    column="veri_unit"    />
        <result property="veriDate"    column="veri_date"    />
        <result property="nextVeriDate"    column="next_veri_date"    />
        <result property="curTranRatio"    column="cur_tran_ratio"    />
        <result property="serialAddress"    column="serial_address"    />
        <result property="gatewayAddrNum"    column="gateway_addr_num"    />
        <result property="gatewayStartAddr"    column="gateway_start_addr"    />
        <result property="energyMed"    column="energy_med"    />
        <result property="costCenterId"    column="cost_center_id"    />
        <result property="commMode"    column="comm_mode"    />
        <result property="manuFact"    column="manu_fact"    />
        <result property="status"    column="status"    />
        <result property="certId"    column="cert_id"    />
        <result property="veriRes"    column="veri_res"    />
        <result property="installAddr"    column="install_addr"    />
        <result property="voltTranRatio"    column="volt_tran_ratio"    />
        <result property="serialLoopNum"    column="serial_loop_num"    />
        <result property="gatewayPort"    column="gateway_port"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEmsMeasureVo">
        select mea_id, mea_name,mea_code, ancestors, mea_parent_id, mea_spec, mea_accuracy, veri_cycle, veri_unit, veri_date, next_veri_date, cur_tran_ratio, serial_address, gateway_addr_num, gateway_start_addr, energy_med, cost_center_id, comm_mode, manu_fact, status, cert_id, veri_res, install_addr, volt_tran_ratio, serial_loop_num, gateway_port, del_flag from ems_measure
    </sql>

    <select id="selectEmsMeasureList" parameterType="EmsMeasure" resultMap="EmsMeasureResult">
        <include refid="selectEmsMeasureVo"/>
        <where>
            del_flag=1
            <if test="meaName != null  and meaName != ''"> and mea_name like concat('%', #{meaName}, '%')</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
            <if test="meaParentId != null "> and mea_parent_id = #{meaParentId}</if>
            <if test="meaSpec != null  and meaSpec != ''"> and mea_spec = #{meaSpec}</if>
            <if test="meaAccuracy != null  and meaAccuracy != ''"> and mea_accuracy = #{meaAccuracy}</if>
            <if test="veriCycle != null  and veriCycle != ''"> and veri_cycle = #{veriCycle}</if>
            <if test="veriUnit != null  and veriUnit != ''"> and veri_unit = #{veriUnit}</if>
            <if test="veriDate != null "> and veri_date = #{veriDate}</if>
            <if test="nextVeriDate != null "> and next_veri_date = #{nextVeriDate}</if>
            <if test="curTranRatio != null  and curTranRatio != ''"> and cur_tran_ratio = #{curTranRatio}</if>
            <if test="serialAddress != null  and serialAddress != ''"> and serial_address = #{serialAddress}</if>
            <if test="gatewayAddrNum != null  and gatewayAddrNum != ''"> and gateway_addr_num = #{gatewayAddrNum}</if>
            <if test="gatewayStartAddr != null  and gatewayStartAddr != ''"> and gateway_start_addr = #{gatewayStartAddr}</if>
            <if test="energyMed != null "> and energy_med = #{energyMed}</if>
            <if test="costCenterId != null "> and cost_center_id = #{costCenterId}</if>
            <if test="commMode != null  and commMode != ''"> and comm_mode = #{commMode}</if>
            <if test="manuFact != null  and manuFact != ''"> and manu_fact = #{manuFact}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="certId != null "> and cert_id = #{certId}</if>
            <if test="veriRes != null  and veriRes != ''"> and veri_res = #{veriRes}</if>
            <if test="installAddr != null  and installAddr != ''"> and install_addr = #{installAddr}</if>
            <if test="voltTranRatio != null  and voltTranRatio != ''"> and volt_tran_ratio = #{voltTranRatio}</if>
            <if test="serialLoopNum != null  and serialLoopNum != ''"> and serial_loop_num = #{serialLoopNum}</if>
            <if test="gatewayPort != null  and gatewayPort != ''"> and gateway_port = #{gatewayPort}</if>
        </where>
    </select>

    <select id="selectEmsMeasureByMeaId" parameterType="Long" resultMap="EmsMeasureResult">
        <include refid="selectEmsMeasureVo"/>
        where mea_id = #{meaId}
    </select>
    <select id="selectEmsMeasureListDTO" resultType="com.ems.energy.domain.EmsMeasureListDTO">
        select mea_id,mea_code,
               center_id cost_center_id,
               center_name cost_center_name,cust_name,
               energy_med,energy_name,
               relation_expression,description
        from ems_measure mea
                 left join ems_energy energy
                           on mea.energy_med=energy_id
                 left join ems_cost_center cost_center
                           on mea.cost_center_id=cost_center.center_id
                 left join ems_mea_center_relat center_relat
                           on cost_center.center_id=center_relat.cost_center_id
        <where>
            <if test="listDTO.costCenterId != null">
                and  cost_center.cost_center_id=#{listDTO.costCenterId}
            </if>
            <if test="listDTO.energyMed != null">
                and  energy.energy_id=#{listDTO.energyMed}
            </if>
        </where>
    </select>
    <select id="selectEmsMeasureListPage" parameterType="com.ems.energy.domain.vo.EmsMeasureVo" resultType="com.ems.energy.domain.vo.EmsMeasureVo">
        select mea.*,cc.center_name cost_center_name,energy.energy_name from ems_measure mea
        left join ems_cost_center cc
        on mea.cost_center_id=cc.center_id
        left join ems_energy energy
        on mea.energy_med=energy.energy_id<where>
        mea.del_flag=1
        <if test="costCenterId != null "> and mea.cost_center_id = #{costCenterId}</if>
        </where>
    </select>
    <select id="checkDeptNameUnique" resultType="com.ems.energy.domain.EmsMeasure">
        <include refid="selectEmsMeasureVo"/>
        where mea_name=#{meaName} and mea_parent_id = #{meaParentId} limit 1
    </select>
    <select id="selectByMeaid" resultType="java.lang.Integer">
        SELECT
            *
        FROM
            ems_measure
        WHERE
            mea_parent_id = #{meaId}
            LIMIT 1
    </select>
    <select id="countEmsMeaCenterRelatByIds" resultType="java.lang.Integer">
        select count(*) from ems_measure where mea_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectEmsMeasureBycostCent" resultType="com.ems.energy.domain.EmsMeasure">
        SELECT
            measure.mea_id,
            measure.mea_name
        FROM
            ems_cost_center cost
                LEFT JOIN ems_measure measure ON cost.center_id = measure.cost_center_id
        WHERE
            cost.center_id = #{costCenterId}
    </select>

    <insert id="insertEmsMeasure" parameterType="EmsMeasure" useGeneratedKeys="true" keyColumn="meaId">
        insert into ems_measure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="meaId != null">mea_id,</if>
            <if test="meaName != null">mea_name,</if>
            <if test="meaCode != null">mea_code,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="meaParentId != null">mea_parent_id,</if>
            <if test="meaSpec != null">mea_spec,</if>
            <if test="meaAccuracy != null">mea_accuracy,</if>
            <if test="veriCycle != null">veri_cycle,</if>
            <if test="veriUnit != null">veri_unit,</if>
            <if test="veriDate != null">veri_date,</if>
            <if test="nextVeriDate != null">next_veri_date,</if>
            <if test="curTranRatio != null">cur_tran_ratio,</if>
            <if test="serialAddress != null">serial_address,</if>
            <if test="gatewayAddrNum != null">gateway_addr_num,</if>
            <if test="gatewayStartAddr != null">gateway_start_addr,</if>
            <if test="energyMed != null">energy_med,</if>
            <if test="costCenterId != null">cost_center_id,</if>
            <if test="commMode != null">comm_mode,</if>
            <if test="manuFact != null">manu_fact,</if>
            <if test="status != null">status,</if>
            <if test="certId != null">cert_id,</if>
            <if test="veriRes != null">veri_res,</if>
            <if test="installAddr != null">install_addr,</if>
            <if test="voltTranRatio != null">volt_tran_ratio,</if>
            <if test="serialLoopNum != null">serial_loop_num,</if>
            <if test="gatewayPort != null">gateway_port,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="meaId != null">#{meaId},</if>
            <if test="meaName != null">#{meaName},</if>
            <if test="meaCode != null">#{meaCode},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="meaParentId != null">#{meaParentId},</if>
            <if test="meaSpec != null">#{meaSpec},</if>
            <if test="meaAccuracy != null">#{meaAccuracy},</if>
            <if test="veriCycle != null">#{veriCycle},</if>
            <if test="veriUnit != null">#{veriUnit},</if>
            <if test="veriDate != null">#{veriDate},</if>
            <if test="nextVeriDate != null">#{nextVeriDate},</if>
            <if test="curTranRatio != null">#{curTranRatio},</if>
            <if test="serialAddress != null">#{serialAddress},</if>
            <if test="gatewayAddrNum != null">#{gatewayAddrNum},</if>
            <if test="gatewayStartAddr != null">#{gatewayStartAddr},</if>
            <if test="energyMed != null">#{energyMed},</if>
            <if test="costCenterId != null">#{costCenterId},</if>
            <if test="commMode != null">#{commMode},</if>
            <if test="manuFact != null">#{manuFact},</if>
            <if test="status != null">#{status},</if>
            <if test="certId != null">#{certId},</if>
            <if test="veriRes != null">#{veriRes},</if>
            <if test="installAddr != null">#{installAddr},</if>
            <if test="voltTranRatio != null">#{voltTranRatio},</if>
            <if test="serialLoopNum != null">#{serialLoopNum},</if>
            <if test="gatewayPort != null">#{gatewayPort},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>
    <update id="updateEmsMeasure" parameterType="EmsMeasure">
        update ems_measure
        <trim prefix="SET" suffixOverrides=",">
            <if test="meaName != null">mea_name = #{meaName},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="meaParentId != null">mea_parent_id = #{meaParentId},</if>
            <if test="meaSpec != null">mea_spec = #{meaSpec},</if>
            <if test="meaAccuracy != null">mea_accuracy = #{meaAccuracy},</if>
            <if test="veriCycle != null">veri_cycle = #{veriCycle},</if>
            <if test="veriUnit != null">veri_unit = #{veriUnit},</if>
            <if test="veriDate != null">veri_date = #{veriDate},</if>
            <if test="nextVeriDate != null">next_veri_date = #{nextVeriDate},</if>
            <if test="curTranRatio != null">cur_tran_ratio = #{curTranRatio},</if>
            <if test="serialAddress != null">serial_address = #{serialAddress},</if>
            <if test="gatewayAddrNum != null">gateway_addr_num = #{gatewayAddrNum},</if>
            <if test="gatewayStartAddr != null">gateway_start_addr = #{gatewayStartAddr},</if>
            <if test="energyMed != null">energy_med = #{energyMed},</if>
            <if test="costCenterId != null">cost_center_id = #{costCenterId},</if>
            <if test="commMode != null">comm_mode = #{commMode},</if>
            <if test="manuFact != null">manu_fact = #{manuFact},</if>
            <if test="status != null">status = #{status},</if>
            <if test="certId != null">cert_id = #{certId},</if>
            <if test="veriRes != null">veri_res = #{veriRes},</if>
            <if test="installAddr != null">install_addr = #{installAddr},</if>
            <if test="voltTranRatio != null">volt_tran_ratio = #{voltTranRatio},</if>
            <if test="serialLoopNum != null">serial_loop_num = #{serialLoopNum},</if>
            <if test="gatewayPort != null">gateway_port = #{gatewayPort},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where mea_id = #{meaId}
    </update>

    <update id="deleteEmsMeasureByMeaId" parameterType="Long">
        update  ems_measure set del_flag=0 where mea_id = #{meaId}
    </update>

    <update id="deleteEmsMeasureByMeaIds" parameterType="String">
        update ems_measure
        set del_flag=0
        where mea_id in
        <foreach item="meaId" collection="array" open="(" separator="," close=")">
            #{meaId}
        </foreach>
    </update>
</mapper>
