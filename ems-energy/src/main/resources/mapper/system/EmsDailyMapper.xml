<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsDailyMapper">

    <resultMap type="EmsDaily" id="EmsDailyResult">
        <result property="dailyId"    column="daily_id"    />
        <result property="createTime" javaType="java.util.Date" jdbcType="DATE"    column="create_time"    />
        <result property="prodCode"    column="prod_code"    />
        <result property="prodName"    column="prod_name"    />
        <result property="outputPiece"    column="output_piece"    />
        <result property="outputModel"    column="output_model"    />
        <result property="allWeight"    column="all_weight"    />
        <result property="prodTime"    column="prod_time"    />
        <result property="powerCons"    column="power_cons"    />
        <result property="minuteCons"    column="minute_cons"    />
        <result property="deviceId"    column="device_id"    />
    </resultMap>

    <sql id="selectEmsDailyVo">
        select daily_id, create_time, prod_code, prod_name, output_piece, output_model, all_weight, prod_time, power_cons, minute_cons, device_id from ems_daily
    </sql>

    <select id="selectEmsDailyList" parameterType="EmsDaily" resultMap="EmsDailyResult">
        <include refid="selectEmsDailyVo"/>
        <where>
            <if test="prodCode != null  and prodCode != ''"> and prod_code = #{prodCode}</if>
            <if test="prodName != null  and prodName != ''"> and prod_name like concat('%', #{prodName}, '%')</if>
            <if test="outputPiece != null "> and output_piece = #{outputPiece}</if>
            <if test="outputModel != null "> and output_model = #{outputModel}</if>
            <if test="allWeight != null "> and all_weight = #{allWeight}</if>
            <if test="prodTime != null "> and prod_time = #{prodTime}</if>
            <if test="powerCons != null "> and power_cons = #{powerCons}</if>
            <if test="minuteCons != null "> and minute_cons = #{minuteCons}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
        </where>
    </select>

    <select id="selectEmsDailyByDailyId" parameterType="Long" resultMap="EmsDailyResult">
        <include refid="selectEmsDailyVo"/>
        where daily_id = #{dailyId}
    </select>
    <select id="selectlistOne" resultType="com.ems.energy.domain.EmsDailyDTO">
        SELECT
            daily.*,
            relat.relat_code
        FROM
            ems_daily daily
                LEFT JOIN ems_mea_center_relat relat ON daily.device_id = relat.relat_id
        <where>
            <if test="dto.startTime!=null">
                and daily.create_time>=#{dto.createTime}
            </if>
            <if test="dto.endTime!=null">
                and daily.create_time&lt;=#{dto.createTime}
            </if>
            <if test="dto.deviceId!=null">
                and daily.device_id=#{dto.deviceId}
            </if>
        </where>
    </select>
    <select id="selectDaily" resultType="com.ems.energy.domain.EmsDaily">
        SELECT
        daily.*,
        relat.relat_code
        FROM
        ems_daily daily
        LEFT JOIN ems_mea_center_relat relat ON daily.device_id = relat.relat_id

    </select>

    <select id="selectTimePeriod" resultType="com.ems.energy.domain.EmsDaily">
        SELECT
            minute_cons,
            device_id
        FROM
            ems_daily
        WHERE
            create_time >=str_to_date(#{begin},'%Y-%m-%d %H:%i:%s')
          AND create_time &lt;= str_to_date(#{end},'%Y-%m-%d %H:%i:%s')
    </select>

    <select id="selectTimePeriodgrain" resultType="com.ems.energy.domain.EmsDaily">
        SELECT
        minute_cons,
        device_id
        FROM
        ems_daily
        WHERE
        create_time >=str_to_date(#{low},'%Y-%m-%d %H:%i:%s')
        AND create_time &lt;= str_to_date(#{ebbone},'%Y-%m-%d %H:%i:%s')
        or
        create_time >=str_to_date(#{lowtwo},'%Y-%m-%d %H:%i:%s')
        AND create_time &lt;= str_to_date(#{ebb},'%Y-%m-%d %H:%i:%s')
    </select>

    <insert id="insertEmsDaily" parameterType="EmsDaily" useGeneratedKeys="true" keyProperty="dailyId">
        insert into ems_daily
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="prodCode != null">prod_code,</if>
            <if test="prodName != null">prod_name,</if>
            <if test="outputPiece != null">output_piece,</if>
            <if test="outputModel != null">output_model,</if>
            <if test="allWeight != null">all_weight,</if>
            <if test="prodTime != null">prod_time,</if>
            <if test="powerCons != null">power_cons,</if>
            <if test="minuteCons != null">minute_cons,</if>
            <if test="deviceId != null">device_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime,jdbcType=DATE,javaType=java.util.Date},</if>
            <if test="prodCode != null">#{prodCode},</if>
            <if test="prodName != null">#{prodName},</if>
            <if test="outputPiece != null">#{outputPiece},</if>
            <if test="outputModel != null">#{outputModel},</if>
            <if test="allWeight != null">#{allWeight},</if>
            <if test="prodTime != null">#{prodTime},</if>
            <if test="powerCons != null">#{powerCons},</if>
            <if test="minuteCons != null">#{minuteCons},</if>
            <if test="deviceId != null">#{deviceId},</if>
         </trim>
    </insert>
    <insert id="insertEmsJson" parameterType="com.ems.energy.domain.EmsDaily" useGeneratedKeys="false">
        insert into ems_daily
        (create_time,prod_name,output_piece,output_model,all_weight,prod_time,power_cons,minute_cons)
        values
        <foreach collection="list" item="model" separator="," index="index">
              ( #{model.createTime},
               #{model.prodName},
                #{model.outputPiece},
               #{model.outputModel},
                #{model.allWeight},
                #{model.prodTime},
                #{model.powerCons},
                #{model.minuteCons}
                )
        </foreach>
    </insert>
    <insert id="insertEmsrbb" parameterType="com.ems.energy.domain.EmsDailyData" useGeneratedKeys="false">
       insert into ems_daily_data
        (create_time,prod_name,output_piece,output_model,all_weight,prod_time,power_cons,minute_cons)
        values
        <foreach collection="ems" item="model" separator="," index="index">
            ( #{model.createTime},
            #{model.prodName},
            #{model.outputPiece},
            #{model.outputModel},
            #{model.allWeight},
            #{model.prodTime},
            #{model.powerCons},
            #{model.minuteCons}
            )
        </foreach>
    </insert>

    <update id="updateEmsDaily" parameterType="EmsDaily">
        update ems_daily
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="prodCode != null">prod_code = #{prodCode},</if>
            <if test="prodName != null">prod_name = #{prodName},</if>
            <if test="outputPiece != null">output_piece = #{outputPiece},</if>
            <if test="outputModel != null">output_model = #{outputModel},</if>
            <if test="allWeight != null">all_weight = #{allWeight},</if>
            <if test="prodTime != null">prod_time = #{prodTime},</if>
            <if test="powerCons != null">power_cons = #{powerCons},</if>
            <if test="minuteCons != null">minute_cons = #{minuteCons},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
        </trim>
        where daily_id = #{dailyId}
    </update>

    <delete id="deleteEmsDailyByDailyId" parameterType="Long">
        delete from ems_daily where daily_id = #{dailyId}
    </delete>

    <delete id="deleteEmsDailyByDailyIds" parameterType="String">
        delete from ems_daily where daily_id in
        <foreach item="dailyId" collection="array" open="(" separator="," close=")">
            #{dailyId}
        </foreach>
    </delete>
    <delete id="deleteDto">
        DELETE FROM ems_daily_data
    </delete>
</mapper>
