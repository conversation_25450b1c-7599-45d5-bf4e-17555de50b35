<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.ReportMapper">


    <select id="queryMonthDayEnergyBySampler" resultType="java.util.Map">
        select sum(value) as value, date_format(report_date, '%Y-%m-%d') as time
        from ems_sampler_day_report
        where sampler = #{sampler} and date_format(report_date, '%Y-%m') = #{month}
        and category in
        <foreach item="category" collection="categories" open="(" separator="," close=")">
            #{category}
        </foreach>
        group by time
    </select>

    <select id="querySamplerListByPartIds" resultType="java.util.Map">
        select pe.part_id, concat(pe.part_pattern, '|',mt.sampler) as part_pattern from  ems_part_equipment pe
            left join ems_equipment e on pe.equipment_id = e.equipment_id
            left join ems_measuring_tool mt on JSON_EXTRACT(e.measuring_tool_config, '$.PartCount') = mt.measuring_tool_id
        where pe.part_id in
        <foreach item="partId" collection="array" open="(" separator="," close=")">
            #{partId}
        </foreach>
    </select>
</mapper>