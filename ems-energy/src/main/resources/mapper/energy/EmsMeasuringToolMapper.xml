<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsMeasuringToolMapper">
    
    <resultMap type="EmsMeasuringTool" id="EmsMeasuringToolResult">
        <result property="measuringToolId"    column="measuring_tool_id"    />
        <result property="measuringToolCode"    column="measuring_tool_code"    />
        <result property="measuringToolName"    column="measuring_tool_name"    />
        <result property="tag"    column="tag"    />
        <result property="type"    column="type"    />
        <result property="sampler"    column="sampler"    />
        <result property="samplerModel"    column="sampler_model"    />
        <result property="costCenterId"    column="cost_center_id"    />
        <result property="checkDate"    column="check_date"    />
        <result property="checkOrg"    column="check_org"    />
        <result property="certCode"    column="cert_code"    />
        <result property="position"    column="position"    />
        <result property="nextCheckDate"    column="next_check_date"    />
        <result property="parentId"    column="parent_id"    />
        <result property="virtualExpression"    column="virtual_expression"    />
        <result property="virtualJson"    column="virtual_json"    />
        <result property="virtualVariable"    column="virtual_variable"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEmsMeasuringToolVo">
        select measuring_tool_id, measuring_tool_code, measuring_tool_name, tag, type, sampler, sampler_model, cost_center_id, check_date, check_org, cert_code, position, next_check_date, parent_id, virtual_expression, virtual_json, virtual_variable, remark, create_by, create_time, update_by, update_time from ems_measuring_tool
    </sql>

    <select id="selectEmsMeasuringToolList" parameterType="EmsMeasuringTool" resultMap="EmsMeasuringToolResult">
        <include refid="selectEmsMeasuringToolVo"/>
        <where>  
            <if test="measuringToolCode != null  and measuringToolCode != ''"> and measuring_tool_code like concat('%', #{measuringToolCode}, '%')</if>
            <if test="measuringToolName != null  and measuringToolName != ''"> and measuring_tool_name like concat('%', #{measuringToolName}, '%')</if>
            <if test="tag != null  and tag != ''"> and tag like concat('%', #{tag}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="virtualVariable != null  and virtualVariable != ''"> and virtual_variable = #{virtualVariable}</if>
        </where>
    </select>
    
    <select id="selectEmsMeasuringToolByMeasuringToolId" parameterType="Long" resultMap="EmsMeasuringToolResult">
        <include refid="selectEmsMeasuringToolVo"/>
        where measuring_tool_id = #{measuringToolId}
    </select>

    <select id="selectRealEmsMeasuringToolList" parameterType="EmsMeasuringTool" resultMap="EmsMeasuringToolResult">
        <include refid="selectEmsMeasuringToolVo"/>
        <where>
            <if test="measuringToolCode != null  and measuringToolCode != ''"> and measuring_tool_code like concat('%', #{measuringToolCode}, '%')</if>
            <if test="measuringToolName != null  and measuringToolName != ''"> and measuring_tool_name like concat('%', #{measuringToolName}, '%')</if>
            <if test="tag != null  and tag != ''"> and tag like concat('%', #{tag}, '%')</if>
            <if test="virtualVariable != null  and virtualVariable != ''"> and virtual_variable = #{virtualVariable}</if>
             and type <![CDATA[ <> ]]> 'virtual'
        </where>
    </select>
    <select id="counEmsMeasuringToolByIds" resultType="java.lang.Integer">
        select count(*) from ems_measuring_tool where measuring_tool_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectEmsMeasuringToolByTag" resultType="com.ems.energy.domain.EmsMeasuringTool">
        <include refid="selectEmsMeasuringToolVo"/>
        where tag regexp concat('[^,]*',#{tag},',|,',#{tag},'[^,]*|^',#{tag},'$')
    </select>
    <select id="selectEmsMeasuringToolListByIds" resultType="com.ems.energy.domain.EmsMeasuringTool">
        <include refid="selectEmsMeasuringToolVo"/>
        where measuring_tool_id in
        <foreach item="measuringToolId" collection="array" open="(" separator="," close=")">
            #{measuringToolId}
        </foreach>
    </select>

    <insert id="insertEmsMeasuringTool" parameterType="EmsMeasuringTool" useGeneratedKeys="true" keyProperty="measuringToolId">
        insert into ems_measuring_tool
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="measuringToolCode != null">measuring_tool_code,</if>
            <if test="measuringToolName != null">measuring_tool_name,</if>
            <if test="tag != null">tag,</if>
            <if test="type != null">type,</if>
            <if test="sampler != null">sampler,</if>
            <if test="samplerModel != null">sampler_model,</if>
            <if test="costCenterId != null">cost_center_id,</if>
            <if test="checkDate != null">check_date,</if>
            <if test="checkOrg != null">check_org,</if>
            <if test="certCode != null">cert_code,</if>
            <if test="position != null">position,</if>
            <if test="nextCheckDate != null">next_check_date,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="virtualExpression != null">virtual_expression,</if>
            <if test="virtualJson != null">virtual_json,</if>
            <if test="virtualVariable != null">virtual_variable,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="measuringToolCode != null">#{measuringToolCode},</if>
            <if test="measuringToolName != null">#{measuringToolName},</if>
            <if test="tag != null">#{tag},</if>
            <if test="type != null">#{type},</if>
            <if test="sampler != null">#{sampler},</if>
            <if test="samplerModel != null">#{samplerModel},</if>
            <if test="costCenterId != null">#{costCenterId},</if>
            <if test="checkDate != null">#{checkDate},</if>
            <if test="checkOrg != null">#{checkOrg},</if>
            <if test="certCode != null">#{certCode},</if>
            <if test="position != null">#{position},</if>
            <if test="nextCheckDate != null">#{nextCheckDate},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="virtualExpression != null">#{virtualExpression},</if>
            <if test="virtualJson != null">#{virtualJson},</if>
            <if test="virtualVariable != null">#{virtualVariable},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEmsMeasuringTool" parameterType="EmsMeasuringTool">
        update ems_measuring_tool
        <trim prefix="SET" suffixOverrides=",">
            <if test="measuringToolCode != null">measuring_tool_code = #{measuringToolCode},</if>
            <if test="measuringToolName != null">measuring_tool_name = #{measuringToolName},</if>
            <if test="tag != null">tag = #{tag},</if>
            <if test="type != null">type = #{type},</if>
            <if test="sampler != null">sampler = #{sampler},</if>
            <if test="samplerModel != null">sampler_model = #{samplerModel},</if>
            <if test="costCenterId != null">cost_center_id = #{costCenterId},</if>
            <if test="checkDate != null">check_date = #{checkDate},</if>
            <if test="checkOrg != null">check_org = #{checkOrg},</if>
            <if test="certCode != null">cert_code = #{certCode},</if>
            <if test="position != null">position = #{position},</if>
            <if test="nextCheckDate != null">next_check_date = #{nextCheckDate},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="virtualExpression != null">virtual_expression = #{virtualExpression},</if>
            <if test="virtualJson != null">virtual_json = #{virtualJson},</if>
            <if test="virtualVariable != null">virtual_variable = #{virtualVariable},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where measuring_tool_id = #{measuringToolId}
    </update>

    <delete id="deleteEmsMeasuringToolByMeasuringToolId" parameterType="Long">
        delete from ems_measuring_tool where measuring_tool_id = #{measuringToolId}
    </delete>

    <delete id="deleteEmsMeasuringToolByMeasuringToolIds" parameterType="String">
        delete from ems_measuring_tool where measuring_tool_id in 
        <foreach item="measuringToolId" collection="array" open="(" separator="," close=")">
            #{measuringToolId}
        </foreach>
    </delete>
</mapper>