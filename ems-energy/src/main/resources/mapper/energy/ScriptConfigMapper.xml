<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.ScriptConfigMapper">
    
    <resultMap type="ScriptConfig" id="ScriptConfigResult">
        <result property="id"           column="id"           />
        <result property="code"         column="code"         />
        <result property="script"       column="script"       />
        <result property="description"  column="description"  />
        <result property="dbType"       column="db_type"      />
        <result property="createdTime"  column="created_time" />
        <result property="updatedTime"  column="updated_time" />
        <result property="delFlag"      column="del_flag"     />
    </resultMap>

    <sql id="selectScriptConfigVo">
        select id, code, script, description, db_type, created_time, updated_time, del_flag from script_config
    </sql>

    <select id="selectScriptConfigByCode" parameterType="String" resultMap="ScriptConfigResult">
        <include refid="selectScriptConfigVo"/>
        where code = #{code} and del_flag = '1'
    </select>

</mapper>
