<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsSamplerDayReportMapper">
    
    <resultMap type="EmsSamplerDayReport" id="EmsSamplerDayReportResult">
        <result property="reportId"    column="report_id"    />
        <result property="value"    column="value"    />
        <result property="category"    column="category"    />
        <result property="sampler"    column="sampler"    />
        <result property="reportDate"    column="report_date"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEmsSamplerDayReportVo">
        select report_id, value, category, sampler, report_date, remark, create_by, create_time, update_by, update_time from ems_sampler_day_report
    </sql>

    <select id="selectEmsSamplerDayReportList" parameterType="EmsSamplerDayReport" resultMap="EmsSamplerDayReportResult">
        <include refid="selectEmsSamplerDayReportVo"/>
        <where>  
            <if test="value != null "> and value = #{value}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="sampler != null  and sampler != ''"> and sampler = #{sampler}</if>
            <if test="params.beginReportDate != null and params.beginReportDate != '' and params.endReportDate != null and params.endReportDate != ''"> and report_date between #{params.beginReportDate} and #{params.endReportDate}</if>
        </where>
    </select>
    
    <select id="selectEmsSamplerDayReportByReportId" parameterType="Long" resultMap="EmsSamplerDayReportResult">
        <include refid="selectEmsSamplerDayReportVo"/>
        where report_id = #{reportId}
    </select>
        
    <insert id="insertEmsSamplerDayReport" parameterType="EmsSamplerDayReport" useGeneratedKeys="true" keyProperty="reportId">
        insert into ems_sampler_day_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="value != null">value,</if>
            <if test="category != null">category,</if>
            <if test="sampler != null">sampler,</if>
            <if test="reportDate != null">report_date,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="value != null">#{value},</if>
            <if test="category != null">#{category},</if>
            <if test="sampler != null">#{sampler},</if>
            <if test="reportDate != null">#{reportDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
        on duplicate key update
            value = value + #{value}
    </insert>

    <update id="updateEmsSamplerDayReport" parameterType="EmsSamplerDayReport">
        update ems_sampler_day_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="value != null">value = #{value},</if>
            <if test="category != null">category = #{category},</if>
            <if test="sampler != null">sampler = #{sampler},</if>
            <if test="reportDate != null">report_date = #{reportDate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where report_id = #{reportId}
    </update>

    <delete id="deleteEmsSamplerDayReportByReportId" parameterType="Long">
        delete from ems_sampler_day_report where report_id = #{reportId}
    </delete>

    <delete id="deleteEmsSamplerDayReportByReportIds" parameterType="String">
        delete from ems_sampler_day_report where report_id in 
        <foreach item="reportId" collection="array" open="(" separator="," close=")">
            #{reportId}
        </foreach>
    </delete>
</mapper>