<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsPriceConfigMapper">
    
    <resultMap type="EmsPriceConfig" id="EmsPriceConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="validFrom"    column="valid_from"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="EmsPriceConfigEmsPriceEmsPricePeriodResult" type="EmsPriceConfig" extends="EmsPriceConfigResult">
        <collection property="emsPriceList" notNullColumn="sub_price_id" javaType="java.util.List" resultMap="EmsPriceResult" />
        <collection property="emsPricePeriodList" notNullColumn="sub1_period_id" javaType="java.util.List" resultMap="EmsPricePeriodResult" />
    </resultMap>

    <resultMap id="EmsPriceConfigEmsPriceResult" type="EmsPriceConfig" extends="EmsPriceConfigResult">
        <collection property="emsPriceList" select="selectEmsPriceByConfigId" column="Config_ID"/>
    </resultMap>

    <resultMap type="EmsPrice" id="EmsPriceResult">
        <result property="priceId"    column="sub_price_id"    />
        <result property="price"    column="sub_price"    />
        <result property="category"    column="sub_category"    />
        <result property="configId"    column="sub_config_id"    />
        <result property="createBy"    column="sub_create_by"    />
        <result property="createTime"    column="sub_create_time"    />
        <result property="updateBy"    column="sub_update_by"    />
        <result property="updateTime"    column="sub_update_time"    />
        <result property="remark"    column="sub_remark"    />
    </resultMap>

    <resultMap type="EmsPricePeriod" id="EmsPricePeriodResult">
        <result property="periodId"    column="sub1_period_id"    />
        <result property="start"    column="sub1_start"    />
        <result property="end"    column="sub1_end"    />
        <result property="category"    column="sub1_category"    />
        <result property="type"    column="sub1_type"    />
        <result property="configId"    column="sub1_config_id"    />
        <result property="remark"    column="sub1_remark"    />
        <result property="createBy"    column="sub1_create_by"    />
        <result property="createTime"    column="sub1_create_time"    />
        <result property="updateBy"    column="sub1_update_by"    />
        <result property="updateTime"    column="sub1_update_time"    />
    </resultMap>

    <sql id="selectEmsPriceConfigVo">
        select config_id, valid_from, remark, create_by, create_time, update_by, update_time from ems_price_config
    </sql>

    <select id="selectEmsPricePeriodByConfigId" parameterType="Long" resultMap="EmsPricePeriodResult">
        select c.period_id as sub1_period_id, c.start as sub1_start, c.end as sub1_end, c.category as sub1_category, c.type as sub1_type, c.config_id as sub1_config_id, c.remark as sub1_remark, c.create_by as sub1_create_by, c.create_time as sub1_create_time, c.update_by as sub1_update_by, c.update_time as sub1_update_time
        from ems_price_period c where c.config_id = #{configId}
    </select>

    <select id="selectEmsPriceByConfigId" parameterType="Long" resultMap="EmsPriceResult">
        select b.price_id as sub_price_id, b.price as sub_price, b.category as sub_category, b.config_id as sub_config_id, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time, b.remark as sub_remark
        from ems_price b where b.config_id = #{configId}
    </select>

    <select id="selectEmsPriceConfigList" parameterType="EmsPriceConfig" resultMap="EmsPriceConfigEmsPriceResult">
        <include refid="selectEmsPriceConfigVo"/>
        <where>  
            <if test="params.beginValidFrom != null and params.beginValidFrom != '' and params.endValidFrom != null and params.endValidFrom != ''"> and valid_from between #{params.beginValidFrom} and #{params.endValidFrom}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
    </select>
    
    <select id="selectEmsPriceConfigByConfigId" parameterType="Long" resultMap="EmsPriceConfigEmsPriceEmsPricePeriodResult">
        select a.config_id, a.valid_from, a.remark, a.create_by, a.create_time, a.update_by, a.update_time,
                b.price_id as sub_price_id, b.price as sub_price, b.category as sub_category, b.config_id as sub_config_id, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time, b.remark as sub_remark,
               c.period_id as sub1_period_id, c.start as sub1_start, c.end as sub1_end, c.category as sub1_category, c.type as sub1_type, c.config_id as sub1_config_id, c.remark as sub1_remark, c.create_by as sub1_create_by, c.create_time as sub1_create_time, c.update_by as sub1_update_by, c.update_time as sub1_update_time
        from ems_price_config a
        left join ems_price b on b.config_id = a.config_id
        left join ems_price_period c on c.config_id = a.config_id and c.category = b.category
        where a.config_id = #{configId}
    </select>

    <select id="selectValidEmsPriceConfigByTime" parameterType="Date" resultMap="EmsPriceConfigEmsPriceEmsPricePeriodResult">
        select a.config_id, a.valid_from, a.remark, a.create_by, a.create_time, a.update_by, a.update_time,
        b.price_id as sub_price_id, b.price as sub_price, b.category as sub_category, b.config_id as sub_config_id, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time, b.remark as sub_remark,
        c.period_id as sub1_period_id, c.start as sub1_start, c.end as sub1_end, c.category as sub1_category, c.type as sub1_type, c.config_id as sub1_config_id, c.remark as sub1_remark, c.create_by as sub1_create_by, c.create_time as sub1_create_time, c.update_by as sub1_update_by, c.update_time as sub1_update_time
        from  (select config_id, valid_from, remark, create_by, create_time, update_by, update_time from ems_price_config
        where valid_from <![CDATA[<=]]> #{time}
        order by valid_from desc
        limit 1) a
        left join ems_price b on b.config_id = a.config_id
        left join ems_price_period c on c.config_id = a.config_id and c.category = b.category
    </select>
        
    <insert id="insertEmsPriceConfig" parameterType="EmsPriceConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into ems_price_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="validFrom != null">valid_from,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="validFrom != null">#{validFrom},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEmsPriceConfig" parameterType="EmsPriceConfig">
        update ems_price_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="validFrom != null">valid_from = #{validFrom},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deleteEmsPriceConfigByConfigId" parameterType="Long">
        delete from ems_price_config where config_id = #{configId}
    </delete>

    <delete id="deleteEmsPriceConfigByConfigIds" parameterType="String">
        delete from ems_price_config where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>
    
    <delete id="deleteEmsPriceByConfigIds" parameterType="String">
        delete from ems_price where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>

    <delete id="deleteEmsPriceByConfigId" parameterType="Long">
        delete from ems_price where config_id = #{configId}
    </delete>

    <insert id="batchEmsPrice">
        insert into ems_price( price_id, price, category, config_id, create_by, create_time, update_by, update_time, remark) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.priceId}, #{item.price}, #{item.category}, #{item.configId}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.remark})
        </foreach>
    </insert>

    <delete id="deleteEmsPricePeriodByConfigIds" parameterType="String">
        delete from ems_price_period where config_id in
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>

    <delete id="deleteEmsPricePeriodByConfigId" parameterType="Long">
        delete from ems_price_period where config_id = #{configId}
    </delete>

    <insert id="batchEmsPricePeriod">
        insert into ems_price_period( period_id, start, end, category, type, config_id, remark, create_by, create_time, update_by, update_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.periodId}, #{item.start}, #{item.end}, #{item.category}, #{item.type}, #{item.configId}, #{item.remark}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>