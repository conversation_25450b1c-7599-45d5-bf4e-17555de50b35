<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsPartMapper">
    
    <resultMap type="EmsPart" id="EmsPartResult">
        <result property="partId"    column="part_id"    />
        <result property="partNo"    column="part_no"    />
        <result property="partName"    column="part_name"    />
        <result property="pattern"    column="pattern"    />
        <result property="useCount"    column="use_count"    />
        <result property="equivalent"    column="equivalent"    />
        <result property="processCount"    column="process_count"    />
        <result property="parentId"    column="parent_id"    />
        <result property="tag"    column="tag"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEmsPartVo">
        select part_id, part_no, part_name, pattern, use_count, equivalent, process_count, parent_id, tag, remark, create_by, create_time, update_by, update_time from ems_part
    </sql>

    <select id="selectEmsPartList" parameterType="EmsPart" resultMap="EmsPartResult">
        <include refid="selectEmsPartVo"/>
        <where>  
            <if test="partNo != null  and partNo != ''"> and part_no like concat('%', #{partNo}, '%')</if>
            <if test="partName != null  and partName != ''"> and part_name like concat('%', #{partName}, '%')</if>
            <if test="pattern != null  and pattern != ''"> and pattern like concat('%', #{pattern}, '%')</if>
            <if test="parentId != null"> and parent_id = #{parentId}</if>
            <if test="tag != null  and tag != ''"> and tag = #{tag}</if>
        </where>
    </select>
    
    <select id="selectEmsPartByPartId" parameterType="Long" resultMap="EmsPartResult">
        <include refid="selectEmsPartVo"/>
        where part_id = #{partId}
    </select>
    <select id="selectEmsPartByEquipmentIdAndPartPattern" resultType="com.ems.energy.domain.EmsPart">
        <include refid="selectEmsPartVo"/> p
         where p.part_id in (select part_id from ems_part_equipment pe where pe.equipment_id = #{equipmentId} and pe.part_pattern=#{partPattern})
    </select>

    <insert id="insertEmsPart" parameterType="EmsPart" useGeneratedKeys="true" keyProperty="partId">
        insert into ems_part
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="partNo != null">part_no,</if>
            <if test="partName != null">part_name,</if>
            <if test="pattern != null">pattern,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="useCount != null">use_count,</if>
            <if test="equivalent != null">equivalent,</if>
            <if test="processCount != null">process_count,</if>
            <if test="tag != null">tag,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="partNo != null">#{partNo},</if>
            <if test="partName != null">#{partName},</if>
            <if test="pattern != null">#{pattern},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="useCount != null">#{useCount},</if>
            <if test="equivalent != null">#{equivalent},</if>
            <if test="processCount != null">#{processCount},</if>
            <if test="tag != null">#{tag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEmsPart" parameterType="EmsPart">
        update ems_part
        <trim prefix="SET" suffixOverrides=",">
            <if test="partNo != null">part_no = #{partNo},</if>
            <if test="partName != null">part_name = #{partName},</if>
            <if test="pattern != null">pattern = #{pattern},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="useCount != null">use_count = #{useCount},</if>
            <if test="equivalent != null">equivalent = #{equivalent},</if>
            <if test="processCount != null">process_count = #{processCount},</if>
            <if test="tag != null">tag = #{tag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where part_id = #{partId}
    </update>

    <delete id="deleteEmsPartByPartId" parameterType="Long">
        delete from ems_part where part_id = #{partId}
    </delete>

    <delete id="deleteEmsPartByPartIds" parameterType="String">
        delete from ems_part where part_id in 
        <foreach item="partId" collection="array" open="(" separator="," close=")">
            #{partId}
        </foreach>
    </delete>
</mapper>