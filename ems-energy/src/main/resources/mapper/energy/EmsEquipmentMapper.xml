<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsEquipmentMapper">
    
    <resultMap type="EmsEquipment" id="EmsEquipmentResult">
        <result property="equipmentId"    column="equipment_id"    />
        <result property="equipmentCode"    column="equipment_code"    />
        <result property="equipmentName"    column="equipment_name"    />
        <result property="model"    column="model"    />
        <result property="brand"    column="brand"    />
        <result property="maintenanceDate"    column="maintenance_date"    />
        <result property="maintenacePeriod"    column="maintenace_period"    />
        <result property="equipmentType"    column="equipment_type"    />
        <result property="parentId"    column="parent_id"    />
        <result property="costCenterId"    column="cost_center_id"    />
        <result property="tag"    column="tag"    />
        <result property="businessTag"    column="business_tag"    />
        <result property="measuringToolConfig"    column="measuring_tool_config"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEmsEquipmentVo">
        select equipment_id, equipment_code, equipment_name, model, brand, maintenance_date, maintenace_period, equipment_type, parent_id, cost_center_id, tag, business_tag, measuring_tool_config, remark, create_by, create_time, update_by, update_time from ems_equipment
    </sql>

    <select id="selectEmsEquipmentList" parameterType="EmsEquipment" resultMap="EmsEquipmentResult">
        <include refid="selectEmsEquipmentVo"/>
        <where>  
            <if test="equipmentCode != null  and equipmentCode != ''"> and equipment_code like concat('%', #{equipmentCode}, '%')</if>
            <if test="equipmentName != null  and equipmentName != ''"> and equipment_name like concat('%', #{equipmentName}, '%')</if>
            <if test="model != null  and model != ''"> and model like concat('%', #{model}, '%')</if>
            <if test="brand != null  and brand != ''"> and brand like concat('%', #{brand}, '%')</if>
            <if test="params.beginMaintenanceDate != null and params.beginMaintenanceDate != '' and params.endMaintenanceDate != null and params.endMaintenanceDate != ''"> and maintenance_date between #{params.beginMaintenanceDate} and #{params.endMaintenanceDate}</if>
            <if test="maintenacePeriod != null  and maintenacePeriod != ''"> and maintenace_period like concat('%', #{maintenacePeriod}, '%')</if>
            <if test="equipmentType != null  and equipmentType != ''"> and equipment_type = #{equipmentType}</if>
            <if test="tag != null  and tag != ''"> and tag like concat('%', #{tag}, '%')</if>
            <if test="businessTag != null  and businessTag != ''"> and business_tag like concat('%', #{businessTag}, '%')</if>
        </where>
    </select>
    
    <select id="selectEmsEquipmentByEquipmentId" parameterType="Long" resultMap="EmsEquipmentResult">
        <include refid="selectEmsEquipmentVo"/>
        where equipment_id = #{equipmentId}
    </select>
    <select id="selectEquipmentByTag" resultType="com.ems.energy.domain.EmsEquipment">
        <include refid="selectEmsEquipmentVo"/>
        where tag regexp concat('[^,]*',#{tag},',|,',#{tag},'[^,]*|^',#{tag},'$')
    </select>

    <insert id="insertEmsEquipment" parameterType="EmsEquipment" useGeneratedKeys="true" keyProperty="equipmentId">
        insert into ems_equipment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipmentCode != null">equipment_code,</if>
            <if test="equipmentName != null">equipment_name,</if>
            <if test="model != null">model,</if>
            <if test="brand != null">brand,</if>
            <if test="maintenanceDate != null">maintenance_date,</if>
            <if test="maintenacePeriod != null">maintenace_period,</if>
            <if test="equipmentType != null">equipment_type,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="costCenterId != null">cost_center_id,</if>
            <if test="tag != null">tag,</if>
            <if test="businessTag != null">business_tag,</if>
            <if test="measuringToolConfig != null">measuring_tool_config,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="equipmentCode != null">#{equipmentCode},</if>
            <if test="equipmentName != null">#{equipmentName},</if>
            <if test="model != null">#{model},</if>
            <if test="brand != null">#{brand},</if>
            <if test="maintenanceDate != null">#{maintenanceDate},</if>
            <if test="maintenacePeriod != null">#{maintenacePeriod},</if>
            <if test="equipmentType != null">#{equipmentType},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="costCenterId != null">#{costCenterId},</if>
            <if test="tag != null">#{tag},</if>
            <if test="businessTag != null">#{businessTag},</if>
            <if test="measuringToolConfig != null">#{measuringToolConfig},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEmsEquipment" parameterType="EmsEquipment">
        update ems_equipment
        <trim prefix="SET" suffixOverrides=",">
            <if test="equipmentCode != null">equipment_code = #{equipmentCode},</if>
            <if test="equipmentName != null">equipment_name = #{equipmentName},</if>
            <if test="model != null">model = #{model},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="maintenanceDate != null">maintenance_date = #{maintenanceDate},</if>
            <if test="maintenacePeriod != null">maintenace_period = #{maintenacePeriod},</if>
            <if test="equipmentType != null">equipment_type = #{equipmentType},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="costCenterId != null">cost_center_id = #{costCenterId},</if>
            <if test="tag != null">tag = #{tag},</if>
            <if test="businessTag != null">business_tag = #{businessTag},</if>
            <if test="measuringToolConfig != null">measuring_tool_config = #{measuringToolConfig},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where equipment_id = #{equipmentId}
    </update>

    <delete id="deleteEmsEquipmentByEquipmentId" parameterType="Long">
        delete from ems_equipment where equipment_id = #{equipmentId}
    </delete>

    <delete id="deleteEmsEquipmentByEquipmentIds" parameterType="String">
        delete from ems_equipment where equipment_id in 
        <foreach item="equipmentId" collection="array" open="(" separator="," close=")">
            #{equipmentId}
        </foreach>
    </delete>
</mapper>