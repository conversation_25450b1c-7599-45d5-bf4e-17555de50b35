<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.energy.mapper.EmsCarbonFactorMapper">
    
    <resultMap type="EmsCarbonFactor" id="EmsCarbonFactorResult">
        <result property="factorId"    column="factor_id"    />
        <result property="carbonFactor"    column="carbon_factor"    />
        <result property="energyCategory"    column="energy_category"    />
        <result property="unit"    column="unit"    />
        <result property="validFrom"    column="valid_from"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEmsCarbonFactorVo">
        select factor_id, carbon_factor, energy_category, unit, valid_from, remark, create_by, create_time, update_by, update_time from ems_carbon_factor
    </sql>

    <select id="selectEmsCarbonFactorList" parameterType="EmsCarbonFactor" resultMap="EmsCarbonFactorResult">
        <include refid="selectEmsCarbonFactorVo"/>
        <where>  
            <if test="carbonFactor != null "> and carbon_factor = #{carbonFactor}</if>
            <if test="energyCategory != null and energyCategory != ''"> and energy_category = #{energyCategory}</if>
            <if test="params.beginValidFrom != null and params.beginValidFrom != '' and params.endValidFrom != null and params.endValidFrom != ''"> and valid_from between #{params.beginValidFrom} and #{params.endValidFrom}</if>
        </where>
    </select>
    
    <select id="selectEmsCarbonFactorByFactorId" parameterType="Long" resultMap="EmsCarbonFactorResult">
        <include refid="selectEmsCarbonFactorVo"/>
        where factor_id = #{factorId}
    </select>
    <select id="selectValidEmsCarbonFactor" resultType="com.ems.energy.domain.EmsCarbonFactor">
        <include refid="selectEmsCarbonFactorVo"/>
        where energy_category=#{energyCategory} and valid_from <![CDATA[<=]]> #{time}
        order by valid_from desc
        limit 1
    </select>

    <insert id="insertEmsCarbonFactor" parameterType="EmsCarbonFactor" useGeneratedKeys="true" keyProperty="factorId">
        insert into ems_carbon_factor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="carbonFactor != null">carbon_factor,</if>
            <if test="energyCategory != null and energyCategory != ''">energy_category,</if>
            <if test="unit != null and unit != ''">unit,</if>
            <if test="validFrom != null">valid_from,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="carbonFactor != null">#{carbonFactor},</if>
            <if test="energyCategory != null and energyCategory != ''">#{energyCategory},</if>
            <if test="unit != null and unit != ''">#{unit},</if>
            <if test="validFrom != null">#{validFrom},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateEmsCarbonFactor" parameterType="EmsCarbonFactor">
        update ems_carbon_factor
        <trim prefix="SET" suffixOverrides=",">
            <if test="carbonFactor != null">carbon_factor = #{carbonFactor},</if>
            <if test="energyCategory != null and energyCategory != ''">energy_category = #{energyCategory},</if>
            <if test="unit != null and unit != ''">unit = #{unit},</if>
            <if test="validFrom != null">valid_from = #{validFrom},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where factor_id = #{factorId}
    </update>

    <delete id="deleteEmsCarbonFactorByFactorId" parameterType="Long">
        delete from ems_carbon_factor where factor_id = #{factorId}
    </delete>

    <delete id="deleteEmsCarbonFactorByFactorIds" parameterType="String">
        delete from ems_carbon_factor where factor_id in 
        <foreach item="factorId" collection="array" open="(" separator="," close=")">
            #{factorId}
        </foreach>
    </delete>
</mapper>