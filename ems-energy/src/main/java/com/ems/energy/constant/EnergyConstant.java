package com.ems.energy.constant;

/**
 * @className: EnergyConstant
 * @description:
 * @author: RK
 * @date: 2021/12/15 14:07
 **/
public class EnergyConstant {

    /**
     * 空字符串
     */
    public static final String EMPTY_STR = "";

    public static final String EMPTY_BLANK = " ";

    public static final String BASE_ANCESTORS = "0";

    /**
     * 可展示(逻辑删除)
     */
    public static final String NORMAL_DEL_FLAG = "1";
    /**
     * 不展示(逻辑删除)
     */
    public static final String NO_DEL_FLAG = "0";

    /**
     * 算式与常量之间的分隔符
     */
    public static final String BASE_SEPARATOR = ",";
    /**
     * 算式检验引擎
     */
    public static final String JS = "javascript";
    /**
     * 排序字段最上级
     */
    public static final Integer ORDER_TOP = 1;
    /**
     * kafka主题名
     */
    public static final String KAFKA_TOPIC_NAME = "ems_report";
    /**
     * 成本中心编码生成
     */
    public static final String COST_CENTER_CODE = "sys.costCenter.code";
    /**
     * 能源介质编码生成
     */
    public static final String ENERGY_CODE = "sys.energy.code";
    /**
     * 计量器具编号生成
     */
    public static final String MEASURE_CODE = "sys.measure.code";
    /**
     * 编号连接符
     */
    public static final String CODE_BASE_SEPARATOR = "-";

    public static final String ADD_CODE = "+";

    public static final String DOUBLE_QUOT_MARK = "\"";

    public static final String DATETIME_BEGIN="08:30:00";

    public static final String DATETIME_EBB="07:00:00";

    public static final String DATETIME_BEGINONE="18:00:00";

    public static final String DATETIME_NDONE="11:30:00";

    public static final String DATETIME_LOW="23:00:00";

    public static final String DATETIME_ONE="23:59:59";

    public static final String DATETIME_TWO="00:00:00";

    public static final String DECI_MAL ="0.000";

    public static final String HUN_DTED ="100.00";

    public static final String PEAK ="峰";

    public static final String FLAT ="平";

    public static final String GARIN ="谷";

    public static final String DEVI_NAME ="deviceName";

    public static final Integer FLA =2;

    public static final Integer HUNDRED =4;

    public static final Integer ONE =100;


    public static final String REG_NUMBER = "[^0-9]";

    public static final String NUMBER_ROUND_REG = "(\\d+)";
}
