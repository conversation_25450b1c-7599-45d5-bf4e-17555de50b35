package com.ems.energy.constant;

public class EmsConstants {
    public static final String INFLUX_SAMPLER_TAG = "hostname";
    public static final String INFLUX_POSITIVE_ENERGY_DAY = "PositiveEnergy_day";
    public static final String INFLUX_POSITIVE_ENERGY_HALF_HOUR = "PositiveEnergy_30m";

    public static final String INFLUX_POWER = "Power";

    public static final String INFLUX_ELEC_AMOUNT_HALF_HOUR = "Elec_Amount_30m";

    public static final String INFLUX_MD_HALF_HOUR = "MD_30m";
    public static final String SUMMER = "summer";
    public static final String NONSUMER = "nonSummer";
    public static final String ELEC_PRICE_PERIOD_CATEGORY_TYPE = "elec_price_period_category";
    public static final String REPORT_ELEC_AMOUNT = "elec_amount";
    public static final String TOTAL_ELEC_TAG = "工厂总电表";
    public static final String INFLUX_CARBON_HALF_HOUR = "Carbon_30m";

    public static final String SUB_TOTAL_ELEC_TAG = "总电表";
    public static final String TOP_CATEGORY_DICT = "top_category";
    public static final String ENERGY_CATEGORY_ELECTRICITY = "electricity";

    public static final String TOTAL_MD_TAG = "工厂总MD";
    public static final String INFLUX_PROCESS_PART_COUNT_HALF_HOUR = "ProcessPartCount_30m";
    public static final String INFLUX_PART_PATTERN_TAG = "partPattern";
    public static final String INFLUX_PROCESS_PART_ENERGY_HALF_HOUR = "ProcessPartEnergy_30m";
    public static final String INFLUX_PROCESS_TIME_COL = "process_time";
    public static final String PART_COUNT = "PartCount";
    public static final String ENERGY = "Energy";
    public static final String POWER = "Power";
}
