package com.ems.energy.assembler;

import com.ems.common.utils.bean.BeanUtils;
import com.ems.energy.domain.EmsCostCenter;
import com.ems.energy.domain.EmsDailyDataDTO;
import com.ems.energy.domain.vo.EmsCostCenterVo;

/**
 * @className: CostCenterAssembler
 * @description: 对象转换器
 * @author: RK
 * @date: 2022/1/21 13:10
 **/
public class CostCenterAssembler {
    /**
     * 示例方法
     * @return
     */
    public static EmsCostCenterVo toCostCenterVo(EmsCostCenter center){
        EmsCostCenterVo vo = new EmsCostCenterVo();
        //源对象,目标对象
        BeanUtils.copyProperties(center,vo);
        //center.setCenterId(vo.getCenterId());
        return vo;
    }
}
