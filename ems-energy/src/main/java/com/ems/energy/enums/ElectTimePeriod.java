package com.ems.energy.enums;

/**
 * @className: ElectTimePeriod
 * @description: 电费时间段
 * @author: RK
 * @date: 2022/1/10 17:10
 **/
public enum ElectTimePeriod {
    /**
     * 峰电第一阶段
     */
    PEAK_FIRST("峰1","08:30:00","11:30:00"),
    /**
     * 峰电第二阶段
     */
    PEAK_SECOND("峰2","18:00:00","23:00:00"),
    /**
     * 平电第一阶段
     */
    FLAT_HUMP_FIRST("平1","07:00:00","08:30:00"),
    /**
     * 平电第二阶段
     */
    FLAT_HUMP_SECOND("平2","11:30:00","18:00:00"),
    /**
     * 谷电时间段
     */
    LOW_EBB("谷","23:00:00","07:00:00");
    /**
     * 电费时间段名称
     */
    private String periodName;
    /**
     * 开始时间
     */
    private String beginTime;
    /**
     * 结束时间
     */
    private String endTime;

    ElectTimePeriod(String periodName, String beginTime, String endTime) {
        this.periodName = periodName;
        this.beginTime = beginTime;
        this.endTime = endTime;
    }

    public String getPeriodName() {
        return periodName;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public String getEndTime() {
        return endTime;
    }
}
