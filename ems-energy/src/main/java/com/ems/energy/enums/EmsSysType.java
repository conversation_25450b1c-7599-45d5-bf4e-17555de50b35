package com.ems.energy.enums;

/**
 * @className: EmsSysType
 * @description: 能源成本中心系统类别枚举
 * @author: RK
 * @date: 2021/12/20 10:06
 **/
public enum EmsSysType {
    /**
     * 附属系统
     */
    SUB_SYSTEM(1,"附属系统"),
    /**
     * 生产系统
     */
    PRO_SYSTEM(2,"生产系统"),
    /**
     * 辅助系统
     */
    AUX_SYSTEM(3,"辅助系统");

    /**
     * 类型id
     */
    private Integer typeId;
    /**
     * 类型名称
     */
    private String typeName;


    EmsSysType(Integer typeId, String typeName) {
        this.typeId = typeId;
        this.typeName = typeName;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public String getTypeName() {
        return typeName;
    }
}
