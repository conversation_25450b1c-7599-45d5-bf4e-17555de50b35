package com.ems.energy.task;

import com.alibaba.fastjson.JSON;
import com.ems.common.utils.DateUtils;
import com.ems.energy.constant.EmsConstants;
import com.ems.energy.constant.EnergyConstant;
import com.ems.energy.domain.*;
import com.ems.energy.domain.vo.InfluxDBPropertyVo;
import com.ems.energy.service.IEmsCarbonFactorService;
import com.ems.energy.service.IEmsMeasuringToolService;
import com.ems.energy.service.IEmsPriceConfigService;
import com.ems.energy.service.IEmsSamplerDayReportService;
import com.google.common.base.Strings;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component("dataCompute")
public class DataCompute {
    private final Logger LOGGER = LoggerFactory.getLogger(DataCompute.class);

    @Resource
    private IEmsMeasuringToolService emsMeasuringToolService;

    @Resource
    private IEmsPriceConfigService emsPriceConfigService;

    @Resource
    private IEmsSamplerDayReportService samplerDayReportService;

    @Resource
    private IEmsCarbonFactorService carbonFactorService;

    @Resource
    private InfluxDBClient influxDBClient;

    @Resource
    private InfluxDBPropertyVo influxDBPropertyVo;

    public void energyCompute(String start, String end) throws ScriptException {
        Date startDate = DateUtils.parseDate(start);
        Date endDate = DateUtils.parseDate(end);
        startDate = DateUtils.truncate(startDate, Calendar.HOUR);
        for(Date date = startDate; date.before(endDate); date = DateUtils.addMinutes(date, 30)){
            energyCompute(date);
        }
    }

    public void energyCompute() throws ScriptException {
        Calendar calendar = Calendar.getInstance();
        int minutes = -30;
        if(calendar.get(Calendar.MINUTE)>=30){
            minutes = 0;
        }
        calendar = DateUtils.truncate(calendar, Calendar.HOUR);
        calendar.add(Calendar.MINUTE, minutes);
        energyCompute(calendar.getTime());
    }

    public void virtualPowerCompute(String start, String end) throws ScriptException {
        Date startDate = DateUtils.parseDate(start);
        Date endDate = DateUtils.parseDate(end);
        startDate = DateUtils.truncate(startDate, Calendar.HOUR);
        for(Date date = startDate; date.before(endDate); date = DateUtils.addMinutes(date, 30)){
            virtualPowerCompute(date);
        }
    }

    public void virtualPowerCompute() throws ScriptException {
        Calendar calendar = Calendar.getInstance();
        int minutes = -30;
        if(calendar.get(Calendar.MINUTE)>=30){
            minutes = 0;
        }
        calendar = DateUtils.truncate(calendar, Calendar.HOUR);
        calendar.add(Calendar.MINUTE, minutes);
        virtualPowerCompute(calendar.getTime());
    }

    public void energyCompute(Date date) throws ScriptException {
        String start = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(date);
        String end = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(DateUtils.addMinutes(date, 30));
        EmsMeasuringTool search = new EmsMeasuringTool();
        List<EmsMeasuringTool> toolList = emsMeasuringToolService.selectEmsMeasuringToolList(search);
        List<EmsMeasuringTool> virtualList = toolList.stream().filter(measuringTool -> {return measuringTool.getType().equals("virtual");}).collect(Collectors.toList());
        Map<String, BigDecimal> energyMap = getAllSamplerEnergyFromInflux(start, end);
        Map<String, BigDecimal> virtualEnergyMap = new HashMap<>();
        //计算虚拟表电耗
        for (EmsMeasuringTool tool : virtualList){
            BigDecimal energy = getMeasuringEnergy(tool, energyMap, toolList);
            virtualEnergyMap.put(tool.getRealSampler(), energy);
        }
        writeVirtualEnergyMapToInflux(virtualEnergyMap, date);
        //计算所有表的电费
        EmsPrice price = emsPriceConfigService.getPriceByTime(date);
        if (null != price){
            Map<String, BigDecimal> amountMap = getAmountMap(energyMap, price.getPrice());
            writeAmountMapToInflux(amountMap, date);
            updateAllElecDayReport(amountMap, energyMap, price.getCategory(), date);
        }
        Map<String, BigDecimal> carbonMap = getCarbonMap(energyMap, date);
        writeCarbonMapToInflux(carbonMap, date);

    }

    public void virtualPowerCompute(Date date) throws ScriptException {
        String start = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(date);
        Date endDate = DateUtils.addMinutes(date, 30);
        String end = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(endDate);
        List<Long> timeList = getMiniuteList(date, endDate);
        EmsMeasuringTool search = new EmsMeasuringTool();
        List<EmsMeasuringTool> toolList = emsMeasuringToolService.selectEmsMeasuringToolList(search);
        List<EmsMeasuringTool> virtualList = toolList.stream().filter(measuringTool -> {return measuringTool.getType().equals("virtual");}).collect(Collectors.toList());
        Map<String, Map<Long, BigDecimal>> samplerPowerMap = getAllSamplerMinutePowerFromInflux(start, end, timeList);
        for (EmsMeasuringTool tool : virtualList){
            Map<Long, BigDecimal> powerMap = computeSamplerPower(tool, samplerPowerMap, toolList, timeList);
            writeVirtualPowerMapToInflux(powerMap, tool.getRealSampler());
        }
    }

    private List<Long> getMiniuteList(Date start, Date end){
        List<Long> result = Lists.newArrayList();
        Date date = start;
        while(date.before(end)){
            date = DateUtils.addMinutes(date, 1);
            result.add(date.getTime());
        }
        return result;
    }

    private Map<Long, BigDecimal> computeSamplerPower(EmsMeasuringTool measuringTool,Map<String, Map<Long, BigDecimal>> samplerPowerMap, List<EmsMeasuringTool> toolList, List<Long> timeList) throws ScriptException {
        String sampler = measuringTool.getRealSampler();
        if (samplerPowerMap.containsKey(sampler)){
            return samplerPowerMap.get(sampler);
        }
        if (measuringTool.isVirtual()){
            List<RecordPO> recordList = JSON.parseArray(measuringTool.getVirtualJson(), RecordPO.class);
            Map<Long, StringBuilder> calcMap = new HashMap<>();
            for (Long time: timeList){
                calcMap.put(time, new StringBuilder());
            }

            for (RecordPO recordPO : recordList){
                for(EmsMeasuringTool tool : toolList){
                    if (Objects.equals(tool.getMeasuringToolId(), recordPO.getData())){
                        Map<Long, BigDecimal> powerMap = computeSamplerPower(tool, samplerPowerMap, toolList, timeList);
                        for (Long time : timeList){
                            calcMap.get(time).append(powerMap.get(time).toString()).append(Strings.nullToEmpty(recordPO.getSymbol()));
                        }
                        break;
                    }
                }
            }
            Map<Long, BigDecimal> virtualPowers = new HashMap<>();
            for (Long time : calcMap.keySet()){
                virtualPowers.put(time, computeVirtualEnergy(calcMap.get(time).toString()));
            }
            samplerPowerMap.put(sampler, virtualPowers);
            return virtualPowers;
        }else{
            Map<Long, BigDecimal> powerMap = new HashMap<>();
            for (Long time: timeList){
                powerMap.put(time, BigDecimal.ZERO);
            }
            return powerMap;
        }
    }

    private void updateAllElecDayReport(Map<String, BigDecimal> amountMap, Map<String, BigDecimal> energyMap, String category, Date date){
        for (Map.Entry<String, BigDecimal> entry : energyMap.entrySet()){
            updateDayReport(date, entry.getKey(), category, entry.getValue(), amountMap.get(entry.getKey()));
        }
    }

    private void updateDayReport(Date date, String sampler, String category, BigDecimal energy, BigDecimal amount){
        EmsSamplerDayReport dayReport = new EmsSamplerDayReport();
        dayReport.setReportDate(DateUtils.truncate(date, Calendar.DATE));
        dayReport.setCategory(category);
        dayReport.setSampler(sampler);
        dayReport.setValue(energy);
        //用电量峰平谷
        samplerDayReportService.insertEmsSamplerDayReport(dayReport);
        dayReport.setValue(amount);
        dayReport.setCategory(EmsConstants.REPORT_ELEC_AMOUNT);
        //电费
        samplerDayReportService.insertEmsSamplerDayReport(dayReport);
    }

    @NotNull
    private Map<String, BigDecimal> getAmountMap(Map<String, BigDecimal> energyMap, BigDecimal price){
        Map<String, BigDecimal> amountMap = new HashMap<>();
        for (Map.Entry<String, BigDecimal> entry : energyMap.entrySet()){
            amountMap.put(entry.getKey(), entry.getValue().multiply(price));
        }
        return amountMap;
    }

    @NotNull
    private Map<String, BigDecimal> getCarbonMap(Map<String, BigDecimal> energyMap, Date time){
        Map<String, BigDecimal> carbonMap = new HashMap<>();
        EmsCarbonFactor factor = carbonFactorService.selectValidEmsCarbonFactor(time, EmsConstants.ENERGY_CATEGORY_ELECTRICITY);
        if (null != factor){
            for (Map.Entry<String, BigDecimal> entry : energyMap.entrySet()){
                carbonMap.put(entry.getKey(), entry.getValue().multiply(new BigDecimal(Float.toString(factor.getCarbonFactor()))));
            }
        }

        return carbonMap;
    }

    private BigDecimal getMeasuringEnergy(@NotNull EmsMeasuringTool tool, @NotNull Map<String, BigDecimal> energyMap, List<EmsMeasuringTool> toolList) throws ScriptException {
        String sampler = tool.getRealSampler();
        if (energyMap.containsKey(sampler)){
            return energyMap.get(sampler);
        }
        if (tool.isVirtual()){
            List<RecordPO> recordList = JSON.parseArray(tool.getVirtualJson(), RecordPO.class);
            List<Long> toolIds = recordList.stream().map(RecordPO::getData).collect(Collectors.toList());
//            Map<Long, EmsMeasuringTool> samplerMap = new HashMap<>();
//            for (Long toolId : toolIds){
//                for(EmsMeasuringTool measuringTool : toolList){
//                    if (Objects.equals(measuringTool.getMeasuringToolId(), toolId)){
//                        samplerMap.put(toolId, measuringTool);
//                        break;
//                    }
//                }
//            }
            StringBuilder stringBuffer = new StringBuilder();
            for (RecordPO recordPO : recordList){
                for(EmsMeasuringTool measuringTool : toolList){
                    if (Objects.equals(measuringTool.getMeasuringToolId(), recordPO.getData())){
                        BigDecimal samplerEnergy = getMeasuringEnergy(measuringTool, energyMap, toolList);
                        stringBuffer.append(samplerEnergy.toString())
                                .append(Strings.nullToEmpty(recordPO.getSymbol()));
                        break;
                    }
                }
            }
            BigDecimal energy = computeVirtualEnergy(stringBuffer.toString());
            energyMap.put(sampler, energy);
            return energy;
        }
        return new BigDecimal(0);
    }

    @NotNull
    private BigDecimal computeVirtualEnergy(String expression) throws ScriptException {
        ScriptEngineManager engineManager = new ScriptEngineManager();
        ScriptEngine js = engineManager.getEngineByName(EnergyConstant.JS);
        return new BigDecimal(js.eval(expression).toString());
    }

    @NotNull
    private Point buildEnergyPoint(String sampler, BigDecimal energy, Date time){
        return Point.measurement(EmsConstants.INFLUX_POSITIVE_ENERGY_HALF_HOUR)
                .addTag(EmsConstants.INFLUX_SAMPLER_TAG, sampler)
                .addField("value", energy)
                .time(time.getTime(), WritePrecision.MS);
    }

    @NotNull
    private Point buildPowerPoint(String sampler, BigDecimal power, Long time){
        return Point.measurement(EmsConstants.INFLUX_POWER)
                .addTag(EmsConstants.INFLUX_SAMPLER_TAG, sampler)
                .addField("value", power)
                .time(time, WritePrecision.MS);
    }

    @NotNull
    private Point buildCarbonPoint(String sampler, BigDecimal energy, Date time){
        return Point.measurement(EmsConstants.INFLUX_CARBON_HALF_HOUR)
                .addTag(EmsConstants.INFLUX_SAMPLER_TAG, sampler)
                .addField("value", energy)
                .time(time.getTime(), WritePrecision.MS);
    }

    @NotNull
    private Point buildAmountPoint(String sampler, BigDecimal amount, Date time){
        return Point.measurement(EmsConstants.INFLUX_ELEC_AMOUNT_HALF_HOUR)
                .addTag(EmsConstants.INFLUX_SAMPLER_TAG, sampler)
                .addField("value", amount)
                .time(time.getTime(), WritePrecision.MS);
    }

    private void writeVirtualEnergyMapToInflux(@NotNull Map<String, BigDecimal> energyMap, Date time){
        List<Point> points = Lists.newArrayList();
        for (Map.Entry<String, BigDecimal> entry : energyMap.entrySet()){
            points.add(buildEnergyPoint(entry.getKey(), entry.getValue(), time));
        }
        influxDBClient.getWriteApiBlocking().writePoints(influxDBPropertyVo.getReportBucket(), influxDBPropertyVo.getOrg(), points);
    }

    private void writeVirtualPowerMapToInflux(@NotNull Map<Long, BigDecimal> PowerMap, String sampler){
        List<Point> points = Lists.newArrayList();
        for (Map.Entry<Long, BigDecimal> entry : PowerMap.entrySet()){
            points.add(buildPowerPoint(sampler, entry.getValue(), entry.getKey()));
        }
        influxDBClient.getWriteApiBlocking().writePoints(influxDBPropertyVo.getBucket(), influxDBPropertyVo.getOrg(), points);
    }

    private void writeAmountMapToInflux(@NotNull Map<String, BigDecimal> amountMap, Date time){
        List<Point> points = Lists.newArrayList();
        for (Map.Entry<String, BigDecimal> entry : amountMap.entrySet()){
            points.add(buildAmountPoint(entry.getKey(), entry.getValue(), time));
        }
        LOGGER.info("amount points:{}", points.size());
        if (!points.isEmpty()){
            LOGGER.info("time:{}", points.get(0).toLineProtocol());
        }

        influxDBClient.getWriteApiBlocking().writePoints(influxDBPropertyVo.getReportBucket(), influxDBPropertyVo.getOrg(), points);
    }

    private void writeCarbonMapToInflux(@NotNull Map<String, BigDecimal> carbonMap, Date time){
        List<Point> points = Lists.newArrayList();
        for (Map.Entry<String, BigDecimal> entry : carbonMap.entrySet()){
            points.add(buildCarbonPoint(entry.getKey(), entry.getValue(), time));
        }
        influxDBClient.getWriteApiBlocking().writePoints(influxDBPropertyVo.getReportBucket(), influxDBPropertyVo.getOrg(), points);
    }

    @NotNull
    private Map<String, BigDecimal> getAllSamplerEnergyFromInflux(String start, String end){
        Map<String, BigDecimal> energyMap = new HashMap<>();
        String influxQuery = "from(bucket: \""+ influxDBPropertyVo.getReportBucket() +"\")\n" +
                "  |> range(start: "+start+", stop: "+end+")\n" +
                "  |> filter(fn: (r) => r[\"_measurement\"] == \""+EmsConstants.INFLUX_POSITIVE_ENERGY_HALF_HOUR+"\")\n" +
                "  |> filter(fn: (r)=> exists r.device)\n";
        List<FluxTable> results = influxDBClient.getQueryApi().query(influxQuery);
        for (FluxTable table : results){
            if(null == table){
                continue;
            }
            for (FluxRecord record : table.getRecords()){
                if (record.getValueByKey(EmsConstants.INFLUX_SAMPLER_TAG) == null){
                    continue;
                }
                energyMap.put(record.getValueByKey(EmsConstants.INFLUX_SAMPLER_TAG).toString(), new BigDecimal(record.getValue().toString()));
            }
        }
        return energyMap;
    }

    private Map<String, Map<Long, BigDecimal>> getAllSamplerMinutePowerFromInflux(String start, String end, List<Long> timeList){
        Map<String, Map<Long, BigDecimal>> powerMap = new HashMap<>();
        String influxQuery = "from(bucket: \""+ influxDBPropertyVo.getBucket() +"\")\n" +
                "  |> range(start: "+start+", stop: "+end+")\n" +
                "  |> filter(fn: (r) => r[\"_measurement\"] == \""+EmsConstants.POWER+"\")\n" +
                "  |> filter(fn: (r) => exists r.device)\n" +
                "  |> aggregateWindow(every: 1m, fn: mean, createEmpty: true)\n";
        List<FluxTable> results = influxDBClient.getQueryApi().query(influxQuery);
        for (FluxTable table : results){
            Map<Long, BigDecimal> timeMap = new HashMap<>();
            String sampler = "";
            for (FluxRecord record : table.getRecords()){
                BigDecimal power = new BigDecimal(0);
                if (null != record.getValue() && !Strings.isNullOrEmpty(record.getValue().toString())){
                    power = new BigDecimal(record.getValue().toString());
                }
                timeMap.put(record.getTime().toEpochMilli(), power);
                sampler = record.getValueByKey(EmsConstants.INFLUX_SAMPLER_TAG).toString();
            }
            for (Long time:timeList){
                if (!timeMap.containsKey(time)){
                    timeMap.put(time, BigDecimal.ZERO);
                }
            }
            powerMap.put(sampler, timeMap);
        }
        return powerMap;
    }
}
