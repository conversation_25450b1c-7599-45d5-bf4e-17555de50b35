package com.ems.energy.adapter.impl;

import com.ems.energy.adapter.QueryAdapter;
import com.ems.energy.utils.ParameterProcessor;
import com.ems.energy.utils.SqlSecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * MySQL查询适配器实现
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Component
public class MySQLQueryAdapter implements QueryAdapter {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ParameterProcessor parameterProcessor;

    @Autowired
    private SqlSecurityUtils sqlSecurityUtils;

    @Override
    public List<Map<String, Object>> executeQuery(String script, Map<String, Object> params) throws Exception {
        // 验证脚本安全性
        if (!validateScript(script)) {
            throw new SecurityException("脚本包含不安全的内容");
        }

        // 处理参数替换
        String processedScript = processParameters(script, params);

        // 执行查询
        return jdbcTemplate.queryForList(processedScript);
    }

    @Override
    public String getSupportedDbType() {
        return "MYSQL";
    }

    @Override
    public boolean validateScript(String script) {
        return sqlSecurityUtils.validateSql(script);
    }

    @Override
    public String processParameters(String script, Map<String, Object> params) {
        return parameterProcessor.processParameters(script, params, "MYSQL");
    }
}
