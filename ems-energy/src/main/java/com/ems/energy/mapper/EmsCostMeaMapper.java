package com.ems.energy.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsCostMea;

import java.util.List;

/**
 * 成本中心和计量器具中间Mapper接口
 * <AUTHOR>
 * @date 2022-01-04
 */
public interface EmsCostMeaMapper extends BaseMapper<EmsCostMea>
{
    /**
     * 查询成本中心和计量器具中间
     * @param id 成本中心和计量器具中间主键
     * @return 成本中心和计量器具中间
     */
    public EmsCostMea selectEmsCostMeaById(Long id);

    /**
     * 查询成本中心和计量器具中间列表
     * @param emsCostMea 成本中心和计量器具中间
     * @return 成本中心和计量器具中间集合
     */
    public List<EmsCostMea> selectEmsCostMeaList(EmsCostMea emsCostMea);

    /**
     * 新增成本中心和计量器具中间
     * @param emsCostMea 成本中心和计量器具中间
     * @return 结果
     */
    public int insertEmsCostMea(EmsCostMea emsCostMea);

    /**
     * 修改成本中心和计量器具中间
     * @param emsCostMea 成本中心和计量器具中间
     * @return 结果
     */
    public int updateEmsCostMea(EmsCostMea emsCostMea);

    /**
     * 删除成本中心和计量器具中间
     * @param id 成本中心和计量器具中间主键
     * @return 结果
     */
    public int deleteEmsCostMeaById(Long id);

    /**
     * 批量删除成本中心和计量器具中间
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsCostMeaByIds(Long[] ids);

    /**
     * 批量删除成本中心和计量器具中间表数据 根据成本中心ID
     * @param costCenterIds
     * @return
     */
    int deleteEmsCostMeaByCostCenterIds(Long[] costCenterIds);
}
