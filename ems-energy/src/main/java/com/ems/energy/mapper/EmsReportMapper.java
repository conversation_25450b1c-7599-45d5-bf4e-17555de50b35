package com.ems.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 能耗报Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-21
 */
public interface EmsReportMapper extends BaseMapper<EmsReport>
{
    /**
     * 查询能耗报
     *
     * @param reportId 能耗报主键
     * @return 能耗报
     */
    public EmsReport selectEmsReportByReportId(Long reportId);

    /**
     * 查询能耗报列表
     *
     * @param emsReport 能耗报
     * @return 能耗报集合
     */
    public List<EmsReport> selectEmsReportList(EmsReport emsReport);

    /**
     * 新增能耗报
     *
     * @param emsReport 能耗报
     * @return 结果
     */
    public int insertEmsReport(EmsReport emsReport);

    /**
     * 修改能耗报
     *
     * @param emsReport 能耗报
     * @return 结果
     */
    public int updateEmsReport(EmsReport emsReport);

    /**
     * 删除能耗报
     *
     * @param reportId 能耗报主键
     * @return 结果
     */
    public int deleteEmsReportByReportId(Long reportId);

    /**
     * 批量删除能耗报
     *
     * @param reportIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsReportByReportIds(Long[] reportIds);


    List<EmsReport> selectEmsDailyReport(EmsReport emsReport, @Param("yesterday") String yesterday);

}
