package com.ems.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsMonthly;

import java.util.List;


/**
 * 月报Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
public interface EmsMonthlyMapper extends BaseMapper<EmsMonthly>
{
    /**
     * 查询月报
     *
     * @param dailyId 月报主键
     * @return 月报
     */
    public EmsMonthly selectEmsMonthlyByDailyId(Long dailyId);

    /**
     * 查询月报列表
     *
     * @param emsMonthly 月报
     * @return 月报集合
     */
    public List<EmsMonthly> selectEmsMonthlyList(EmsMonthly emsMonthly);

    /**
     * 新增月报
     *
     * @param emsMonthly 月报
     * @return 结果
     */
    public int insertEmsMonthly(EmsMonthly emsMonthly);

    /**
     * 修改月报
     *
     * @param emsMonthly 月报
     * @return 结果
     */
    public int updateEmsMonthly(EmsMonthly emsMonthly);

    /**
     * 删除月报
     *
     * @param dailyId 月报主键
     * @return 结果
     */
    public int deleteEmsMonthlyByDailyId(Long dailyId);

    /**
     * 批量删除月报
     *
     * @param dailyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsMonthlyByDailyIds(Long[] dailyIds);
}
