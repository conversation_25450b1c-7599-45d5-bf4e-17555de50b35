package com.ems.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsDailyData;
import com.ems.energy.domain.EmsDailyDataDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 日报Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
public interface EmsDailyDataMapper extends BaseMapper<EmsDailyData>
{
    /**
     * 查询日报
     *
     * @param dailyId 日报主键
     * @return 日报
     */
    public EmsDailyData selectEmsDailyDataByDailyId(Long dailyId);

    /**
     * 查询日报列表
     *
     * @param emsDailyData 日报
     * @return 日报集合
     */
    public List<EmsDailyData> selectEmsDailyDataList(EmsDailyData emsDailyData);

    /**
     * 新增日报
     *
     * @param emsDailyData 日报
     * @return 结果
     */
    public int insertEmsDailyData(EmsDailyData emsDailyData);

    /**
     * 修改日报
     *
     * @param emsDailyData 日报
     * @return 结果
     */
    public int updateEmsDailyData(EmsDailyData emsDailyData);

    /**
     * 删除日报
     *
     * @param dailyId 日报主键
     * @return 结果
     */
    public int deleteEmsDailyDataByDailyId(Long dailyId);

    /**
     * 批量删除日报
     *
     * @param dailyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsDailyDataByDailyIds(Long[] dailyIds);


    List<EmsDailyDataDTO> selectEmsDailyDatapageList(@Param("format") String format);

}
