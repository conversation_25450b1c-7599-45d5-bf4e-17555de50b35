package com.ems.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsFactory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业架构Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface EmsFactoryMapper extends BaseMapper<EmsFactory>
{
    /**
     * 查询企业架构
     *
     * @param deptId 企业架构主键
     * @return 企业架构
     */
    public EmsFactory selectEmsFactoryByDeptId(Long deptId);

    /**
     * 查询企业架构列表
     *
     * @param emsFactory 企业架构
     * @return 企业架构集合
     */
    public List<EmsFactory> selectEmsFactoryList(EmsFactory emsFactory);

    /**
     * 新增企业架构
     *
     * @param emsFactory 企业架构
     * @return 结果
     */
    public int insertEmsFactory(EmsFactory emsFactory);

    /**
     * 修改企业架构
     *
     * @param emsFactory 企业架构
     * @return 结果
     */
    public int updateEmsFactory(EmsFactory emsFactory);

    /**
     * 删除企业架构
     *
     * @param deptId 企业架构主键
     * @return 结果
     */
    public int deleteEmsFactoryByDeptId(Long deptId);

    /**
     * 批量删除企业架构
     *
     * @param deptIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsFactoryByDeptIds(Long[] deptIds);

    /**
     * 检查部门名称是否唯一
     * @param deptName
     * @param parentId
     * @return
     */
   public EmsFactory checkDeptNameUnique(@Param("deptName")String deptName, @Param("parentId")Long parentId);

    /**
     * 根据企业id查询
     * @param factoryId
     * @return
     */
    public EmsFactory selectFactoryById(Long factoryId);

}
