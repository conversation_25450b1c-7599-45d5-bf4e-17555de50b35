package com.ems.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsDaily;
import com.ems.energy.domain.EmsDailyDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 日报Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-28
 */
public interface EmsDailyMapper extends BaseMapper<EmsDaily>
{
    /**
     * 查询日报
     *
     * @param dailyId 日报主键
     * @return 日报
     */
    public EmsDaily selectEmsDailyByDailyId(Long dailyId);

    /**
     * 查询日报列表
     *
     * @param emsDaily 日报
     * @return 日报集合
     */
    public List<EmsDaily> selectEmsDailyList(EmsDaily emsDaily);

    /**
     * 新增日报
     *
     * @param emsDaily 日报
     * @return 结果
     */
    public int insertEmsDaily(EmsDaily emsDaily);

    /**
     * 修改日报
     *
     * @param emsDaily 日报
     * @return 结果
     */
    public int updateEmsDaily(EmsDaily emsDaily);

    /**
     * 删除日报
     *
     * @param dailyId 日报主键
     * @return 结果
     */
    public int deleteEmsDailyByDailyId(Long dailyId);

    /**
     * 批量删除日报
     *
     * @param dailyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsDailyByDailyIds(Long[] dailyIds);

    public int insertEmsJson(@Param("list") List<EmsDaily> result);

    List<EmsDailyDTO> selectlistOne(@Param("dto") EmsDailyDTO emsDaily);

    List<EmsDaily> selectDaily(EmsDailyDTO emsDailyDTO);

     void insertEmsrbb(@Param("ems")List<EmsDailyDTO> emsDailyDTOS);

    void deleteDto();

    List<EmsDaily> selectTimePeriod(EmsDaily emsDaily,@Param("begin") String sBegin,@Param("end") String sEnd);

    List<EmsDaily> selectTimePeriodgrain(EmsDaily emsDaily, @Param("low") String low, @Param("ebb")String ebb, @Param("ebbone")String ebbone, @Param("lowtwo")String lowtwo);

}
