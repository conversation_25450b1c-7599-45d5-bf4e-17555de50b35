package com.ems.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsMeaCenterRelat;
import com.ems.energy.domain.EmsMeaCenterRelatListDTO;
import com.ems.energy.domain.EmsMeaCenterRelatListPO;
import com.ems.energy.domain.vo.EmsMeaCenterRelatVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 计量器具和成本中心关系Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface EmsMeaCenterRelatMapper extends BaseMapper<EmsMeaCenterRelat>
{
    /**
     * 查询计量器具和成本中心关系
     *
     * @param relatId 计量器具和成本中心关系主键
     * @return 计量器具和成本中心关系
     */
    public EmsMeaCenterRelat selectEmsMeaCenterRelatByRelatId(Long relatId);

    /**
     * 查询计量器具和成本中心关系列表
     *
     * @param emsMeaCenterRelat 计量器具和成本中心关系
     * @return 计量器具和成本中心关系集合
     */
    public List<EmsMeaCenterRelat> selectEmsMeaCenterRelatList(EmsMeaCenterRelat emsMeaCenterRelat);

    /**
     * 新增计量器具和成本中心关系
     *
     * @param emsMeaCenterRelat 计量器具和成本中心关系
     * @return 结果
     */
    public int insertEmsMeaCenterRelat(@Param("dto") EmsMeaCenterRelatListPO emsMeaCenterRelat);

    /**
     * 修改计量器具和成本中心关系
     *
     * @param emsMeaCenterRelat 计量器具和成本中心关系
     * @return 结果
     */
    public int updateEmsMeaCenterRelat(@Param("dto") EmsMeaCenterRelatListPO emsMeaCenterRelat);

    /**
     * 删除计量器具和成本中心关系
     *
     * @param relatId 计量器具和成本中心关系主键
     * @return 结果
     */
    public int deleteEmsMeaCenterRelatByRelatId(Long relatId);

    /**
     * 批量删除计量器具和成本中心关系
     *
     * @param relatIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsMeaCenterRelatByRelatIds(Long[] relatIds);

    List<EmsMeaCenterRelatVo> selectEmsMeaCenterRelatpagelist(@Param("dta") EmsMeaCenterRelatListDTO emsMeaCenterRelat);

    public String selectEmscenterRealt(Integer integer);

    int selectCount();
}
