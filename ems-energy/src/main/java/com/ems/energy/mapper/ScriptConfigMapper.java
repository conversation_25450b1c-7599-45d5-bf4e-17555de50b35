package com.ems.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.ScriptConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 脚本配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Mapper
public interface ScriptConfigMapper extends BaseMapper<ScriptConfig> {

    /**
     * 根据脚本代码查询脚本配置
     *
     * @param code 脚本代码标识符
     * @return 脚本配置
     */
    ScriptConfig selectScriptConfigByCode(@Param("code") String code);
}
