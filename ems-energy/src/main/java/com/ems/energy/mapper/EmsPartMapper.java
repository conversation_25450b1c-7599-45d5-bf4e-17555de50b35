package com.ems.energy.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsPart;
import org.apache.ibatis.annotations.Param;

/**
 * 产品管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-07
 */
public interface EmsPartMapper extends BaseMapper<EmsPart>
{
    /**
     * 查询产品管理
     *
     * @param partId 产品管理主键
     * @return 产品管理
     */
    public EmsPart selectEmsPartByPartId(Long partId);

    /**
     * 查询产品管理列表
     *
     * @param emsPart 产品管理
     * @return 产品管理集合
     */
    public List<EmsPart> selectEmsPartList(EmsPart emsPart);

    /**
     * 新增产品管理
     *
     * @param emsPart 产品管理
     * @return 结果
     */
    public int insertEmsPart(EmsPart emsPart);

    /**
     * 修改产品管理
     *
     * @param emsPart 产品管理
     * @return 结果
     */
    public int updateEmsPart(EmsPart emsPart);

    /**
     * 删除产品管理
     *
     * @param partId 产品管理主键
     * @return 结果
     */
    public int deleteEmsPartByPartId(Long partId);

    /**
     * 批量删除产品管理
     *
     * @param partIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsPartByPartIds(Long[] partIds);

    public EmsPart selectEmsPartByEquipmentIdAndPartPattern(@Param("equipmentId") Long equipmentId, @Param("partPattern") String partPattern);
}
