package com.ems.energy.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsMesuringTool;

/**
 * 表具管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-06
 */
public interface EmsMesuringToolMapper extends BaseMapper<EmsMesuringTool>
{
    /**
     * 查询表具管理
     *
     * @param measuringToolId 表具管理主键
     * @return 表具管理
     */
    public EmsMesuringTool selectEmsMesuringToolByMeasuringToolId(Long measuringToolId);

    /**
     * 查询表具管理列表
     *
     * @param emsMesuringTool 表具管理
     * @return 表具管理集合
     */
    public List<EmsMesuringTool> selectEmsMesuringToolList(EmsMesuringTool emsMesuringTool);

    /**
     * 新增表具管理
     *
     * @param emsMesuringTool 表具管理
     * @return 结果
     */
    public int insertEmsMesuringTool(EmsMesuringTool emsMesuringTool);

    /**
     * 修改表具管理
     *
     * @param emsMesuringTool 表具管理
     * @return 结果
     */
    public int updateEmsMesuringTool(EmsMesuringTool emsMesuringTool);

    /**
     * 删除表具管理
     *
     * @param measuringToolId 表具管理主键
     * @return 结果
     */
    public int deleteEmsMesuringToolByMeasuringToolId(Long measuringToolId);

    /**
     * 批量删除表具管理
     *
     * @param measuringToolIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsMesuringToolByMeasuringToolIds(Long[] measuringToolIds);
}
