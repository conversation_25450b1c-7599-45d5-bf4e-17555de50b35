package com.ems.energy.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ReportMapper {
    public List<Map<String, Object>> queryMonthDayEnergyBySampler(@Param("sampler") String sampler, @Param("categories") List<String> categories, @Param("month") String month);

    public List<Map<String, Object>> querySamplerListByPartIds(Long[] partIds);
}
