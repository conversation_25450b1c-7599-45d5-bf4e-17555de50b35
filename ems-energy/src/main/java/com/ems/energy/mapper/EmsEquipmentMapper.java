package com.ems.energy.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsEquipment;

/**
 * 设备管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
public interface EmsEquipmentMapper extends BaseMapper<EmsEquipment>
{
    /**
     * 查询设备管理
     *
     * @param equipmentId 设备管理主键
     * @return 设备管理
     */
    public EmsEquipment selectEmsEquipmentByEquipmentId(Long equipmentId);

    /**
     * 查询设备管理列表
     *
     * @param emsEquipment 设备管理
     * @return 设备管理集合
     */
    public List<EmsEquipment> selectEmsEquipmentList(EmsEquipment emsEquipment);

    /**
     * 新增设备管理
     *
     * @param emsEquipment 设备管理
     * @return 结果
     */
    public int insertEmsEquipment(EmsEquipment emsEquipment);

    /**
     * 修改设备管理
     *
     * @param emsEquipment 设备管理
     * @return 结果
     */
    public int updateEmsEquipment(EmsEquipment emsEquipment);

    /**
     * 删除设备管理
     *
     * @param equipmentId 设备管理主键
     * @return 结果
     */
    public int deleteEmsEquipmentByEquipmentId(Long equipmentId);

    /**
     * 批量删除设备管理
     *
     * @param equipmentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsEquipmentByEquipmentIds(Long[] equipmentIds);

    public List<EmsEquipment> selectEquipmentByTag(String tag);
}
