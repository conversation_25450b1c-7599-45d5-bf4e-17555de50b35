package com.ems.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsEnergy;

import java.util.List;

/**
 * 能源介质Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface EmsEnergyMapper  extends BaseMapper<EmsEnergy>
{
    /**
     * 查询能源介质
     *
     * @param energyId 能源介质主键
     * @return 能源介质
     */
    public EmsEnergy selectEmsEnergyByEnergyId(Long energyId);

    /**
     * 查询能源介质列表
     *
     * @param emsEnergy 能源介质
     * @return 能源介质集合
     */
    public List<EmsEnergy> selectEmsEnergyList(EmsEnergy emsEnergy);

    /**
     * 新增能源介质
     *
     * @param emsEnergy 能源介质
     * @return 结果
     */
    public int insertEmsEnergy(EmsEnergy emsEnergy);

    /**
     * 修改能源介质
     *
     * @param emsEnergy 能源介质
     * @return 结果
     */
    public int updateEmsEnergy(EmsEnergy emsEnergy);

    /**
     * 删除能源介质
     *
     * @param energyId 能源介质主键
     * @return 结果
     */
    public int deleteEmsEnergyByEnergyId(Long energyId);

    /**
     * 批量删除能源介质
     *
     * @param energyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsEnergyByEnergyIds(Long[] energyIds);

    Integer selectCount();

}
