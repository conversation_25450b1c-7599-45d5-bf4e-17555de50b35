package com.ems.energy.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsCarbonFactor;
import org.apache.ibatis.annotations.Param;

/**
 * 碳排放因子设置Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-02
 */
public interface EmsCarbonFactorMapper extends BaseMapper<EmsCarbonFactor>
{
    /**
     * 查询碳排放因子设置
     *
     * @param factorId 碳排放因子设置主键
     * @return 碳排放因子设置
     */
    public EmsCarbonFactor selectEmsCarbonFactorByFactorId(Long factorId);

    /**
     * 查询碳排放因子设置列表
     *
     * @param emsCarbonFactor 碳排放因子设置
     * @return 碳排放因子设置集合
     */
    public List<EmsCarbonFactor> selectEmsCarbonFactorList(EmsCarbonFactor emsCarbonFactor);

    /**
     * 新增碳排放因子设置
     *
     * @param emsCarbonFactor 碳排放因子设置
     * @return 结果
     */
    public int insertEmsCarbonFactor(EmsCarbonFactor emsCarbonFactor);

    /**
     * 修改碳排放因子设置
     *
     * @param emsCarbonFactor 碳排放因子设置
     * @return 结果
     */
    public int updateEmsCarbonFactor(EmsCarbonFactor emsCarbonFactor);

    /**
     * 删除碳排放因子设置
     *
     * @param factorId 碳排放因子设置主键
     * @return 结果
     */
    public int deleteEmsCarbonFactorByFactorId(Long factorId);

    /**
     * 批量删除碳排放因子设置
     *
     * @param factorIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsCarbonFactorByFactorIds(Long[] factorIds);

    public EmsCarbonFactor selectValidEmsCarbonFactor(@Param("time") Date time, @Param("energyCategory") String energyCategory);
}
