package com.ems.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsProduct;

import java.util.List;


/**
 * 产品Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-18
 */
public interface EmsProductMapper extends BaseMapper<EmsProduct>
{
    /**
     * 查询产品
     *
     * @param proId 产品主键
     * @return 产品
     */
    public EmsProduct selectEmsProductByProId(Long proId);

    /**
     * 查询产品列表
     *
     * @param emsProduct 产品
     * @return 产品集合
     */
    public List<EmsProduct> selectEmsProductList(EmsProduct emsProduct);

    /**
     * 新增产品
     *
     * @param emsProduct 产品
     * @return 结果
     */
    public int insertEmsProduct(EmsProduct emsProduct);

    /**
     * 修改产品
     *
     * @param emsProduct 产品
     * @return 结果
     */
    public int updateEmsProduct(EmsProduct emsProduct);

    /**
     * 删除产品
     *
     * @param proId 产品主键
     * @return 结果
     */
    public int deleteEmsProductByProId(Long proId);

    /**
     * 批量删除产品
     *
     * @param proIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsProductByProIds(Long[] proIds);
}
