package com.ems.energy.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsMeasuringTool;
import org.apache.ibatis.annotations.Param;

/**
 * 表具Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-07
 */
public interface EmsMeasuringToolMapper extends BaseMapper<EmsMeasuringTool>
{
    /**
     * 查询表具
     *
     * @param measuringToolId 表具主键
     * @return 表具
     */
    public EmsMeasuringTool selectEmsMeasuringToolByMeasuringToolId(Long measuringToolId);

    /**
     * 查询表具列表
     *
     * @param emsMeasuringTool 表具
     * @return 表具集合
     */
    public List<EmsMeasuringTool> selectEmsMeasuringToolList(EmsMeasuringTool emsMeasuringTool);

    /**
     * 查询实体表具列表
     *
     * @param emsMeasuringTool 表具
     * @return 表具集合
     */
    public List<EmsMeasuringTool> selectRealEmsMeasuringToolList(EmsMeasuringTool emsMeasuringTool);

    /**
     * 新增表具
     *
     * @param emsMeasuringTool 表具
     * @return 结果
     */
    public int insertEmsMeasuringTool(EmsMeasuringTool emsMeasuringTool);

    /**
     * 修改表具
     *
     * @param emsMeasuringTool 表具
     * @return 结果
     */
    public int updateEmsMeasuringTool(EmsMeasuringTool emsMeasuringTool);

    /**
     * 删除表具
     *
     * @param measuringToolId 表具主键
     * @return 结果
     */
    public int deleteEmsMeasuringToolByMeasuringToolId(Long measuringToolId);

    /**
     * 批量删除表具
     *
     * @param measuringToolIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsMeasuringToolByMeasuringToolIds(Long[] measuringToolIds);

    public int counEmsMeasuringToolByIds(@Param("ids") List<String> ids);

    public List<EmsMeasuringTool> selectEmsMeasuringToolByTag(@Param("tag") String tag);

    public List<EmsMeasuringTool> selectEmsMeasuringToolListByIds(Long[] measuringToolIds);
}
