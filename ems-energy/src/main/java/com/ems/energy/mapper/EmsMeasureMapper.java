package com.ems.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsMeasure;
import com.ems.energy.domain.EmsMeasureListDTO;
import com.ems.energy.domain.vo.EmsMeasureVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 计量器具Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface EmsMeasureMapper extends BaseMapper<EmsMeasure>
{
    /**
     * 查询计量器具
     *
     * @param meaId 计量器具主键
     * @return 计量器具
     */
    public EmsMeasure selectEmsMeasureByMeaId(Long meaId);

    /**
     * 查询计量器具列表
     *
     * @param emsMeasure 计量器具
     * @return 计量器具集合
     */
    List<EmsMeasure> selectEmsMeasureList(EmsMeasure emsMeasure);

    /**
     * 新增计量器具
     *
     * @param emsMeasure 计量器具
     * @return 结果
     */
    int insertEmsMeasure(EmsMeasure emsMeasure);

    /**
     * 修改计量器具
     *
     * @param emsMeasure 计量器具
     * @return 结果
     */
    int updateEmsMeasure(EmsMeasure emsMeasure);

    /**
     * 删除计量器具
     *
     * @param meaId 计量器具主键
     * @return 结果
     */
    int deleteEmsMeasureByMeaId(Long meaId);

    /**
     * 批量删除计量器具
     *
     * @param meaIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteEmsMeasureByMeaIds(Long[] meaIds);

    List<EmsMeasureListDTO> selectEmsMeasureListDTO(@Param("listDTO") EmsMeasureListDTO listDTO);

    List<EmsMeasureVo> selectEmsMeasureListPage(EmsMeasureVo emsMeasure);

    EmsMeasure checkDeptNameUnique(@Param("meaName") String meaName,@Param("meaParentId") Long meaParentId);

    Integer selectByMeaid(Long meaId);


    int countEmsMeaCenterRelatByIds(@Param("ids") List<String> ids);

    public  List<EmsMeasure> selectEmsMeasureBycostCent(Long costCenterId);
}
