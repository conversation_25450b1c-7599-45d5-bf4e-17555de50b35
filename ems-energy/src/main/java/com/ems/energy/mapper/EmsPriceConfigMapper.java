package com.ems.energy.mapper;

import java.util.Date;
import java.util.List;
import com.ems.energy.domain.EmsPriceConfig;
import com.ems.energy.domain.EmsPrice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsPricePeriod;

/**
 * 电价配置Mapper接口
 *
 * <AUTHOR>
 * @date 2022-05-30
 */
public interface EmsPriceConfigMapper extends BaseMapper<EmsPriceConfig>
{
    /**
     * 查询电价配置
     *
     * @param configId 电价配置主键
     * @return 电价配置
     */
    public EmsPriceConfig selectEmsPriceConfigByConfigId(Long configId);

    public EmsPriceConfig selectValidEmsPriceConfigByTime(Date time);

    /**
     * 查询电价配置列表
     *
     * @param emsPriceConfig 电价配置
     * @return 电价配置集合
     */
    public List<EmsPriceConfig> selectEmsPriceConfigList(EmsPriceConfig emsPriceConfig);

    /**
     * 新增电价配置
     *
     * @param emsPriceConfig 电价配置
     * @return 结果
     */
    public int insertEmsPriceConfig(EmsPriceConfig emsPriceConfig);

    /**
     * 修改电价配置
     *
     * @param emsPriceConfig 电价配置
     * @return 结果
     */
    public int updateEmsPriceConfig(EmsPriceConfig emsPriceConfig);

    /**
     * 删除电价配置
     *
     * @param configId 电价配置主键
     * @return 结果
     */
    public int deleteEmsPriceConfigByConfigId(Long configId);

    /**
     * 批量删除电价配置
     *
     * @param configIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsPriceConfigByConfigIds(Long[] configIds);

    /**
     * 批量删除价格
     *
     * @param configIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsPriceByConfigIds(Long[] configIds);

    /**
     * 批量新增价格
     *
     * @param emsPriceList 价格列表
     * @return 结果
     */
    public int batchEmsPrice(List<EmsPrice> emsPriceList);


    /**
     * 通过电价配置主键删除价格信息
     *
     * @param configId 电价配置ID
     * @return 结果
     */
    public int deleteEmsPriceByConfigId(Long configId);

    /**
     * 批量删除价格时段
     *
     * @param configIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsPricePeriodByConfigIds(Long[] configIds);

    /**
     * 批量新增价格时段
     *
     * @param emsPricePeriodList 价格时段列表
     * @return 结果
     */
    public int batchEmsPricePeriod(List<EmsPricePeriod> emsPricePeriodList);


    /**
     * 通过电价配置主键删除价格时段信息
     *
     * @param configId 电价配置ID
     * @return 结果
     */
    public int deleteEmsPricePeriodByConfigId(Long configId);
}
