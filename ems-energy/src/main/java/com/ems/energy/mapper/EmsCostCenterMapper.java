package com.ems.energy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsCostCenter;
import com.ems.energy.domain.EmsFactory;
import com.ems.energy.domain.vo.EmsCostCenterVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成本中心Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface EmsCostCenterMapper extends BaseMapper<EmsCostCenter>
{
    /**
     * 查询成本中心
     *
     * @param centerId 成本中心主键
     * @return 成本中心
     */
    public EmsCostCenter selectEmsCostCenterByCenterId(Long centerId);

    /**
     * 查询成本中心列表
     *
     * @param emsCostCenter 成本中心
     * @return 成本中心集合
     */
    List<EmsCostCenter> selectEmsCostCenterList(EmsCostCenter emsCostCenter);

    /**
     * 新增成本中心
     *
     * @param emsCostCenter 成本中心
     * @return 结果
     */
    public int insertEmsCostCenter(EmsCostCenter emsCostCenter);

    /**
     * 修改成本中心
     *
     * @param emsCostCenter 成本中心
     * @return 结果
     */
    public int updateEmsCostCenter(EmsCostCenter emsCostCenter);

    /**
     * 删除成本中心
     *
     * @param centerId 成本中心主键
     * @return 结果
     */
    public int deleteEmsCostCenterByCenterId(Long centerId);

    /**
     * 批量删除成本中心
     *
     * @param centerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsCostCenterByCenterIds(Long[] centerIds);

    /**
     * 校验成本中心是否唯一
     * @param centerName
     * @param parentCenterId
     * @return
     */
    public EmsFactory checkCostcentNameUnique(@Param("centerName") String centerName, @Param("parentCenterId") Long parentCenterId);

    public EmsCostCenter selectFactoryById(Long parentCenterId);


    public int insertCostent(EmsCostCenter emsCostCenter);

    List<EmsCostCenterVo> selectEmsCostCenterByPage(@Param("e1") EmsCostCenter emsCostCenter);

    EmsCostCenter checkDeptNameUnique(@Param("centerName") String centerName,@Param("parentCenterId") Long parentCenterId);

    Integer selectCount();
}
