package com.ems.energy.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsSamplerDayReport;

/**
 * 日数据报Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-01
 */
public interface EmsSamplerDayReportMapper extends BaseMapper<EmsSamplerDayReport>
{
    /**
     * 查询日数据报
     *
     * @param reportId 日数据报主键
     * @return 日数据报
     */
    public EmsSamplerDayReport selectEmsSamplerDayReportByReportId(Long reportId);

    /**
     * 查询日数据报列表
     *
     * @param emsSamplerDayReport 日数据报
     * @return 日数据报集合
     */
    public List<EmsSamplerDayReport> selectEmsSamplerDayReportList(EmsSamplerDayReport emsSamplerDayReport);

    /**
     * 新增日数据报
     *
     * @param emsSamplerDayReport 日数据报
     * @return 结果
     */
    public int insertEmsSamplerDayReport(EmsSamplerDayReport emsSamplerDayReport);

    /**
     * 修改日数据报
     *
     * @param emsSamplerDayReport 日数据报
     * @return 结果
     */
    public int updateEmsSamplerDayReport(EmsSamplerDayReport emsSamplerDayReport);

    /**
     * 删除日数据报
     *
     * @param reportId 日数据报主键
     * @return 结果
     */
    public int deleteEmsSamplerDayReportByReportId(Long reportId);

    /**
     * 批量删除日数据报
     *
     * @param reportIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsSamplerDayReportByReportIds(Long[] reportIds);
}
