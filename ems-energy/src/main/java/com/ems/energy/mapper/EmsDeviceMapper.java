package com.ems.energy.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ems.energy.domain.EmsDevice;
import com.ems.energy.domain.vo.EmsDeviceVo;

import java.util.List;

/**
 * 工厂设备Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
public interface EmsDeviceMapper extends BaseMapper<EmsDevice>
{
    /**
     * 查询工厂设备
     *
     * @param equId 工厂设备主键
     * @return 工厂设备
     */
    public EmsDevice selectEmsDeviceByEquId(Long equId);

    /**
     * 查询工厂设备列表
     *
     * @param emsDevice 工厂设备
     * @return 工厂设备集合
     */
    public List<EmsDeviceVo> selectEmsDeviceList(EmsDeviceVo emsDevice);

    /**
     * 新增工厂设备
     *
     * @param emsDevice 工厂设备
     * @return 结果
     */
    public int insertEmsDevice(EmsDevice emsDevice);

    /**
     * 修改工厂设备
     *
     * @param emsDevice 工厂设备
     * @return 结果
     */
    public int updateEmsDevice(EmsDevice emsDevice);

    /**
     * 删除工厂设备
     *
     * @param equId 工厂设备主键
     * @return 结果
     */
    public int deleteEmsDeviceByEquId(Long equId);

    /**
     * 批量删除工厂设备
     *
     * @param equIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmsDeviceByEquIds(Long[] equIds);
}
