package com.ems.energy.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;

/**
 * 能源介质对象 ems_energy
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public class EmsEnergy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 能源介质id */
    private Long energyId;

    /** 能源编号 */
    @Excel(name = "能源编号")
    private String energyCode;

    /** 能源名称 */
    @Excel(name = "能源名称")
    private String energyName;

    /** 乐观锁(0删除,1正常) */
    @Excel(name = "乐观锁(0删除,1正常)")
    @TableLogic
    private String delFlag;

    @Override
    public String toString() {
        return "EmsEnergy{" +
                "energyId=" + energyId +
                ", energyCode='" + energyCode + '\'' +
                ", energyName='" + energyName + '\'' +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }

    public Long getEnergyId() {
        return energyId;
    }

    public void setEnergyId(Long energyId) {
        this.energyId = energyId;
    }

    public String getEnergyCode() {
        return energyCode;
    }

    public void setEnergyCode(String energyCode) {
        this.energyCode = energyCode;
    }

    public String getEnergyName() {
        return energyName;
    }

    public void setEnergyName(String energyName) {
        this.energyName = energyName;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public EmsEnergy() {
    }

    public EmsEnergy(Long energyId, String energyCode, String energyName, String delFlag) {
        this.energyId = energyId;
        this.energyCode = energyCode;
        this.energyName = energyName;
        this.delFlag = delFlag;
    }
}
