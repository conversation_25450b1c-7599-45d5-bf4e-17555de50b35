package com.ems.energy.domain;

import com.ems.common.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * @className: ReportData
 * @description:
 * @author: RK
 * @date: 2021/12/27 12:58
 **/
public class ReportData extends BaseEntity {
    /**
     * 产品名称
     * @return
     */
    private String productName;
    /**
     * 产量(件)
     */
    private Long outputPiece;
    /**
     * 产品(模)
     */
    private Long outputModel;

    /**
     * 产品总重量
     */
    private Long productTotalWeight;

    /**
     * 生产时间
     */
    private BigDecimal productionTime;

    /**
     * 生产耗电量
     * @return
     */
    private BigDecimal powerConsumption;

    /**
     * 分钟耗电
     * @return
     */
    private BigDecimal powerConsOfMinute;

    /**
     * 每件耗电
     */
    private BigDecimal powerConsOfPiece;

    /**
     * 每模耗电
     */
    private BigDecimal powerCOnsOfPiece;

    @Override
    public String toString() {
        return "ReportData{" +
                "productName='" + productName + '\'' +
                ", outputPiece=" + outputPiece +
                ", outputModel=" + outputModel +
                ", productTotalWeight=" + productTotalWeight +
                ", productionTime=" + productionTime +
                ", powerConsumption=" + powerConsumption +
                ", powerConsOfMinute=" + powerConsOfMinute +
                ", powerConsOfPiece=" + powerConsOfPiece +
                ", powerCOnsOfPiece=" + powerCOnsOfPiece +
                '}';
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getOutputPiece() {
        return outputPiece;
    }

    public void setOutputPiece(Long outputPiece) {
        this.outputPiece = outputPiece;
    }

    public Long getOutputModel() {
        return outputModel;
    }

    public void setOutputModel(Long outputModel) {
        this.outputModel = outputModel;
    }

    public Long getProductTotalWeight() {
        return productTotalWeight;
    }

    public void setProductTotalWeight(Long productTotalWeight) {
        this.productTotalWeight = productTotalWeight;
    }

    public BigDecimal getProductionTime() {
        return productionTime;
    }

    public void setProductionTime(BigDecimal productionTime) {
        this.productionTime = productionTime;
    }

    public BigDecimal getPowerConsumption() {
        return powerConsumption;
    }

    public void setPowerConsumption(BigDecimal powerConsumption) {
        this.powerConsumption = powerConsumption;
    }

    public BigDecimal getPowerConsOfMinute() {
        return powerConsOfMinute;
    }

    public void setPowerConsOfMinute(BigDecimal powerConsOfMinute) {
        this.powerConsOfMinute = powerConsOfMinute;
    }

    public BigDecimal getPowerConsOfPiece() {
        return powerConsOfPiece;
    }

    public void setPowerConsOfPiece(BigDecimal powerConsOfPiece) {
        this.powerConsOfPiece = powerConsOfPiece;
    }

    public BigDecimal getPowerCOnsOfPiece() {
        return powerCOnsOfPiece;
    }

    public void setPowerCOnsOfPiece(BigDecimal powerCOnsOfPiece) {
        this.powerCOnsOfPiece = powerCOnsOfPiece;
    }

    public ReportData() {
    }

    public ReportData(String productName, Long outputPiece, Long outputModel, Long productTotalWeight, BigDecimal productionTime, BigDecimal powerConsumption, BigDecimal powerConsOfMinute, BigDecimal powerConsOfPiece, BigDecimal powerCOnsOfPiece) {
        this.productName = productName;
        this.outputPiece = outputPiece;
        this.outputModel = outputModel;
        this.productTotalWeight = productTotalWeight;
        this.productionTime = productionTime;
        this.powerConsumption = powerConsumption;
        this.powerConsOfMinute = powerConsOfMinute;
        this.powerConsOfPiece = powerConsOfPiece;
        this.powerCOnsOfPiece = powerCOnsOfPiece;
    }
}
