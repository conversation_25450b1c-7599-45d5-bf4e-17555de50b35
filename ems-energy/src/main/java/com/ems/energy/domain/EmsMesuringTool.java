package com.ems.energy.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;

/**
 * 表具管理对象 ems_mesuring_tool
 *
 * <AUTHOR>
 * @date 2022-03-06
 */
public class EmsMesuringTool extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表具ID */
    private Long measuringToolId;

    /** 表具编号 */
    @Excel(name = "表具编号")
    private String measuringToolCode;

    /** 表具名称 */
    @Excel(name = "表具名称")
    private String measuringToolName;

    /** 标签 */
    @Excel(name = "标签")
    private String tag;

    /** 表具类型 */
    @Excel(name = "表具类型")
    private String type;

    /** 采集器 */
    private String sampler;

    /** 采集器型号 */
    private String samplerModel;

    /** 成本中心 */
    private Long costCenterId;

    /** 检定日期 */
    private Date checkDate;

    /** 检定单位 */
    private String checkOrg;

    /** 证书编号 */
    private String certCode;

    /** 安装位置 */
    private String position;

    /** 下次检定日期 */
    private Date nextCheckDate;

    /** 上级表具 */
    private Long parentId;

    /** 计算公式 */
    private String virtualExpression;

    /** 计算JSON */
    private String virtualJson;

    /** 变量 */
    private String virtualVariable;

    public void setMeasuringToolId(Long measuringToolId)
    {
        this.measuringToolId = measuringToolId;
    }

    public Long getMeasuringToolId()
    {
        return measuringToolId;
    }
    public void setMeasuringToolCode(String measuringToolCode)
    {
        this.measuringToolCode = measuringToolCode;
    }

    public String getMeasuringToolCode()
    {
        return measuringToolCode;
    }
    public void setMeasuringToolName(String measuringToolName)
    {
        this.measuringToolName = measuringToolName;
    }

    public String getMeasuringToolName()
    {
        return measuringToolName;
    }
    public void setTag(String tag)
    {
        this.tag = tag;
    }

    public String getTag()
    {
        return tag;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setSampler(String sampler)
    {
        this.sampler = sampler;
    }

    public String getSampler()
    {
        return sampler;
    }
    public void setSamplerModel(String samplerModel)
    {
        this.samplerModel = samplerModel;
    }

    public String getSamplerModel()
    {
        return samplerModel;
    }
    public void setCostCenterId(Long costCenterId)
    {
        this.costCenterId = costCenterId;
    }

    public Long getCostCenterId()
    {
        return costCenterId;
    }
    public void setCheckDate(Date checkDate)
    {
        this.checkDate = checkDate;
    }

    public Date getCheckDate()
    {
        return checkDate;
    }
    public void setCheckOrg(String checkOrg)
    {
        this.checkOrg = checkOrg;
    }

    public String getCheckOrg()
    {
        return checkOrg;
    }
    public void setCertCode(String certCode)
    {
        this.certCode = certCode;
    }

    public String getCertCode()
    {
        return certCode;
    }
    public void setPosition(String position)
    {
        this.position = position;
    }

    public String getPosition()
    {
        return position;
    }
    public void setNextCheckDate(Date nextCheckDate)
    {
        this.nextCheckDate = nextCheckDate;
    }

    public Date getNextCheckDate()
    {
        return nextCheckDate;
    }
    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public Long getParentId()
    {
        return parentId;
    }
    public void setVirtualExpression(String virtualExpression)
    {
        this.virtualExpression = virtualExpression;
    }

    public String getVirtualExpression()
    {
        return virtualExpression;
    }
    public void setVirtualJson(String virtualJson)
    {
        this.virtualJson = virtualJson;
    }

    public String getVirtualJson()
    {
        return virtualJson;
    }
    public void setVirtualVariable(String virtualVariable)
    {
        this.virtualVariable = virtualVariable;
    }

    public String getVirtualVariable()
    {
        return virtualVariable;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("measuringToolId", getMeasuringToolId())
            .append("measuringToolCode", getMeasuringToolCode())
            .append("measuringToolName", getMeasuringToolName())
            .append("tag", getTag())
            .append("type", getType())
            .append("sampler", getSampler())
            .append("samplerModel", getSamplerModel())
            .append("costCenterId", getCostCenterId())
            .append("checkDate", getCheckDate())
            .append("checkOrg", getCheckOrg())
            .append("certCode", getCertCode())
            .append("position", getPosition())
            .append("nextCheckDate", getNextCheckDate())
            .append("parentId", getParentId())
            .append("virtualExpression", getVirtualExpression())
            .append("virtualJson", getVirtualJson())
            .append("virtualVariable", getVirtualVariable())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
