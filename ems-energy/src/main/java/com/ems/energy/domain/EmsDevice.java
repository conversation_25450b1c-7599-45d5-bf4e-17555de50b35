package com.ems.energy.domain;

import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 工厂设备对象 ems_device
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
public class EmsDevice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 工厂设备id */
    private Long equId;

    /** 设备号 */
    @Excel(name = "设备号")
    private String deviceNum;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 计量器具(一对一的,设备对应一个电表) */
    @Excel(name = "计量器具(一对一的,设备对应一个电表)")
    private Long meaId;

    /** 能源介质 */
    @Excel(name = "能源介质")
    private Long energyId;

    public void setEquId(Long equId)
    {
        this.equId = equId;
    }

    public Long getEquId()
    {
        return equId;
    }
    public void setDeviceNum(String deviceNum)
    {
        this.deviceNum = deviceNum;
    }

    public String getDeviceNum()
    {
        return deviceNum;
    }
    public void setDeviceName(String deviceName)
    {
        this.deviceName = deviceName;
    }

    public String getDeviceName()
    {
        return deviceName;
    }
    public void setMeaId(Long meaId)
    {
        this.meaId = meaId;
    }

    public Long getMeaId()
    {
        return meaId;
    }

    public void setEnergyId(Long energyId)
    {
        this.energyId = energyId;
    }

    public Long getEnergyId()
    {
        return energyId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("equId", getEquId())
            .append("deviceNum", getDeviceNum())
            .append("deviceName", getDeviceName())
            .append("meaId", getMeaId())
            .append("energyId", getEnergyId())
            .toString();
    }
}
