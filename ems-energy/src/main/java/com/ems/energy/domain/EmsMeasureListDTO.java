package com.ems.energy.domain;

import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;

/**
 * @className: EmsMeasureListDTO
 * @description:
 * @author: RK
 * @date: 2021/12/15 14:09
 **/
public class EmsMeasureListDTO extends BaseEntity {

    private Long meaId;
    @Excel(name = "编号")
    private String meaCode;

    private Long costCenterId;

    @Excel(name = "成本中心")
    private String costCenterName;

    @Excel(name = "自定义名称")
    private String custName;

    private Long energyMed;

    @Excel(name = "能源介质")
    private String energyName;

    @Excel(name = "关系")
    private String relationExpression;

    @Excel(name = "说明")
    private String description;

    public Long getMeaId() {
        return meaId;
    }

    public void setMeaId(Long meaId) {
        this.meaId = meaId;
    }

    public String getMeaCode() {
        return meaCode;
    }

    public void setMeaCode(String meaCode) {
        this.meaCode = meaCode;
    }

    public Long getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(Long costCenterId) {
        this.costCenterId = costCenterId;
    }

    public String getCostCenterName() {
        return costCenterName;
    }

    public void setCostCenterName(String costCenterName) {
        this.costCenterName = costCenterName;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public Long getEnergyMed() {
        return energyMed;
    }

    public void setEnergyMed(Long energyMed) {
        this.energyMed = energyMed;
    }

    public String getEnergyName() {
        return energyName;
    }

    public void setEnergyName(String energyName) {
        this.energyName = energyName;
    }

    public String getRelationExpression() {
        return relationExpression;
    }

    public void setRelationExpression(String relationExpression) {
        this.relationExpression = relationExpression;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public EmsMeasureListDTO() {
    }

    public EmsMeasureListDTO(Long meaId, String meaCode,Long costCenterId, String costCenterName, String custName, Long energyMed, String energyName, String relationExpression, String description) {
        this.meaId = meaId;
        this.meaCode = meaCode;
        this.costCenterId = costCenterId;
        this.costCenterName = costCenterName;
        this.custName = custName;
        this.energyMed = energyMed;
        this.energyName = energyName;
        this.relationExpression = relationExpression;
        this.description = description;
    }

    @Override
    public String toString() {
        return "EmsMeasureListDTO{" +
                "meaId=" + meaId +
                ", meaCode='" + meaCode + '\'' +
                ", costCenterId=" + costCenterId +
                ", costCenterName='" + costCenterName + '\'' +
                ", custName='" + custName + '\'' +
                ", energyMed=" + energyMed +
                ", energyName='" + energyName + '\'' +
                ", relationExpression='" + relationExpression + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}

