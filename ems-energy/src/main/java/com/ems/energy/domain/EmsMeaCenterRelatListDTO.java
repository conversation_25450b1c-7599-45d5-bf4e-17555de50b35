package com.ems.energy.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;

import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * @className: DataConstant
 * @description:
 * @author: RK
 * @date: 2021/12/29 9:25
 **/
public class EmsMeaCenterRelatListDTO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 维护关系id */
    private Long relatId;

    /** 维护类别(1成本中心,2自定义) 有成本中心名称无自定义名称 */
    @Excel(name = "维护类别(1成本中心,2自定义) 有成本中心名称无自定义名称")
    private Long keepType;

    /** 自定义名称 */
    @Excel(name = "自定义名称")
    private String custName;

    /** 能源成本中心id(非空) */
    @Excel(name = "能源成本中心id(非空)")
    private Long costCenterId;

    /** 能源介质(非空) */
    @Excel(name = "能源介质(非空)")
    private Long energyId;

    /** 企业id*/
    @NotNull(message = "企业不能为空")
    private Long factoryFirmId;

    /** 维护关系 */
    @Excel(name = "维护关系")
    private String relationExpression;
    /** 编号*/
    private String relatCode;
    /** 说明 */
    @Excel(name = "说明")
    private String description;

    /** 乐观锁(0禁用,1启用) */
    @Excel(name = "乐观锁(0禁用,1启用)")
    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private List<Integer> numberList;

    private List<RecordPO> recordList;

    @Override
    public String toString() {
        return "EmsMeaCenterRelatListDTO{" +
                "relatId=" + relatId +
                ", keepType=" + keepType +
                ", custName='" + custName + '\'' +
                ", costCenterId=" + costCenterId +
                ", energyId=" + energyId +
                ", factoryFirmId=" + factoryFirmId +
                ", relationExpression='" + relationExpression + '\'' +
                ", relatCode='" + relatCode + '\'' +
                ", description='" + description + '\'' +
                ", delFlag='" + delFlag + '\'' +
                ", numberList=" + numberList +
                ", recordList=" + recordList +
                '}';
    }

    public Long getRelatId() {
        return relatId;
    }

    public void setRelatId(Long relatId) {
        this.relatId = relatId;
    }

    public Long getKeepType() {
        return keepType;
    }

    public void setKeepType(Long keepType) {
        this.keepType = keepType;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public Long getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(Long costCenterId) {
        this.costCenterId = costCenterId;
    }

    public Long getEnergyId() {
        return energyId;
    }

    public void setEnergyId(Long energyId) {
        this.energyId = energyId;
    }

    public Long getFactoryFirmId() {
        return factoryFirmId;
    }

    public void setFactoryFirmId(Long factoryFirmId) {
        this.factoryFirmId = factoryFirmId;
    }

    public String getRelationExpression() {
        return relationExpression;
    }

    public void setRelationExpression(String relationExpression) {
        this.relationExpression = relationExpression;
    }

    public String getRelatCode() {
        return relatCode;
    }

    public void setRelatCode(String relatCode) {
        this.relatCode = relatCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public List<Integer> getNumberList() {
        return numberList;
    }

    public void setNumberList(List<Integer> numberList) {
        this.numberList = numberList;
    }

    public List<RecordPO> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<RecordPO> recordList) {
        this.recordList = recordList;
    }

    public EmsMeaCenterRelatListDTO() {
    }

    public EmsMeaCenterRelatListDTO(Long relatId, Long keepType, String custName, Long costCenterId, Long energyId, Long factoryFirmId, String relationExpression, String relatCode, String description, String delFlag, List<Integer> numberList, List<RecordPO> recordList) {
        this.relatId = relatId;
        this.keepType = keepType;
        this.custName = custName;
        this.costCenterId = costCenterId;
        this.energyId = energyId;
        this.factoryFirmId = factoryFirmId;
        this.relationExpression = relationExpression;
        this.relatCode = relatCode;
        this.description = description;
        this.delFlag = delFlag;
        this.numberList = numberList;
        this.recordList = recordList;
    }
}
