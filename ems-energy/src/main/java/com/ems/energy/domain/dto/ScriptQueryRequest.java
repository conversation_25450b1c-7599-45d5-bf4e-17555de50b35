package com.ems.energy.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 脚本查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@ApiModel("脚本查询请求")
public class ScriptQueryRequest {

    @ApiModelProperty(value = "脚本代码标识符", required = true, example = "QUERY_ENERGY_BY_DATE")
    @NotBlank(message = "脚本代码标识符不能为空")
    private String code;

    @ApiModelProperty(value = "查询参数", example = "{\"startDate\":\"2025-01-01\",\"endDate\":\"2025-01-31\"}")
    private Map<String, Object> params;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    @Override
    public String toString() {
        return "ScriptQueryRequest{" +
                "code='" + code + '\'' +
                ", params=" + params +
                '}';
    }
}
