package com.ems.energy.domain;

import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 成本中心和计量器具中间
对象 ems_cost_mea
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
public class EmsCostMea extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 关系ID */
    private Long id;

    /** 成本中心ID */
    @Excel(name = "成本中心ID")
    private Long costCenterId;

    /** 计量器具ID */
    @Excel(name = "计量器具ID")
    private Long meaId;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCostCenterId(Long costCenterId)
    {
        this.costCenterId = costCenterId;
    }

    public Long getCostCenterId()
    {
        return costCenterId;
    }
    public void setMeaId(Long meaId)
    {
        this.meaId = meaId;
    }

    public Long getMeaId()
    {
        return meaId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("costCenterId", getCostCenterId())
            .append("meaId", getMeaId())
            .toString();
    }
}
