package com.ems.energy.domain.tree;

import com.ems.common.core.domain.entity.SysMenu;
import com.ems.energy.domain.EmsCostCenter;
import com.ems.energy.domain.EmsMeasure;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
/**
 * @className: DataConstant
 * @description:
 * @author: RK
 * @date: 2021/12/29 9:25
 **/
public class Emstreecenter implements Serializable
{

    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Emstreecenter> children;

    public Emstreecenter()
    {

    }

    public Emstreecenter(EmsCostCenter costCenter)
    {
        this.id = costCenter.getCenterId();
        this.label = costCenter.getCenterName();
        this.children = costCenter.getChildren().stream().map(Emstreecenter::new).collect(Collectors.toList());
    }

    public Emstreecenter(SysMenu menu)
    {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        this.children = menu.getChildren().stream().map(Emstreecenter::new).collect(Collectors.toList());
    }

    public Emstreecenter(EmsMeasure emsMeasure) {
        this.id = emsMeasure.getMeaId();
        this.label = emsMeasure.getMeaName();
        this.children = emsMeasure.getChildren()!=null ?emsMeasure.getChildren().stream().map(Emstreecenter::new).collect(Collectors.toList()):null;
    }


    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public List<Emstreecenter> getChildren()
    {
        return children;
    }

    public void setChildren(List<Emstreecenter> children)
    {
        this.children = children;
    }


}
