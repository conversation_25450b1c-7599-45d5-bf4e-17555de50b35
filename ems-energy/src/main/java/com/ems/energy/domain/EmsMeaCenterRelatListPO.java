package com.ems.energy.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;

import java.util.List;

/**
 * @className: EmsMeaCenterRelatListPO
 * @description:
 * @author: RK
 * @date: 2021/12/24 18:17
 **/
public class EmsMeaCenterRelatListPO extends BaseEntity {
    /** 维护关系id */
    private Long relatId;

    /** 维护类别(1成本中心,2自定义) 有成本中心名称无自定义名称 */
    @Excel(name = "维护类别(1成本中心,2自定义) 有成本中心名称无自定义名称")
    private Long keepType;

    /** 自定义名称 */
    @Excel(name = "自定义名称")
    private String custName;

    /** 能源成本中心id(非空) */
    @Excel(name = "能源成本中心id(非空)")
    private Long costCenterId;

    /** 能源介质(非空) */
    @Excel(name = "能源介质(非空)")
    private Long energyId;
    /** 编号*/
    private String relatCode;
    private Long factoryFirmId;
    /** 维护关系 */
    @Excel(name = "维护关系")
    private String relationExpression;

    /** 说明 */
    @Excel(name = "说明")
    private String description;

    /** 乐观锁(0禁用,1启用) */
    @Excel(name = "乐观锁(0禁用,1启用)")
    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private List<Integer> numberList;

    private String recordList;

    @Override
    public String toString() {
        return "EmsMeaCenterRelatListPO{" +
                "relatId=" + relatId +
                ", keepType=" + keepType +
                ", custName='" + custName + '\'' +
                ", costCenterId=" + costCenterId +
                ", energyId=" + energyId +
                ", relatCode='" + relatCode + '\'' +
                ", factoryFirmId=" + factoryFirmId +
                ", relationExpression='" + relationExpression + '\'' +
                ", description='" + description + '\'' +
                ", delFlag='" + delFlag + '\'' +
                ", numberList=" + numberList +
                ", recordList='" + recordList + '\'' +
                '}';
    }

    public Long getRelatId() {
        return relatId;
    }

    public void setRelatId(Long relatId) {
        this.relatId = relatId;
    }

    public Long getKeepType() {
        return keepType;
    }

    public void setKeepType(Long keepType) {
        this.keepType = keepType;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public Long getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(Long costCenterId) {
        this.costCenterId = costCenterId;
    }

    public Long getEnergyId() {
        return energyId;
    }

    public void setEnergyId(Long energyId) {
        this.energyId = energyId;
    }

    public String getRelatCode() {
        return relatCode;
    }

    public void setRelatCode(String relatCode) {
        this.relatCode = relatCode;
    }

    public Long getFactoryFirmId() {
        return factoryFirmId;
    }

    public void setFactoryFirmId(Long factoryFirmId) {
        this.factoryFirmId = factoryFirmId;
    }

    public String getRelationExpression() {
        return relationExpression;
    }

    public void setRelationExpression(String relationExpression) {
        this.relationExpression = relationExpression;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public List<Integer> getNumberList() {
        return numberList;
    }

    public void setNumberList(List<Integer> numberList) {
        this.numberList = numberList;
    }

    public String getRecordList() {
        return recordList;
    }

    public void setRecordList(String recordList) {
        this.recordList = recordList;
    }

    public EmsMeaCenterRelatListPO() {
    }

    public EmsMeaCenterRelatListPO(Long relatId, Long keepType, String custName, Long costCenterId, Long energyId, String relatCode, Long factoryFirmId, String relationExpression, String description, String delFlag, List<Integer> numberList, String recordList) {
        this.relatId = relatId;
        this.keepType = keepType;
        this.custName = custName;
        this.costCenterId = costCenterId;
        this.energyId = energyId;
        this.relatCode = relatCode;
        this.factoryFirmId = factoryFirmId;
        this.relationExpression = relationExpression;
        this.description = description;
        this.delFlag = delFlag;
        this.numberList = numberList;
        this.recordList = recordList;
    }
}
