package com.ems.energy.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ems.common.core.domain.BaseEntity;

import java.util.ArrayList;
import java.util.List;
/**
 * @className: DataConstant
 * @description:
 * @author: RK
 * @date: 2021/12/29 9:25
 **/
public class EmsCostCenterVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 成本中心id */
    private Long centerId;

    /** 成本中心名称 */
    private String centerName;

    /** 祖级列表 */
    private String ancestors;
    /** 排序字段 */
    private Integer orderNum;
    /** 成本中心编码 */
    private String centerCode;

    /** 上一级成本中心id */
    private Long parentCenterId;

    /** 上一级成本中心名称*/
    private String parentCenterName;
    /** 系统类别(1附属系统,2生产系统,3辅助系统) */
    private String sysType;

    /** 启用状态(0禁用,1启用) */
    private String useStatus;

    /** 乐观锁(0删除,1正常) */
    private String delFlag;

    @TableField(exist = false)
    private List<EmsCostCenterVo> children = new ArrayList<EmsCostCenterVo>();

    @Override
    public String toString() {
        return "EmsCostCenterVo{" +
                "centerId=" + centerId +
                ", centerName='" + centerName + '\'' +
                ", ancestors='" + ancestors + '\'' +
                ", orderNum=" + orderNum +
                ", centerCode='" + centerCode + '\'' +
                ", parentCenterId=" + parentCenterId +
                ", parentCenterName='" + parentCenterName + '\'' +
                ", sysType='" + sysType + '\'' +
                ", useStatus='" + useStatus + '\'' +
                ", delFlag='" + delFlag + '\'' +
                ", children=" + children +
                '}';
    }

    public Long getCenterId() {
        return centerId;
    }

    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    public String getCenterName() {
        return centerName;
    }

    public void setCenterName(String centerName) {
        this.centerName = centerName;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getCenterCode() {
        return centerCode;
    }

    public void setCenterCode(String centerCode) {
        this.centerCode = centerCode;
    }

    public Long getParentCenterId() {
        return parentCenterId;
    }

    public void setParentCenterId(Long parentCenterId) {
        this.parentCenterId = parentCenterId;
    }

    public String getParentCenterName() {
        return parentCenterName;
    }

    public void setParentCenterName(String parentCenterName) {
        this.parentCenterName = parentCenterName;
    }

    public String getSysType() {
        return sysType;
    }

    public void setSysType(String sysType) {
        this.sysType = sysType;
    }

    public String getUseStatus() {
        return useStatus;
    }

    public void setUseStatus(String useStatus) {
        this.useStatus = useStatus;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public List<EmsCostCenterVo> getChildren() {
        return children;
    }

    public void setChildren(List<EmsCostCenterVo> children) {
        this.children = children;
    }

    public EmsCostCenterVo() {
    }

    public EmsCostCenterVo(Long centerId, String centerName, String ancestors, Integer orderNum, String centerCode, Long parentCenterId, String parentCenterName, String sysType, String useStatus, String delFlag, List<EmsCostCenterVo> children) {
        this.centerId = centerId;
        this.centerName = centerName;
        this.ancestors = ancestors;
        this.orderNum = orderNum;
        this.centerCode = centerCode;
        this.parentCenterId = parentCenterId;
        this.parentCenterName = parentCenterName;
        this.sysType = sysType;
        this.useStatus = useStatus;
        this.delFlag = delFlag;
        this.children = children;
    }
}
