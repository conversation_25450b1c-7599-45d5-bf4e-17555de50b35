package com.ems.energy.domain;

import com.ems.common.annotation.Excel;

/**
 * 日报对象 ems_daily
 *
 * <AUTHOR>
 * @date 2021-12-28
 */
public class EmsDaily
{
    private static final long serialVersionUID = 1L;

    /** 日报id */
    private Long dailyId;

    private String createTime;

    /** 产品代码 */
    @Excel(name = "产品代码")
    private String prodCode;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String prodName;

    /** 产量(件) */
    @Excel(name = "产量(件)")
    private Long outputPiece;

    /** 产量(模) */
    @Excel(name = "产量(模)")
    private Long outputModel;

    /** 产品总重量 */
    @Excel(name = "产品总重量")
    private Long allWeight;

    /** 生产时间(分钟) */
    @Excel(name = "生产时间(分钟)")
    private Long prodTime;

    /** 耗电量(kWh) */
    @Excel(name = "耗电量(kWh)")
    private Long powerCons;

    /** 分钟耗电量 */
    @Excel(name = "分钟耗电量")
    private Long minuteCons;

    /** 设备id */
    @Excel(name = "设备id")
    private Long deviceId;

    @Override
    public String toString() {
        return "EmsDaily{" +
                "dailyId=" + dailyId +
                ", createTime='" + createTime + '\'' +
                ", prodCode='" + prodCode + '\'' +
                ", prodName='" + prodName + '\'' +
                ", outputPiece=" + outputPiece +
                ", outputModel=" + outputModel +
                ", allWeight=" + allWeight +
                ", prodTime=" + prodTime +
                ", powerCons=" + powerCons +
                ", minuteCons=" + minuteCons +
                ", deviceId=" + deviceId +
                '}';
    }

    public Long getDailyId() {
        return dailyId;
    }

    public void setDailyId(Long dailyId) {
        this.dailyId = dailyId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getProdCode() {
        return prodCode;
    }

    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }

    public String getProdName() {
        return prodName;
    }

    public void setProdName(String prodName) {
        this.prodName = prodName;
    }

    public Long getOutputPiece() {
        return outputPiece;
    }

    public void setOutputPiece(Long outputPiece) {
        this.outputPiece = outputPiece;
    }

    public Long getOutputModel() {
        return outputModel;
    }

    public void setOutputModel(Long outputModel) {
        this.outputModel = outputModel;
    }

    public Long getAllWeight() {
        return allWeight;
    }

    public void setAllWeight(Long allWeight) {
        this.allWeight = allWeight;
    }

    public Long getProdTime() {
        return prodTime;
    }

    public void setProdTime(Long prodTime) {
        this.prodTime = prodTime;
    }

    public Long getPowerCons() {
        return powerCons;
    }

    public void setPowerCons(Long powerCons) {
        this.powerCons = powerCons;
    }

    public Long getMinuteCons() {
        return minuteCons;
    }

    public void setMinuteCons(Long minuteCons) {
        this.minuteCons = minuteCons;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public EmsDaily() {
    }

    public EmsDaily(Long dailyId, String createTime, String prodCode, String prodName, Long outputPiece, Long outputModel, Long allWeight, Long prodTime, Long powerCons, Long minuteCons, Long deviceId) {
        this.dailyId = dailyId;
        this.createTime = createTime;
        this.prodCode = prodCode;
        this.prodName = prodName;
        this.outputPiece = outputPiece;
        this.outputModel = outputModel;
        this.allWeight = allWeight;
        this.prodTime = prodTime;
        this.powerCons = powerCons;
        this.minuteCons = minuteCons;
        this.deviceId = deviceId;
    }


    public static long minuteCons(EmsDaily emsDaily) {

        return emsDaily.getMinuteCons();
    }
}
