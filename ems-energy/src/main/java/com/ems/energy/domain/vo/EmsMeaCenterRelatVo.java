package com.ems.energy.domain.vo;

import com.ems.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 计量器具和成本中心关系对象 ems_mea_center_relat
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public class EmsMeaCenterRelatVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 维护关系id */
    private Long relatId;

    /** 自定义名称 */
    private String custName;

    private String relatCode;

    /** 成本中心名称 */
    private String centerName;

    /** 部门名称 */
    private String deptName;

    private String meaCode;

    /** 能源名称 */
    private String energyName;

    /** 说明 */
    private String description;

    private String relationExpression;

    private Long keepType;
    private Long costCenterId;
    private Long energyId;

    private List<Integer> numberList;

    @Override
    public String toString() {
        return "EmsMeaCenterRelatVo{" +
                "relatId=" + relatId +
                ", custName='" + custName + '\'' +
                ", relatCode='" + relatCode + '\'' +
                ", centerName='" + centerName + '\'' +
                ", deptName='" + deptName + '\'' +
                ", meaCode='" + meaCode + '\'' +
                ", energyName='" + energyName + '\'' +
                ", description='" + description + '\'' +
                ", relationExpression='" + relationExpression + '\'' +
                ", keepType=" + keepType +
                ", costCenterId=" + costCenterId +
                ", energyId=" + energyId +
                ", numberList=" + numberList +
                '}';
    }

    public Long getRelatId() {
        return relatId;
    }

    public void setRelatId(Long relatId) {
        this.relatId = relatId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getRelatCode() {
        return relatCode;
    }

    public void setRelatCode(String relatCode) {
        this.relatCode = relatCode;
    }

    public String getCenterName() {
        return centerName;
    }

    public void setCenterName(String centerName) {
        this.centerName = centerName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getMeaCode() {
        return meaCode;
    }

    public void setMeaCode(String meaCode) {
        this.meaCode = meaCode;
    }

    public String getEnergyName() {
        return energyName;
    }

    public void setEnergyName(String energyName) {
        this.energyName = energyName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRelationExpression() {
        return relationExpression;
    }

    public void setRelationExpression(String relationExpression) {
        this.relationExpression = relationExpression;
    }

    public Long getKeepType() {
        return keepType;
    }

    public void setKeepType(Long keepType) {
        this.keepType = keepType;
    }

    public Long getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(Long costCenterId) {
        this.costCenterId = costCenterId;
    }

    public Long getEnergyId() {
        return energyId;
    }

    public void setEnergyId(Long energyId) {
        this.energyId = energyId;
    }

    public List<Integer> getNumberList() {
        return numberList;
    }

    public void setNumberList(List<Integer> numberList) {
        this.numberList = numberList;
    }

    public EmsMeaCenterRelatVo() {
    }

    public EmsMeaCenterRelatVo(Long relatId, String custName, String relatCode, String centerName, String deptName, String meaCode, String energyName, String description, String relationExpression, Long keepType, Long costCenterId, Long energyId, List<Integer> numberList) {
        this.relatId = relatId;
        this.custName = custName;
        this.relatCode = relatCode;
        this.centerName = centerName;
        this.deptName = deptName;
        this.meaCode = meaCode;
        this.energyName = energyName;
        this.description = description;
        this.relationExpression = relationExpression;
        this.keepType = keepType;
        this.costCenterId = costCenterId;
        this.energyId = energyId;
        this.numberList = numberList;
    }
}
