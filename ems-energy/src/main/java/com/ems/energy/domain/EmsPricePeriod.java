package com.ems.energy.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;

/**
 * 价格时段对象 ems_price_period
 *
 * <AUTHOR>
 * @date 2022-05-30
 */
public class EmsPricePeriod extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 时段ID */
    private Long periodId;

    /** 时段开始 */
    @Excel(name = "时段开始", width = 30)
    private Integer start;

    /** 时段结束 */
    @Excel(name = "时段结束", width = 30)
    private Integer end;

    /** 尖峰平谷分类 */
    @Excel(name = "尖峰平谷分类")
    private String category;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 价格配置ID */
    @Excel(name = "价格配置ID")
    private Long configId;

    public void setPeriodId(Long periodId)
    {
        this.periodId = periodId;
    }

    public Long getPeriodId()
    {
        return periodId;
    }
    public void setStart(Integer start)
    {
        this.start = start;
    }

    public Integer getStart()
    {
        return start;
    }
    public void setEnd(Integer end)
    {
        this.end = end;
    }

    public Integer getEnd()
    {
        return end;
    }
    public void setCategory(String category)
    {
        this.category = category;
    }

    public String getCategory()
    {
        return category;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setConfigId(Long configId)
    {
        this.configId = configId;
    }

    public Long getConfigId()
    {
        return configId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("periodId", getPeriodId())
            .append("start", getStart())
            .append("end", getEnd())
            .append("category", getCategory())
            .append("type", getType())
            .append("configId", getConfigId())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
