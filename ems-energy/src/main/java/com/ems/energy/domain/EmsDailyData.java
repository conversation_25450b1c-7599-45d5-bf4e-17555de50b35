package com.ems.energy.domain;

import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 日报对象 ems_daily_data
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
public class EmsDailyData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日报id */
    private Long dailyId;

    /** 产品代码 */
    @Excel(name = "产品代码")
    private String prodCode;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String prodName;

    /** 产量(件) */
    @Excel(name = "产量(件)")
    private Long outputPiece;

    /** 产量(模) */
    @Excel(name = "产量(模)")
    private Long outputModel;

    /** 产品总重量 */
    @Excel(name = "产品总重量")
    private Long allWeight;

    /** 生产时间(分钟) */
    @Excel(name = "生产时间(分钟)")
    private Long prodTime;

    /** 耗电量(kWh) */
    @Excel(name = "耗电量(kWh)")
    private Long powerCons;

    /** 分钟耗电量 */
    @Excel(name = "分钟耗电量")
    private Long minuteCons;

    /** 设备id */
    @Excel(name = "设备id")
    private Long deviceId;

    public void setDailyId(Long dailyId)
    {
        this.dailyId = dailyId;
    }

    public Long getDailyId()
    {
        return dailyId;
    }
    public void setProdCode(String prodCode)
    {
        this.prodCode = prodCode;
    }

    public String getProdCode()
    {
        return prodCode;
    }
    public void setProdName(String prodName)
    {
        this.prodName = prodName;
    }

    public String getProdName()
    {
        return prodName;
    }
    public void setOutputPiece(Long outputPiece)
    {
        this.outputPiece = outputPiece;
    }

    public Long getOutputPiece()
    {
        return outputPiece;
    }
    public void setOutputModel(Long outputModel)
    {
        this.outputModel = outputModel;
    }

    public Long getOutputModel()
    {
        return outputModel;
    }
    public void setAllWeight(Long allWeight)
    {
        this.allWeight = allWeight;
    }

    public Long getAllWeight()
    {
        return allWeight;
    }
    public void setProdTime(Long prodTime)
    {
        this.prodTime = prodTime;
    }

    public Long getProdTime()
    {
        return prodTime;
    }
    public void setPowerCons(Long powerCons)
    {
        this.powerCons = powerCons;
    }

    public Long getPowerCons()
    {
        return powerCons;
    }
    public void setMinuteCons(Long minuteCons)
    {
        this.minuteCons = minuteCons;
    }

    public Long getMinuteCons()
    {
        return minuteCons;
    }
    public void setDeviceId(Long deviceId)
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId()
    {
        return deviceId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dailyId", getDailyId())
            .append("createTime", getCreateTime())
            .append("prodCode", getProdCode())
            .append("prodName", getProdName())
            .append("outputPiece", getOutputPiece())
            .append("outputModel", getOutputModel())
            .append("allWeight", getAllWeight())
            .append("prodTime", getProdTime())
            .append("powerCons", getPowerCons())
            .append("minuteCons", getMinuteCons())
            .append("deviceId", getDeviceId())
            .toString();
    }
}
