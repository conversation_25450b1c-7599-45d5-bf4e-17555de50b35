package com.ems.energy.domain;

import com.ems.energy.domain.vo.EmsMeasureVo;

import java.util.List;

/**
 * @className: RecordPO
 * @description:
 * @author: RK
 * @date: 2021/12/24 18:12
 **/
public class RecordPO {
    private Long data;
    private String symbol;
    private String expression;
    private List<Integer> numberList;

    public RecordPO(EmsMeasureVo vo) {
        this.data = vo.getMeaId()==null? null : vo.getMeaId();
        this.symbol = vo.getMeaName() == null ? null : vo.getMeaName();
    }

    public Long getData() {
        return data;
    }

    public void setData(Long data) {
        this.data = data;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getExpression() {
        return expression;
    }

    public void setExpression(String expression) {
        this.expression = expression;
    }

    public List<Integer> getNumberList() {
        return numberList;
    }

    public void setNumberList(List<Integer> numberList) {
        this.numberList = numberList;
    }

    public RecordPO() {
    }

    public RecordPO(Long data, String symbol, String expression, List<Integer> numberList) {
        this.data = data;
        this.symbol = symbol;
        this.expression = expression;
        this.numberList = numberList;
    }
}
