package com.ems.energy.domain.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class EmsChartvotwo {
    private String name;

    private BigDecimal value;

    public EmsChartvotwo(){

    }

    @Override
    public String toString() {
        return "EmsChartvotwo{" +
                "name='" + name + '\'' +
                ", value=" + value +
                '}';
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public EmsChartvotwo(String name, BigDecimal value) {
        this.name = name;
        this.value = value;
    }
}
