package com.ems.energy.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;
import com.ems.energy.domain.EmsMeasure;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

/**
 * @className: EmsMeasureVo
 * @description:
 * @author: RK
 * @date: 2021/12/20 11:05
 **/
public class EmsMeasureVo  extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 计量器具id */
    private Long meaId;

    @Excel(name = "计量器具编号")
    private String meaCode;

    /** 计量对象 */
    @Excel(name = "计量对象")
    private String meaName;

    /** 祖级列表 */
    @Excel(name = "祖级列表")
    private String ancestors;

    /** 上一级计量对象 */
    @Excel(name = "上一级计量对象")
    private Long meaParentId;

    /** 型号规格 */
    @Excel(name = "型号规格")
    private String meaSpec;

    /** 准确度等级 */
    @Excel(name = "准确度等级")
    private String meaAccuracy;

    /** 检定周期/校准周期 */
    @Excel(name = "检定周期/校准周期")
    private String veriCycle;

    /** 检定单位 */
    @Excel(name = "检定单位")
    private String veriUnit;

    /** 检定日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检定日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date veriDate;

    /** 下次检定日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下次检定日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date nextVeriDate;

    /** 电流互感器变比 */
    @Excel(name = "电流互感器变比")
    private String curTranRatio;

    /** 串口地址 */
    @Excel(name = "串口地址")
    private String serialAddress;

    /** 网关地址号 */
    @Excel(name = "网关地址号")
    private String gatewayAddrNum;

    /** 网关内起始地址 */
    @Excel(name = "网关内起始地址")
    private String gatewayStartAddr;

    /** 能源介质 */
    private Long energyMed;

    @Excel(name = "能源介质名称")
    private String energyName;
    /** 能源成本中心 */
    private Long costCenterId;

    /** 能源成本中心名称 */
    @Excel(name = "能源成本中心")
    private String costCenterName;

    @Excel(name = "自定义名称")
    private String custName;

    /** 通讯方式(0无,1有) */
    @Excel(name = "通讯方式(0无,1有)")
    private String commMode;

    /** 生产厂家 */
    @Excel(name = "生产厂家")
    private String manuFact;

    /** 状态(0不合格,1合格) */
    @Excel(name = "状态(0不合格,1合格)")
    private String status;

    /** 证书编号 */
    @Excel(name = "证书编号")
    private Long certId;

    /** 检定结果 */
    @Excel(name = "检定结果")
    private String veriRes;

    /** 安装位置 */
    @Excel(name = "安装位置")
    private String installAddr;

    /** 电压互感器变比 */
    @Excel(name = "电压互感器变比")
    private String voltTranRatio;

    /** 串口回路号 */
    @Excel(name = "串口回路号")
    private String serialLoopNum;

    /** 网关端口号 */
    @Excel(name = "网关端口号")
    private String gatewayPort;

    /** 乐观锁(0删除,1正常) */
    @Excel(name = "乐观锁(0删除,1正常)")
    @TableLogic
    private String delFlag;

    @Excel(name = "关系")
    private String relationExpression;

    @Excel(name = "说明")
    private String description;

    @TableField(exist = false)
    private List<EmsMeasure> children;


    @Override
    public String toString() {
        return "EmsMeasureVo{" +
                "meaId=" + meaId +
                ", meaCode='" + meaCode + '\'' +
                ", meaName='" + meaName + '\'' +
                ", ancestors='" + ancestors + '\'' +
                ", meaParentId=" + meaParentId +
                ", meaSpec='" + meaSpec + '\'' +
                ", meaAccuracy='" + meaAccuracy + '\'' +
                ", veriCycle='" + veriCycle + '\'' +
                ", veriUnit='" + veriUnit + '\'' +
                ", veriDate=" + veriDate +
                ", nextVeriDate=" + nextVeriDate +
                ", curTranRatio='" + curTranRatio + '\'' +
                ", serialAddress='" + serialAddress + '\'' +
                ", gatewayAddrNum='" + gatewayAddrNum + '\'' +
                ", gatewayStartAddr='" + gatewayStartAddr + '\'' +
                ", energyMed=" + energyMed +
                ", energyName='" + energyName + '\'' +
                ", costCenterId=" + costCenterId +
                ", costCenterName='" + costCenterName + '\'' +
                ", custName='" + custName + '\'' +
                ", commMode='" + commMode + '\'' +
                ", manuFact='" + manuFact + '\'' +
                ", status='" + status + '\'' +
                ", certId=" + certId +
                ", veriRes='" + veriRes + '\'' +
                ", installAddr='" + installAddr + '\'' +
                ", voltTranRatio='" + voltTranRatio + '\'' +
                ", serialLoopNum='" + serialLoopNum + '\'' +
                ", gatewayPort='" + gatewayPort + '\'' +
                ", delFlag='" + delFlag + '\'' +
                ", relationExpression='" + relationExpression + '\'' +
                ", description='" + description + '\'' +
                ", children=" + children +
                '}';
    }

    public EmsMeasureVo() {
    }

    public EmsMeasureVo(Long meaId, String meaCode, String meaName, String ancestors, Long meaParentId,String meaSpec, String meaAccuracy, String veriCycle, String veriUnit, Date veriDate, Date nextVeriDate, String curTranRatio, String serialAddress, String gatewayAddrNum, String gatewayStartAddr, Long energyMed, String energyName, Long costCenterId, String costCenterName, String custName, String commMode, String manuFact, String status, Long certId, String veriRes, String installAddr, String voltTranRatio, String serialLoopNum, String gatewayPort, String delFlag, String relationExpression, String description, List<EmsMeasure> children) {
        this.meaId = meaId;
        this.meaCode = meaCode;
        this.meaName = meaName;
        this.ancestors = ancestors;
        this.meaParentId = meaParentId;
        this.meaSpec = meaSpec;
        this.meaAccuracy = meaAccuracy;
        this.veriCycle = veriCycle;
        this.veriUnit = veriUnit;
        this.veriDate = veriDate;
        this.nextVeriDate = nextVeriDate;
        this.curTranRatio = curTranRatio;
        this.serialAddress = serialAddress;
        this.gatewayAddrNum = gatewayAddrNum;
        this.gatewayStartAddr = gatewayStartAddr;
        this.energyMed = energyMed;
        this.energyName = energyName;
        this.costCenterId = costCenterId;
        this.costCenterName = costCenterName;
        this.custName = custName;
        this.commMode = commMode;
        this.manuFact = manuFact;
        this.status = status;
        this.certId = certId;
        this.veriRes = veriRes;
        this.installAddr = installAddr;
        this.voltTranRatio = voltTranRatio;
        this.serialLoopNum = serialLoopNum;
        this.gatewayPort = gatewayPort;
        this.delFlag = delFlag;
        this.relationExpression = relationExpression;
        this.description = description;
        this.children = children;
    }

    public Long getMeaId() {
        return meaId;
    }

    public void setMeaId(Long meaId) {
        this.meaId = meaId;
    }

    public String getMeaCode() {
        return meaCode;
    }

    public void setMeaCode(String meaCode) {
        this.meaCode = meaCode;
    }

    public String getMeaName() {
        return meaName;
    }

    public void setMeaName(String meaName) {
        this.meaName = meaName;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public Long getMeaParentId() {
        return meaParentId;
    }

    public void setMeaParentId(Long meaParentId) {
        this.meaParentId = meaParentId;
    }

    public String getMeaSpec() {
        return meaSpec;
    }

    public void setMeaSpec(String meaSpec) {
        this.meaSpec = meaSpec;
    }

    public String getMeaAccuracy() {
        return meaAccuracy;
    }

    public void setMeaAccuracy(String meaAccuracy) {
        this.meaAccuracy = meaAccuracy;
    }

    public String getVeriCycle() {
        return veriCycle;
    }

    public void setVeriCycle(String veriCycle) {
        this.veriCycle = veriCycle;
    }

    public String getVeriUnit() {
        return veriUnit;
    }

    public void setVeriUnit(String veriUnit) {
        this.veriUnit = veriUnit;
    }

    public Date getVeriDate() {
        return veriDate;
    }

    public void setVeriDate(Date veriDate) {
        this.veriDate = veriDate;
    }

    public Date getNextVeriDate() {
        return nextVeriDate;
    }

    public void setNextVeriDate(Date nextVeriDate) {
        this.nextVeriDate = nextVeriDate;
    }

    public String getCurTranRatio() {
        return curTranRatio;
    }

    public void setCurTranRatio(String curTranRatio) {
        this.curTranRatio = curTranRatio;
    }

    public String getSerialAddress() {
        return serialAddress;
    }

    public void setSerialAddress(String serialAddress) {
        this.serialAddress = serialAddress;
    }

    public String getGatewayAddrNum() {
        return gatewayAddrNum;
    }

    public void setGatewayAddrNum(String gatewayAddrNum) {
        this.gatewayAddrNum = gatewayAddrNum;
    }

    public String getGatewayStartAddr() {
        return gatewayStartAddr;
    }

    public void setGatewayStartAddr(String gatewayStartAddr) {
        this.gatewayStartAddr = gatewayStartAddr;
    }

    public Long getEnergyMed() {
        return energyMed;
    }

    public void setEnergyMed(Long energyMed) {
        this.energyMed = energyMed;
    }

    public String getEnergyName() {
        return energyName;
    }

    public void setEnergyName(String energyName) {
        this.energyName = energyName;
    }

    public Long getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(Long costCenterId) {
        this.costCenterId = costCenterId;
    }

    public String getCostCenterName() {
        return costCenterName;
    }

    public void setCostCenterName(String costCenterName) {
        this.costCenterName = costCenterName;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCommMode() {
        return commMode;
    }

    public void setCommMode(String commMode) {
        this.commMode = commMode;
    }

    public String getManuFact() {
        return manuFact;
    }

    public void setManuFact(String manuFact) {
        this.manuFact = manuFact;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getCertId() {
        return certId;
    }

    public void setCertId(Long certId) {
        this.certId = certId;
    }

    public String getVeriRes() {
        return veriRes;
    }

    public void setVeriRes(String veriRes) {
        this.veriRes = veriRes;
    }

    public String getInstallAddr() {
        return installAddr;
    }

    public void setInstallAddr(String installAddr) {
        this.installAddr = installAddr;
    }

    public String getVoltTranRatio() {
        return voltTranRatio;
    }

    public void setVoltTranRatio(String voltTranRatio) {
        this.voltTranRatio = voltTranRatio;
    }

    public String getSerialLoopNum() {
        return serialLoopNum;
    }

    public void setSerialLoopNum(String serialLoopNum) {
        this.serialLoopNum = serialLoopNum;
    }

    public String getGatewayPort() {
        return gatewayPort;
    }

    public void setGatewayPort(String gatewayPort) {
        this.gatewayPort = gatewayPort;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getRelationExpression() {
        return relationExpression;
    }

    public void setRelationExpression(String relationExpression) {
        this.relationExpression = relationExpression;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<EmsMeasure> getChildren() {
        return children;
    }

    public void setChildren(List<EmsMeasure> children) {
        this.children = children;
    }
}
