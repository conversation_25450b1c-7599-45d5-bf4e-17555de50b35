package com.ems.energy.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 计量器具和成本中心关系对象 ems_mea_center_relat
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public class EmsMeaCenterRelat extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 维护关系id */
    private Long relatId;

    /** 维护类别(1成本中心,2自定义) 有成本中心名称无自定义名称 */
    @Excel(name = "维护类别(1成本中心,2自定义) 有成本中心名称无自定义名称")
    private Long keepType;

    /** 自定义名称 */
    @Excel(name = "自定义名称")
    private String custName;

    /** 能源成本中心id(非空) */
    @Excel(name = "能源成本中心id(非空)")
    private Long costCenterId;

    /** 能源介质(非空) */
    @Excel(name = "能源介质(非空)")
    private Long energyId;

    /** 维护关系 */
    @Excel(name = "维护关系")
    private String relationExpression;

    /** 说明 */
    @Excel(name = "说明")
    private String description;

    /** 企业id*/
    private Long factoryFirmId;

    /** 乐观锁(0禁用,1启用) */
    @Excel(name = "乐观锁(0禁用,1启用)")
    @TableLogic
    private String delFlag;

    private String recordList;

    private List<Integer> numberList;

    public Long getRelatId() {
        return relatId;
    }

    public void setRelatId(Long relatId) {
        this.relatId = relatId;
    }

    public Long getKeepType() {
        return keepType;
    }

    public void setKeepType(Long keepType) {
        this.keepType = keepType;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public Long getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(Long costCenterId) {
        this.costCenterId = costCenterId;
    }

    public Long getEnergyId() {
        return energyId;
    }

    public void setEnergyId(Long energyId) {
        this.energyId = energyId;
    }

    public String getRelationExpression() {
        return relationExpression;
    }

    public void setRelationExpression(String relationExpression) {
        this.relationExpression = relationExpression;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getFactoryFirmId() {
        return factoryFirmId;
    }

    public void setFactoryFirmId(Long factoryFirmId) {
        this.factoryFirmId = factoryFirmId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getRecordList() {
        return recordList;
    }

    public void setRecordList(String recordList) {
        this.recordList = recordList;
    }

    public List<Integer> getNumberList() {
        return numberList;
    }

    public void setNumberList(List<Integer> numberList) {
        this.numberList = numberList;
    }

    @Override
    public String toString() {
        return "EmsMeaCenterRelat{" +
                "relatId=" + relatId +
                ", keepType=" + keepType +
                ", custName='" + custName + '\'' +
                ", costCenterId=" + costCenterId +
                ", energyId=" + energyId +
                ", relationExpression='" + relationExpression + '\'' +
                ", description='" + description + '\'' +
                ", factoryFirmId=" + factoryFirmId +
                ", delFlag='" + delFlag + '\'' +
                ", recordList='" + recordList + '\'' +
                ", numberList=" + numberList +
                '}';
    }

    public EmsMeaCenterRelat() {
    }

    public EmsMeaCenterRelat(Long relatId, Long keepType, String custName, Long costCenterId, Long energyId, String relationExpression, String description, Long factoryFirmId, String delFlag, String recordList, List<Integer> numberList) {
        this.relatId = relatId;
        this.keepType = keepType;
        this.custName = custName;
        this.costCenterId = costCenterId;
        this.energyId = energyId;
        this.relationExpression = relationExpression;
        this.description = description;
        this.factoryFirmId = factoryFirmId;
        this.delFlag = delFlag;
        this.recordList = recordList;
        this.numberList = numberList;
    }
}
