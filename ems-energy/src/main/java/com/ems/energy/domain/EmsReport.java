package com.ems.energy.domain;

import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 能耗报对象 ems_report
 *
 * <AUTHOR>
 * @date 2021-12-21
 */
public class EmsReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 报表id */
    private Long reportId;

    /** 维护关系id */
    @Excel(name = "维护关系id")
    private Long relatId;

    /** 收录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收录时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dockTime;

    /** 设备状态(0,正常;1,停用) */
    @Excel(name = "设备状态(0,正常;1,停用)")
    private String deviceStatus;

    /** 机器传送的数据 */
    @Excel(name = "机器传送的数据")
    private String dataJson;

    /** 用时(单位:H) */
    @Excel(name = "用时(单位:H)")
    private Long runTime;

    /** 耗能(单位:Kwh) */
    @Excel(name = "耗能(单位:Kwh)")
    private Long powerWasting;

    public void setReportId(Long reportId)
    {
        this.reportId = reportId;
    }

    public Long getReportId()
    {
        return reportId;
    }
    public void setRelatId(Long relatId)
    {
        this.relatId = relatId;
    }

    public Long getRelatId()
    {
        return relatId;
    }
    public void setDockTime(Date dockTime)
    {
        this.dockTime = dockTime;
    }

    public Date getDockTime()
    {
        return dockTime;
    }
    public void setDeviceStatus(String deviceStatus)
    {
        this.deviceStatus = deviceStatus;
    }

    public String getDeviceStatus()
    {
        return deviceStatus;
    }
    public void setDataJson(String dataJson)
    {
        this.dataJson = dataJson;
    }

    public String getDataJson()
    {
        return dataJson;
    }
    public void setRunTime(Long runTime)
    {
        this.runTime = runTime;
    }

    public Long getRunTime()
    {
        return runTime;
    }
    public void setPowerWasting(Long powerWasting)
    {
        this.powerWasting = powerWasting;
    }

    public Long getPowerWasting()
    {
        return powerWasting;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("reportId", getReportId())
            .append("relatId", getRelatId())
            .append("dockTime", getDockTime())
            .append("deviceStatus", getDeviceStatus())
            .append("dataJson", getDataJson())
            .append("runTime", getRunTime())
            .append("powerWasting", getPowerWasting())
            .toString();
    }
}
