package com.ems.energy.domain;

import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 产品对象 ems_product
 *
 * <AUTHOR>
 * @date 2022-01-18
 */
public class EmsProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 产品id */
    private Long proId;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String proName;

    /** ERP号码 */
    @Excel(name = "ERP号码")
    private String  erpId;

    /** 当量 */
    @Excel(name = "当量")
    private Long equiva;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 产品代码(冗余字段) */
    @Excel(name = "产品代码(冗余字段)")
    private String proCode;

    public Long getProId() {
        return proId;
    }

    public void setProId(Long proId) {
        this.proId = proId;
    }

    public String getProName() {
        return proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    public String getErpId() {
        return erpId;
    }

    public void setErpId(String erpId) {
        this.erpId = erpId;
    }

    public Long getEquiva() {
        return equiva;
    }

    public void setEquiva(Long equiva) {
        this.equiva = equiva;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getProCode() {
        return proCode;
    }

    public void setProCode(String proCode) {
        this.proCode = proCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("proId", getProId())
            .append("proName", getProName())
            .append("erpId", getErpId())
            .append("equiva", getEquiva())
            .append("unit", getUnit())
            .append("proCode", getProCode())
            .toString();
    }
}
