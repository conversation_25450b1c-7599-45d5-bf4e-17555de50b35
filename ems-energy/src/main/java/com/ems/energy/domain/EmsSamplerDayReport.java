package com.ems.energy.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;

/**
 * 日数据报对象 ems_sampler_day_report
 *
 * <AUTHOR>
 * @date 2022-06-01
 */
public class EmsSamplerDayReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据ID */
    private Long reportId;

    /** 数值 */
    @Excel(name = "数值")
    private BigDecimal value;

    /** 分类 */
    @Excel(name = "分类")
    private String category;

    /** 采集器 */
    @Excel(name = "采集器")
    private String sampler;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reportDate;

    public void setReportId(Long reportId)
    {
        this.reportId = reportId;
    }

    public Long getReportId()
    {
        return reportId;
    }
    public void setValue(BigDecimal value)
    {
        this.value = value;
    }

    public BigDecimal getValue()
    {
        return value;
    }
    public void setCategory(String category)
    {
        this.category = category;
    }

    public String getCategory()
    {
        return category;
    }
    public void setSampler(String sampler)
    {
        this.sampler = sampler;
    }

    public String getSampler()
    {
        return sampler;
    }
    public void setReportDate(Date reportDate)
    {
        this.reportDate = reportDate;
    }

    public Date getReportDate()
    {
        return reportDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("reportId", getReportId())
            .append("value", getValue())
            .append("category", getCategory())
            .append("sampler", getSampler())
            .append("reportDate", getReportDate())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
