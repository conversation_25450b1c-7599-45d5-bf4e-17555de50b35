package com.ems.energy.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.TreeEntity;

/**
 * 设备管理对象 ems_equipment
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
public class EmsEquipment extends TreeEntity
{
    private static final long serialVersionUID = 1L;

    /** 设备ID */
    private Long equipmentId;

    /** 设备编号 */
    @Excel(name = "设备编号")
    private String equipmentCode;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String equipmentName;

    /** 设备型号 */
    @Excel(name = "设备型号")
    private String model;

    /** 设备品牌 */
    @Excel(name = "设备品牌")
    private String brand;

    /** 维护时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "维护时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date maintenanceDate;

    /** 维护周期 */
    @Excel(name = "维护周期")
    private String maintenacePeriod;

    /** 设备类型 */
    @Excel(name = "设备类型")
    private String equipmentType;

    /** 成本中心 */
    private Long costCenterId;

    /** 标签 */
    @Excel(name = "标签")
    private String tag;

    /** 业务标签 */
    @Excel(name = "业务标签")
    private String businessTag;

    /** 表具配置 */
    private String measuringToolConfig;

    public void setEquipmentId(Long equipmentId)
    {
        this.equipmentId = equipmentId;
    }

    public Long getEquipmentId()
    {
        return equipmentId;
    }
    public void setEquipmentCode(String equipmentCode)
    {
        this.equipmentCode = equipmentCode;
    }

    public String getEquipmentCode()
    {
        return equipmentCode;
    }
    public void setEquipmentName(String equipmentName)
    {
        this.equipmentName = equipmentName;
    }

    public String getEquipmentName()
    {
        return equipmentName;
    }
    public void setModel(String model)
    {
        this.model = model;
    }

    public String getModel()
    {
        return model;
    }
    public void setBrand(String brand)
    {
        this.brand = brand;
    }

    public String getBrand()
    {
        return brand;
    }
    public void setMaintenanceDate(Date maintenanceDate)
    {
        this.maintenanceDate = maintenanceDate;
    }

    public Date getMaintenanceDate()
    {
        return maintenanceDate;
    }
    public void setMaintenacePeriod(String maintenacePeriod)
    {
        this.maintenacePeriod = maintenacePeriod;
    }

    public String getMaintenacePeriod()
    {
        return maintenacePeriod;
    }
    public void setEquipmentType(String equipmentType)
    {
        this.equipmentType = equipmentType;
    }

    public String getEquipmentType()
    {
        return equipmentType;
    }
    public void setCostCenterId(Long costCenterId)
    {
        this.costCenterId = costCenterId;
    }

    public Long getCostCenterId()
    {
        return costCenterId;
    }
    public void setTag(String tag)
    {
        this.tag = tag;
    }

    public String getTag()
    {
        return tag;
    }
    public void setMeasuringToolConfig(String measuringToolConfig)
    {
        this.measuringToolConfig = measuringToolConfig;
    }

    public String getMeasuringToolConfig()
    {
        return measuringToolConfig;
    }

    public String getBusinessTag() {
        return businessTag;
    }

    public void setBusinessTag(String businessTag) {
        this.businessTag = businessTag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("equipmentId", getEquipmentId())
            .append("equipmentCode", getEquipmentCode())
            .append("equipmentName", getEquipmentName())
            .append("model", getModel())
            .append("brand", getBrand())
            .append("maintenanceDate", getMaintenanceDate())
            .append("maintenacePeriod", getMaintenacePeriod())
            .append("equipmentType", getEquipmentType())
            .append("parentId", getParentId())
            .append("costCenterId", getCostCenterId())
            .append("tag", getTag())
            .append("businessTag", getBusinessTag())
            .append("measuringToolConfig", getMeasuringToolConfig())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
