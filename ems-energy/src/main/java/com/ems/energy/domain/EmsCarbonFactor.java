package com.ems.energy.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;

/**
 * 碳排放因子设置对象 ems_carbon_factor
 *
 * <AUTHOR>
 * @date 2022-06-02
 */
public class EmsCarbonFactor extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 碳排放因子ID */
    private Long factorId;

    /** 碳排放因子 */
    @Excel(name = "碳排放因子")
    private Float carbonFactor;

    private String energyCategory;

    private String unit;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validFrom;

    public String getEnergyCategory() {
        return energyCategory;
    }

    public void setEnergyCategory(String energyCategory) {
        this.energyCategory = energyCategory;
    }

    public void setFactorId(Long factorId)
    {
        this.factorId = factorId;
    }

    public Long getFactorId()
    {
        return factorId;
    }
    public void setCarbonFactor(Float carbonFactor)
    {
        this.carbonFactor = carbonFactor;
    }

    public Float getCarbonFactor()
    {
        return carbonFactor;
    }
    public void setValidFrom(Date validFrom)
    {
        this.validFrom = validFrom;
    }

    public Date getValidFrom()
    {
        return validFrom;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("factorId", getFactorId())
            .append("carbonFactor", getCarbonFactor())
            .append("validFrom", getValidFrom())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
