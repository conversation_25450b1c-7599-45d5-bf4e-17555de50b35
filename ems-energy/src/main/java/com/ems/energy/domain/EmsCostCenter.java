package com.ems.energy.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 成本中心对象 ems_cost_center
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public class EmsCostCenter extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 成本中心id */
    private Long centerId;

    /** 成本中心名称 */
    @Excel(name = "成本中心名称")
    private String centerName;

    /** 祖级列表 */
    @Excel(name = "祖级列表")
    private String ancestors;
    /** 排序字段 */
    private Integer orderNum;
    /** 成本中心编码 */
    @Excel(name = "成本中心编码")
    private String centerCode;

    /** 上一级成本中心id */
    @Excel(name = "上一级成本中心id")
    private Long parentCenterId;

    /** 系统类别(1附属系统,2生产系统,3辅助系统) */
    @Excel(name = "系统类别(1附属系统,2生产系统,3辅助系统)")
    @NotNull(message = "系统类别不能为空")
    private String sysType;

    /** 启用状态(0禁用,1启用) */
    @Excel(name = "启用状态(0禁用,1启用)")
    private String useStatus;

    private String subName;

    /** 乐观锁(0删除,1正常) */
    @Excel(name = "乐观锁(0删除,1正常)")
    private String delFlag;

    @TableField(exist = false)
    private List<EmsCostCenter> children = new ArrayList<EmsCostCenter>();

    @Override
    public String toString() {
        return "EmsCostCenter{" +
                "centerId=" + centerId +
                ", centerName='" + centerName + '\'' +
                ", ancestors='" + ancestors + '\'' +
                ", orderNum=" + orderNum +
                ", centerCode='" + centerCode + '\'' +
                ", parentCenterId=" + parentCenterId +
                ", sysType='" + sysType + '\'' +
                ", useStatus='" + useStatus + '\'' +
                ", subName='" + subName + '\'' +
                ", delFlag='" + delFlag + '\'' +
                ", children=" + children +
                '}';
    }

    public Long getCenterId() {
        return centerId;
    }

    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    public String getCenterName() {
        return centerName;
    }

    public void setCenterName(String centerName) {
        this.centerName = centerName;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getCenterCode() {
        return centerCode;
    }

    public void setCenterCode(String centerCode) {
        this.centerCode = centerCode;
    }

    public Long getParentCenterId() {
        return parentCenterId;
    }

    public void setParentCenterId(Long parentCenterId) {
        this.parentCenterId = parentCenterId;
    }

    public String getSysType() {
        return sysType;
    }

    public void setSysType(String sysType) {
        this.sysType = sysType;
    }

    public String getUseStatus() {
        return useStatus;
    }

    public void setUseStatus(String useStatus) {
        this.useStatus = useStatus;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public List<EmsCostCenter> getChildren() {
        return children;
    }

    public void setChildren(List<EmsCostCenter> children) {
        this.children = children;
    }

    public EmsCostCenter() {
    }

    public EmsCostCenter(Long centerId, String centerName, String ancestors, Integer orderNum, String centerCode, Long parentCenterId, @NotNull(message = "系统类别不能为空") String sysType, String useStatus, String subName, String delFlag, List<EmsCostCenter> children) {
        this.centerId = centerId;
        this.centerName = centerName;
        this.ancestors = ancestors;
        this.orderNum = orderNum;
        this.centerCode = centerCode;
        this.parentCenterId = parentCenterId;
        this.sysType = sysType;
        this.useStatus = useStatus;
        this.subName = subName;
        this.delFlag = delFlag;
        this.children = children;
    }
}
