package com.ems.energy.domain.tree;

import com.ems.energy.domain.vo.EmsCostCenterVo;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 */
public class EmsCenterVoTree implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 成本中心Id */
    private Long id;

    /** 成本中心名称 */
    private String label;

    /** 成本中心编码*/
    private String code;

    /** 上一级成本中心ID */
    private Long parentCenterId;

    /** 上一级成本中心名称 */
    private String parentCenterName;

    /** 系统类别(1附属系统,2生产系统,3辅助系统) */
    private String sysType;

    /** 启用状态(0禁用,1启用) */
    private String useStatus;

    /** 乐观锁(0删除,1正常) */
    private String delFlag;
    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<EmsCenterVoTree> children;

    public EmsCenterVoTree()
    {

    }

    public EmsCenterVoTree(EmsCostCenterVo emsCostCenterVo) {
        this.id = emsCostCenterVo.getCenterId();
        this.label = emsCostCenterVo.getCenterName();
        this.code = emsCostCenterVo.getCenterCode();
        this.parentCenterId = emsCostCenterVo.getParentCenterId();
        this.parentCenterName = emsCostCenterVo.getParentCenterName();
        this.sysType = emsCostCenterVo.getSysType();
        this.delFlag = emsCostCenterVo.getDelFlag();
        this.useStatus = emsCostCenterVo.getUseStatus();
        this.children = emsCostCenterVo.getChildren().stream().map(EmsCenterVoTree::new).collect(Collectors.toList());
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getParentCenterId() {
        return parentCenterId;
    }

    public void setParentCenterId(Long parentCenterId) {
        this.parentCenterId = parentCenterId;
    }

    public String getParentCenterName() {
        return parentCenterName;
    }

    public void setParentCenterName(String parentCenterName) {
        this.parentCenterName = parentCenterName;
    }

    public String getSysType() {
        return sysType;
    }

    public void setSysType(String sysType) {
        this.sysType = sysType;
    }

    public String getUseStatus() {
        return useStatus;
    }

    public void setUseStatus(String useStatus) {
        this.useStatus = useStatus;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public List<EmsCenterVoTree> getChildren() {
        return children;
    }

    public void setChildren(List<EmsCenterVoTree> children) {
        this.children = children;
    }
}
