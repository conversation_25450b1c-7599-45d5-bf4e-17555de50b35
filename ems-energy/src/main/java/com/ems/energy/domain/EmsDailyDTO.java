package com.ems.energy.domain;

/**
 * @className: EmsDailyDTO
 * @description:
 * @author: RK
 * @date: 2021/12/29 13:33
 **/
public class EmsDailyDTO {
    /** 日报id */
    private Long dailyId;

    private String createTime;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 产品代码 */
    private String prodCode;

    /** 产品名称 */
    private String prodName;

    /** 产量(件) */
    private Long outputPiece;

    /** 产量(模) */
    private Long outputModel;

    /** 产品总重量 */
    private Long allWeight;

    /** 生产时间(分钟) */
    private Long prodTime;

    /** 耗电量(kWh) */
    private Long powerCons;

    /** 分钟耗电量 */
    private Long minuteCons;

    /** 设备id */
    private Long deviceId;

    private String relatCode;

    @Override
    public String toString() {
        return "EmsDailyDTO{" +
                "dailyId=" + dailyId +
                ", createTime='" + createTime + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", prodCode='" + prodCode + '\'' +
                ", prodName='" + prodName + '\'' +
                ", outputPiece=" + outputPiece +
                ", outputModel=" + outputModel +
                ", allWeight=" + allWeight +
                ", prodTime=" + prodTime +
                ", powerCons=" + powerCons +
                ", minuteCons=" + minuteCons +
                ", deviceId=" + deviceId +
                ", relatCode='" + relatCode + '\'' +
                '}';
    }

    public Long getDailyId() {
        return dailyId;
    }

    public void setDailyId(Long dailyId) {
        this.dailyId = dailyId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getProdCode() {
        return prodCode;
    }

    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }

    public String getProdName() {
        return prodName;
    }

    public void setProdName(String prodName) {
        this.prodName = prodName;
    }

    public Long getOutputPiece() {
        return outputPiece;
    }

    public void setOutputPiece(Long outputPiece) {
        this.outputPiece = outputPiece;
    }

    public Long getOutputModel() {
        return outputModel;
    }

    public void setOutputModel(Long outputModel) {
        this.outputModel = outputModel;
    }

    public Long getAllWeight() {
        return allWeight;
    }

    public void setAllWeight(Long allWeight) {
        this.allWeight = allWeight;
    }

    public Long getProdTime() {
        return prodTime;
    }

    public void setProdTime(Long prodTime) {
        this.prodTime = prodTime;
    }

    public Long getPowerCons() {
        return powerCons;
    }

    public void setPowerCons(Long powerCons) {
        this.powerCons = powerCons;
    }

    public Long getMinuteCons() {
        return minuteCons;
    }

    public void setMinuteCons(Long minuteCons) {
        this.minuteCons = minuteCons;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public String getRelatCode() {
        return relatCode;
    }

    public void setRelatCode(String relatCode) {
        this.relatCode = relatCode;
    }

    public EmsDailyDTO() {
    }

    public EmsDailyDTO(Long dailyId, String createTime, String startTime, String endTime, String prodCode, String prodName, Long outputPiece, Long outputModel, Long allWeight, Long prodTime, Long powerCons, Long minuteCons, Long deviceId, String relatCode) {
        this.dailyId = dailyId;
        this.createTime = createTime;
        this.startTime = startTime;
        this.endTime = endTime;
        this.prodCode = prodCode;
        this.prodName = prodName;
        this.outputPiece = outputPiece;
        this.outputModel = outputModel;
        this.allWeight = allWeight;
        this.prodTime = prodTime;
        this.powerCons = powerCons;
        this.minuteCons = minuteCons;
        this.deviceId = deviceId;
        this.relatCode = relatCode;
    }
}
