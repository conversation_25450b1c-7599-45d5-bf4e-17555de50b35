package com.ems.energy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.TreeEntity;

/**
 * 产品管理对象 ems_part
 *
 * <AUTHOR>
 * @date 2022-03-07
 */
public class EmsPart extends TreeEntity
{
    private static final long serialVersionUID = 1L;

    /** 产品ID */
    private Long partId;

    /** 产线零件号 */
    @Excel(name = "产线零件号")
    private String partNo;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String partName;

    /** 特征码 */
    @Excel(name = "特征码")
    private String pattern;

    /** 标签 */
    @Excel(name = "标签")
    private String tag;

    @Excel(name = "用量")
    private Float useCount;

    @Excel(name = "当量")
    private Float equivalent;

    @Excel(name = "加工道序")
    private Float processCount;

    public Float getUseCount() {
        return useCount;
    }

    public void setUseCount(Float useCount) {
        this.useCount = useCount;
    }

    public Float getEquivalent() {
        return equivalent;
    }

    public void setEquivalent(Float equivalent) {
        this.equivalent = equivalent;
    }

    public Float getProcessCount() {
        return processCount;
    }

    public void setProcessCount(Float processCount) {
        this.processCount = processCount;
    }

    public void setPartId(Long partId)
    {
        this.partId = partId;
    }

    public Long getPartId()
    {
        return partId;
    }
    public void setPartNo(String partNo)
    {
        this.partNo = partNo;
    }

    public String getPartNo()
    {
        return partNo;
    }
    public void setPartName(String partName)
    {
        this.partName = partName;
    }

    public String getPartName()
    {
        return partName;
    }
    public void setPattern(String pattern)
    {
        this.pattern = pattern;
    }

    public String getPattern()
    {
        return pattern;
    }
    public void setTag(String tag)
    {
        this.tag = tag;
    }

    public String getTag()
    {
        return tag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("partId", getPartId())
            .append("partNo", getPartNo())
            .append("partName", getPartName())
            .append("pattern", getPattern())
            .append("parentId", getParentId())
            .append("tag", getTag())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
