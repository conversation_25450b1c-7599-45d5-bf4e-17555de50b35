package com.ems.energy.domain.vo;

import com.ems.common.annotation.Excel;

/**
 * @className: EmsDeviceVo
 * @description:
 * @author: RK
 * @date: 2022/1/19 19:17
 **/
public class EmsDeviceVo {
    /** 工厂设备id */
    private Long equId;

    /** 设备号 */
    @Excel(name = "设备号")
    private String deviceNum;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 计量器具(一对一的,设备对应一个电表) */
    @Excel(name = "计量器具(一对一的,设备对应一个电表)")
    private Long meaId;

    @Excel(name = "计量器具名称")
    private String meaCode;

    /** 能源介质 */
    private Long energyId;
    /** 能源介质名称*/
    @Excel(name = "能源介质")
    private String energyName;

    @Override
    public String toString() {
        return "EmsDeviceVo{" +
                "equId=" + equId +
                ", deviceNum='" + deviceNum + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", meaId=" + meaId +
                ", meaCode='" + meaCode + '\'' +
                ", energyId=" + energyId +
                ", energyName='" + energyName + '\'' +
                '}';
    }

    public Long getEquId() {
        return equId;
    }

    public void setEquId(Long equId) {
        this.equId = equId;
    }

    public String getDeviceNum() {
        return deviceNum;
    }

    public void setDeviceNum(String deviceNum) {
        this.deviceNum = deviceNum;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public Long getMeaId() {
        return meaId;
    }

    public void setMeaId(Long meaId) {
        this.meaId = meaId;
    }

    public String getMeaCode() {
        return meaCode;
    }

    public void setMeaCode(String meaCode) {
        this.meaCode = meaCode;
    }

    public Long getEnergyId() {
        return energyId;
    }

    public void setEnergyId(Long energyId) {
        this.energyId = energyId;
    }

    public String getEnergyName() {
        return energyName;
    }

    public void setEnergyName(String energyName) {
        this.energyName = energyName;
    }

    public EmsDeviceVo() {
    }

    public EmsDeviceVo(Long equId, String deviceNum, String deviceName, Long meaId, String meaCode, Long energyId, String energyName) {
        this.equId = equId;
        this.deviceNum = deviceNum;
        this.deviceName = deviceName;
        this.meaId = meaId;
        this.meaCode = meaCode;
        this.energyId = energyId;
        this.energyName = energyName;
    }
}
