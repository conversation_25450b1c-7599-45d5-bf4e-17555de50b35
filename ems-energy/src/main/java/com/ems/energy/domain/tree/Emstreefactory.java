package com.ems.energy.domain.tree;

import com.ems.common.core.domain.entity.SysMenu;
import com.ems.energy.domain.EmsFactory;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
/**
 * @className: DataConstant
 * @description:
 * @author: RK
 * @date: 2021/12/29 9:25
 **/
public class Emstreefactory implements Serializable
{

    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Emstreefactory> children;

    public Emstreefactory()
    {

    }

    public Emstreefactory(EmsFactory emsFactory)
    {
        this.id = emsFactory.getDeptId();
        this.label = emsFactory.getDeptName();
        this.children = emsFactory.getChildren().stream().map(Emstreefactory::new).collect(Collectors.toList());
    }

    public Emstreefactory(SysMenu menu)
    {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        this.children = menu.getChildren().stream().map(Emstreefactory::new).collect(Collectors.toList());
    }




    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public List<Emstreefactory> getChildren()
    {
        return children;
    }

    public void setChildren(List<Emstreefactory> children)
    {
        this.children = children;
    }


}
