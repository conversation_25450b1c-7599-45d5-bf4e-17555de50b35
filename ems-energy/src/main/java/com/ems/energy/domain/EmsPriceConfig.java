package com.ems.energy.domain;

import java.math.BigDecimal;
import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;

/**
 * 电价配置对象 ems_price_config
 *
 * <AUTHOR>
 * @date 2022-05-30
 */
public class EmsPriceConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long configId;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validFrom;

    /** 价格时段信息 */
    private List<EmsPricePeriod> emsPricePeriodList;

    /** 价格信息 */
    private List<EmsPrice> emsPriceList;

    public void setConfigId(Long configId)
    {
        this.configId = configId;
    }

    public Long getConfigId()
    {
        return configId;
    }
    public void setValidFrom(Date validFrom)
    {
        this.validFrom = validFrom;
    }

    public Date getValidFrom()
    {
        return validFrom;
    }

    public List<EmsPricePeriod> getEmsPricePeriodList()
    {
        return emsPricePeriodList;
    }

    public void setEmsPricePeriodList(List<EmsPricePeriod> emsPricePeriodList)
    {
        this.emsPricePeriodList = emsPricePeriodList;
    }

    public List<EmsPrice> getEmsPriceList()
    {
        return emsPriceList;
    }

    public void setEmsPriceList(List<EmsPrice> emsPriceList)
    {
        this.emsPriceList = emsPriceList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("configId", getConfigId())
            .append("validFrom", getValidFrom())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("emsPricePeriodList", getEmsPricePeriodList())
            .toString();
    }
}
