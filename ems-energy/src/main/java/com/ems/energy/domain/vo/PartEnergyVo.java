package com.ems.energy.domain.vo;

import com.ems.common.utils.bean.BeanUtils;
import com.ems.energy.domain.EmsPart;

import java.math.BigDecimal;

public class PartEnergyVo extends EmsPart {
    private BigDecimal selfEnergy;
    private BigDecimal totalEnergy;

    private BigDecimal totalCount;

    private BigDecimal carbonFactor;

    private String carbonFactorUnit;

    public PartEnergyVo(){}

    public PartEnergyVo(EmsPart part){
        BeanUtils.copyProperties(part, this);
    }

    public BigDecimal getSelfEnergy() {
        return selfEnergy;
    }

    public void setSelfEnergy(BigDecimal selfEnergy) {
        this.selfEnergy = selfEnergy;
    }

    public BigDecimal getTotalEnergy() {
        return totalEnergy;
    }

    public void setTotalEnergy(BigDecimal totalEnergy) {
        this.totalEnergy = totalEnergy;
    }

    public BigDecimal getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getCarbonFactor() {
        return carbonFactor;
    }

    public void setCarbonFactor(BigDecimal carbonFactor) {
        this.carbonFactor = carbonFactor;
    }

    public String getCarbonFactorUnit() {
        return carbonFactorUnit;
    }

    public void setCarbonFactorUnit(String carbonFactorUnit) {
        this.carbonFactorUnit = carbonFactorUnit;
    }
}
