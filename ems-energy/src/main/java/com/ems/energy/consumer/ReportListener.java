package com.ems.energy.consumer;

import com.ems.energy.constant.EnergyConstant;
//import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
//import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * @className: ReportConsumer
 * @description:
 * @author: RK
 * @date: 2021/12/24 13:26
 **/
@Component
public class ReportListener {
    private final static Logger log = LoggerFactory.getLogger(ReportListener.class);

//    @KafkaListener(topics = EnergyConstant.KAFKA_TOPIC_NAME)
//    public void listenConsumerGroup(ConsumerRecord<String,String>record){
//        String value = record.value();
//        log.info("消息队列接收到了消息:{}",value);
//    }
}
