package com.ems.energy.service.impl;

import com.ems.energy.domain.EmsProduct;
import com.ems.energy.mapper.EmsProductMapper;
import com.ems.energy.service.IEmsProductService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-18
 */
@Service
public class EmsProductServiceImpl implements IEmsProductService
{
    @Resource
    private EmsProductMapper emsProductMapper;

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 查询产品
     *
     * @param proId 产品主键
     * @return 产品
     */
    @Override
    public EmsProduct selectEmsProductByProId(Long proId)
    {
        return emsProductMapper.selectEmsProductByProId(proId);
    }

    /**
     * 查询产品列表
     *
     * @param emsProduct 产品
     * @return 产品
     */
    @Override
    public List<EmsProduct> selectEmsProductList(EmsProduct emsProduct)
    {
        return emsProductMapper.selectEmsProductList(emsProduct);
    }

    /**
     * 新增产品
     *
     * @param emsProduct 产品
     * @return 结果
     */
    @Override
    public int insertEmsProduct(EmsProduct emsProduct)
    {
        return emsProductMapper.insertEmsProduct(emsProduct);
    }

    /**
     * 修改产品
     *
     * @param emsProduct 产品
     * @return 结果
     */
    @Override
    public int updateEmsProduct(EmsProduct emsProduct)
    {
        return emsProductMapper.updateEmsProduct(emsProduct);
    }

    /**
     * 批量删除产品
     *
     * @param proIds 需要删除的产品主键
     * @return 结果
     */
    @Override
    public int deleteEmsProductByProIds(Long[] proIds)
    {
        return emsProductMapper.deleteEmsProductByProIds(proIds);
    }

    /**
     * 删除产品信息
     *
     * @param proId 产品主键
     * @return 结果
     */
    @Override
    public int deleteEmsProductByProId(Long proId)
    {
        return emsProductMapper.deleteEmsProductByProId(proId);
    }
}
