package com.ems.energy.service;

import com.ems.energy.domain.EmsReport;

import java.util.List;


/**
 * 能耗报Service接口
 *
 * <AUTHOR>
 * @date 2021-12-21
 */
public interface IEmsReportService
{
    /**
     * 查询能耗报
     *
     * @param reportId 能耗报主键
     * @return 能耗报
     */
    EmsReport selectEmsReportByReportId(Long reportId);

    /**
     * 查询能耗报列表
     *
     * @param emsReport 能耗报
     * @return 能耗报集合
     */
    List<EmsReport> selectEmsReportList(EmsReport emsReport);

    /**
     * 新增能耗报
     *
     * @param emsReport 能耗报
     * @return 结果
     */
    int insertEmsReport(EmsReport emsReport);

    /**
     * 修改能耗报
     *
     * @param emsReport 能耗报
     * @return 结果
     */
    int updateEmsReport(EmsReport emsReport);

    /**
     * 批量删除能耗报
     *
     * @param reportIds 需要删除的能耗报主键集合
     * @return 结果
     */
    int deleteEmsReportByReportIds(Long[] reportIds);

    /**
     * 删除能耗报信息
     *
     * @param reportId 能耗报主键
     * @return 结果
     */
    int deleteEmsReportByReportId(Long reportId);
}
