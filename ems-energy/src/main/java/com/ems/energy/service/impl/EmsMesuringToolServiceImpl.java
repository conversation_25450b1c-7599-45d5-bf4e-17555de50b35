package com.ems.energy.service.impl;

import java.util.ArrayList;
import java.util.List;
import com.ems.common.utils.DateUtils;
import com.ems.common.utils.StringUtils;
import com.ems.energy.domain.EmsMeasure;
import com.ems.energy.domain.tree.Emstreecenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ems.energy.mapper.EmsMesuringToolMapper;
import com.ems.energy.domain.EmsMesuringTool;
import com.ems.energy.service.IEmsMesuringToolService;

/**
 * 表具管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-06
 */
@Service
public class EmsMesuringToolServiceImpl implements IEmsMesuringToolService
{
    @Autowired
    private EmsMesuringToolMapper emsMesuringToolMapper;

    /**
     * 查询表具管理
     *
     * @param measuringToolId 表具管理主键
     * @return 表具管理
     */
    @Override
    public EmsMesuringTool selectEmsMesuringToolByMeasuringToolId(Long measuringToolId)
    {
        return emsMesuringToolMapper.selectEmsMesuringToolByMeasuringToolId(measuringToolId);
    }

    /**
     * 查询表具管理列表
     *
     * @param emsMesuringTool 表具管理
     * @return 表具管理
     */
    @Override
    public List<EmsMesuringTool> selectEmsMesuringToolList(EmsMesuringTool emsMesuringTool)
    {
        return emsMesuringToolMapper.selectEmsMesuringToolList(emsMesuringTool);
    }

    /**
     * 新增表具管理
     *
     * @param emsMesuringTool 表具管理
     * @return 结果
     */
    @Override
    public int insertEmsMesuringTool(EmsMesuringTool emsMesuringTool)
    {
        emsMesuringTool.setCreateTime(DateUtils.getNowDate());
        return emsMesuringToolMapper.insertEmsMesuringTool(emsMesuringTool);
    }

    /**
     * 修改表具管理
     *
     * @param emsMesuringTool 表具管理
     * @return 结果
     */
    @Override
    public int updateEmsMesuringTool(EmsMesuringTool emsMesuringTool)
    {
        emsMesuringTool.setUpdateTime(DateUtils.getNowDate());
        return emsMesuringToolMapper.updateEmsMesuringTool(emsMesuringTool);
    }

    /**
     * 批量删除表具管理
     *
     * @param measuringToolIds 需要删除的表具管理主键
     * @return 结果
     */
    @Override
    public int deleteEmsMesuringToolByMeasuringToolIds(Long[] measuringToolIds)
    {
        return emsMesuringToolMapper.deleteEmsMesuringToolByMeasuringToolIds(measuringToolIds);
    }

    /**
     * 删除表具管理信息
     *
     * @param measuringToolId 表具管理主键
     * @return 结果
     */
    @Override
    public int deleteEmsMesuringToolByMeasuringToolId(Long measuringToolId)
    {
        return emsMesuringToolMapper.deleteEmsMesuringToolByMeasuringToolId(measuringToolId);
    }

    @Override
    public List<Emstreecenter> buildEmsMeasureTreeSelect(List<EmsMesuringTool> emsMesuringTools) {
        return null;
    }

    @Override
    public List<EmsMesuringTool> buildEmsMeasureTree(List<EmsMesuringTool> emsMesuringTools) {
        List<EmsMesuringTool> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<>();
        for (EmsMesuringTool mesuringTool : emsMesuringTools) {
            tempList.add(mesuringTool.getMeasuringToolId());
        }
        for (EmsMesuringTool costCenter : emsMesuringTools) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(costCenter.getParentId())) {
                recursionFn(emsMesuringTools, costCenter);
                returnList.add(costCenter);
            }
        }
        if (returnList.isEmpty()) {
            returnList = emsMesuringTools;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<EmsMesuringTool> list, EmsMesuringTool t)
    {
        // 得到子节点列表
        List<EmsMesuringTool> childList = getChildList(list, t);
//        t.setChildren(childList);
        for (EmsMesuringTool tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }
    /**
     * 得到子节点列表
     */
    private List<EmsMesuringTool> getChildList(List<EmsMesuringTool> list, EmsMesuringTool t)
    {
        List<EmsMesuringTool> tlist = new ArrayList<>();
        for (EmsMesuringTool n : list) {
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getMeasuringToolId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<EmsMesuringTool> list, EmsMesuringTool t)
    {
        return getChildList(list, t).size() > 0;
    }
}
