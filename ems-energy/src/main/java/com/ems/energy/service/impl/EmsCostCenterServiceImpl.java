package com.ems.energy.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ems.common.annotation.EmsLog;
import com.ems.common.constant.Constants;
import com.ems.common.constant.ErrorConstant;
import com.ems.common.constant.UserConstants;
import com.ems.common.exception.ServiceException;
import com.ems.common.utils.StringUtils;
import com.ems.energy.constant.DataConstant;
import com.ems.energy.constant.EnergyConstant;
import com.ems.energy.domain.EmsCostCenter;
import com.ems.energy.domain.tree.EmsCenterVoTree;
import com.ems.energy.domain.tree.Emstreecenter;
import com.ems.energy.domain.vo.EmsCostCenterVo;
import com.ems.energy.mapper.EmsCostCenterMapper;
import com.ems.energy.service.IEmsCostCenterService;
import com.ems.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 成本中心Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Service
public class EmsCostCenterServiceImpl implements IEmsCostCenterService {

    @Resource
    private EmsCostCenterMapper emsCostCenterMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ISysConfigService configService;

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 查询成本中心
     *
     * @param centerId 成本中心主键
     * @return 成本中心
     */
    @Override
    public EmsCostCenter selectEmsCostCenterByCenterId(Long centerId) {
        return emsCostCenterMapper.selectEmsCostCenterByCenterId(centerId);
    }

    /**
     * 查询成本中心列表
     *
     * @param emsCostCenter 成本中心
     * @return 成本中心
     */
    @Override
    public List<EmsCostCenter> selectEmsCostCenterList(EmsCostCenter emsCostCenter) {
        return emsCostCenterMapper.selectEmsCostCenterList(emsCostCenter);
    }

    /**
     * 新增成本中心
     *
     * @param emsCostCenter 成本中心
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @EmsLog
    public int insertEmsCostCenter(EmsCostCenter emsCostCenter) {
        EmsCostCenter info = emsCostCenterMapper.selectEmsCostCenterByCenterId(emsCostCenter.getParentCenterId());
        //没有父节点的情况
        if(ObjectUtils.isEmpty(info)){
            throw new ServiceException(ErrorConstant.TOP_COST_CENTER_IS_UNIQUE);
        }else{
            emsCostCenter.setOrderNum(info.getOrderNum() + 1);
        }
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_DISABLE.equals(info.getUseStatus()))
        {
            throw new ServiceException(ErrorConstant.COST_CENTER_NOT_USE);
        }
        //添加逻辑删除字段
        emsCostCenter.setDelFlag(EnergyConstant.NORMAL_DEL_FLAG);
        String preFix = stringRedisTemplate.boundValueOps(Constants.SYS_CONFIG_KEY + EnergyConstant.COST_CENTER_CODE).get();
        assert preFix != null;
        preFix = preFix.replace(EnergyConstant.DOUBLE_QUOT_MARK,EnergyConstant.EMPTY_STR);
        if(StringUtils.isEmpty(preFix)){
            configService.resetConfigCache();
            preFix = stringRedisTemplate.boundValueOps(Constants.SYS_CONFIG_KEY + EnergyConstant.COST_CENTER_CODE).get();
            if(StringUtils.isEmpty(preFix)){
                throw new ServiceException(ErrorConstant.KEY_IS_NOT_FOUND);
            }
        }
        Integer count = emsCostCenterMapper.selectCount();
        emsCostCenter.setCenterCode(preFix + String.format(DataConstant.NUMBER_03D,count+1));
        emsCostCenter.setAncestors(info.getAncestors() + EnergyConstant.BASE_SEPARATOR + emsCostCenter.getParentCenterId());
        return emsCostCenterMapper.insertEmsCostCenter(emsCostCenter);
    }

    /**
     * 修改成本中心
     *
     * @param emsCostCenter 成本中心
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEmsCostCenter(EmsCostCenter emsCostCenter) {
        return emsCostCenterMapper.updateEmsCostCenter(emsCostCenter);
    }

    /**
     * 批量删除成本中心
     *  TODO: 添加成本中心和计量器具中间表后,删除成本中心后
     *         1.中间表数据删除
     *         2.该计量器具的成本中心ID置空
     * @param centerIds 需要删除的成本中心主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteEmsCostCenterByCenterIds(Long[] centerIds) {
        // 1.中间表数据的删除
        //iEmsCostMeaService.deleteEmsCostMeaByCostCenterIds(centerIds);
        // 2.对应的计量器具的成本中心ID置空
        /**
         *  2021-11-23 TODO: 目前使用的是ems_mea_center_relat表维护关系,删除成本中心之后,应该把关系表内含有该成本中心的数据删除
         */
        //刪除成本中心关系表内含有待删除成本中心ID的数据
        //emsMeaCenterRelatService.deleteEmsMeaCenterRelatByCostCenterIds();
        return emsCostCenterMapper.deleteEmsCostCenterByCenterIds(centerIds);
    }

    /**
     * 删除成本中心信息
     *
     * @param centerId 成本中心主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteEmsCostCenterByCenterId(Long centerId) {
        return emsCostCenterMapper.deleteEmsCostCenterByCenterId(centerId);
    }

    /**
     * 构建前端所需要的下拉结构
     *
     * @param emsCostCenters 成本中心树
     * @return 下拉列表
     */
    @Override
    public List<Emstreecenter> buildcenterselect(List<EmsCostCenter> emsCostCenters) {

        List<EmsCostCenter> costCenters = builcenterselect(emsCostCenters);
        return costCenters.stream().map(Emstreecenter::new).collect(Collectors.toList());
    }

    /**
     * 根据条件查询
     * @param emsCostCenter
     * @return
     */
    @Override
    public List<EmsCostCenterVo> pagelist(EmsCostCenter emsCostCenter) {
        return emsCostCenterMapper.selectEmsCostCenterByPage(emsCostCenter);
    }

    /**
     * 检查成本中心名称是否唯一
     * @param emsCostCenter
     * @return
     */
    @Override
    public String checkCenterNameUnique(EmsCostCenter emsCostCenter) {
        Long centerId = StringUtils.isNull(emsCostCenter.getCenterId()) ? -1L : emsCostCenter.getCenterId();
        EmsCostCenter info = emsCostCenterMapper.checkDeptNameUnique(emsCostCenter.getCenterName(), emsCostCenter.getParentCenterId());
        if (StringUtils.isNotNull(info) && info.getCenterId().longValue() != centerId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public List<EmsCenterVoTree> buildcenterVoselect(List<EmsCostCenterVo> emsCostCenters) {
        List<EmsCostCenterVo> costCenters = builcenterselectVo(emsCostCenters);
        return costCenters.stream().map(EmsCenterVoTree::new).collect(Collectors.toList());
    }

    public List<EmsCostCenterVo> builcenterselectVo(List<EmsCostCenterVo> emsCostCenters) {
        List<EmsCostCenterVo> returnList = new ArrayList
                <>();
        List<Long> tempList = new ArrayList<>();
        for (EmsCostCenterVo costCenter : emsCostCenters) {
            tempList.add(costCenter.getCenterId());
        }
        for (EmsCostCenterVo costCenter : emsCostCenters) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(costCenter.getParentCenterId())) {
                recursionFnVo(emsCostCenters, costCenter);
                returnList.add(costCenter);
            }
        }
        if (returnList.isEmpty()) {
            returnList = emsCostCenters;
        }
        return returnList;
    }

    private void recursionFnVo(List<EmsCostCenterVo> emsCostCenters, EmsCostCenterVo costCenter) {
        // 得到子节点列表
        List<EmsCostCenterVo> childList = getChildListVo(emsCostCenters, costCenter);
        costCenter.setChildren(childList);
        for (EmsCostCenterVo tChild : childList) {
            if (hasChildVo(emsCostCenters, tChild)) {
                recursionFnVo(emsCostCenters, tChild);
            }
        }

    }
    private boolean hasChildVo(List<EmsCostCenterVo> emsCostCenters, EmsCostCenterVo tChild) {
        return getChildListVo(emsCostCenters, tChild).size() > 0;
    }

    private List<EmsCostCenterVo> getChildListVo(List<EmsCostCenterVo> emsCostCenters, EmsCostCenterVo costCenter) {
        List<EmsCostCenterVo> tlist = new ArrayList<>();
        for (EmsCostCenterVo n : emsCostCenters) {
            if (StringUtils.isNotNull(n.getParentCenterId()) && n.getParentCenterId().longValue() == costCenter.getCenterId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    public List<EmsCostCenter> builcenterselect(List<EmsCostCenter> emsCostCenters) {
        List<EmsCostCenter> returnList = new ArrayList
                <>();
        List<Long> tempList = new ArrayList<>();
        for (EmsCostCenter costCenter : emsCostCenters) {
            tempList.add(costCenter.getCenterId());
        }
        for (EmsCostCenter costCenter : emsCostCenters) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(costCenter.getParentCenterId())) {
                recursionFn(emsCostCenters, costCenter);
                returnList.add(costCenter);
            }
        }
        if (returnList.isEmpty()) {
            returnList = emsCostCenters;
        }
        return returnList;
    }

    private void recursionFn(List<EmsCostCenter> emsCostCenters, EmsCostCenter costCenter) {
        // 得到子节点列表
        List<EmsCostCenter> childList = getChildList(emsCostCenters, costCenter);
        costCenter.setChildren(childList);
        for (EmsCostCenter tChild : childList) {
            if (hasChild(emsCostCenters, tChild)) {
                recursionFn(emsCostCenters, tChild);
            }
        }
    }

    private boolean hasChild(List<EmsCostCenter> emsCostCenters, EmsCostCenter tChild) {
        return getChildList(emsCostCenters, tChild).size() > 0;
    }

    private List<EmsCostCenter> getChildList(List<EmsCostCenter> emsCostCenters, EmsCostCenter costCenter) {
        List<EmsCostCenter> tlist = new ArrayList<>();
        for (EmsCostCenter n : emsCostCenters) {
            if (StringUtils.isNotNull(n.getParentCenterId()) && n.getParentCenterId().longValue() == costCenter.getCenterId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }
}
