package com.ems.energy.service;

import java.util.List;
import com.ems.energy.domain.EmsSamplerDayReport;

/**
 * 日数据报Service接口
 *
 * <AUTHOR>
 * @date 2022-06-01
 */
public interface IEmsSamplerDayReportService
{
    /**
     * 查询日数据报
     *
     * @param reportId 日数据报主键
     * @return 日数据报
     */
    EmsSamplerDayReport selectEmsSamplerDayReportByReportId(Long reportId);

    /**
     * 查询日数据报列表
     *
     * @param emsSamplerDayReport 日数据报
     * @return 日数据报集合
     */
    List<EmsSamplerDayReport> selectEmsSamplerDayReportList(EmsSamplerDayReport emsSamplerDayReport);

    /**
     * 新增日数据报
     *
     * @param emsSamplerDayReport 日数据报
     * @return 结果
     */
    int insertEmsSamplerDayReport(EmsSamplerDayReport emsSamplerDayReport);

    /**
     * 修改日数据报
     *
     * @param emsSamplerDayReport 日数据报
     * @return 结果
     */
    int updateEmsSamplerDayReport(EmsSamplerDayReport emsSamplerDayReport);

    /**
     * 批量删除日数据报
     *
     * @param reportIds 需要删除的日数据报主键集合
     * @return 结果
     */
    int deleteEmsSamplerDayReportByReportIds(Long[] reportIds);

    /**
     * 删除日数据报信息
     *
     * @param reportId 日数据报主键
     * @return 结果
     */
    int deleteEmsSamplerDayReportByReportId(Long reportId);
}
