package com.ems.energy.service;

import com.ems.energy.domain.ScriptConfig;
import com.ems.energy.domain.dto.ScriptQueryResponse;

import java.util.List;
import java.util.Map;

/**
 * 脚本查询Service接口
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface IScriptQueryService {

    /**
     * 执行动态脚本查询
     *
     * @param code   脚本代码标识符
     * @param params 查询参数
     * @return 查询结果
     */
    ScriptQueryResponse executeQuery(String code, Map<String, Object> params);

    /**
     * 根据代码查询脚本配置
     *
     * @param code 脚本代码标识符
     * @return 脚本配置
     */
    ScriptConfig getScriptConfigByCode(String code);

    /**
     * 根据ID查询脚本配置
     *
     * @param id 脚本配置ID
     * @return 脚本配置
     */
    ScriptConfig getScriptConfigById(Long id);

    /**
     * 查询脚本配置列表
     *
     * @param scriptConfig 查询条件
     * @return 脚本配置列表
     */
    List<ScriptConfig> selectScriptConfigList(ScriptConfig scriptConfig);

    /**
     * 新增脚本配置
     *
     * @param scriptConfig 脚本配置
     * @return 结果
     */
    int insertScriptConfig(ScriptConfig scriptConfig);

    /**
     * 修改脚本配置
     *
     * @param scriptConfig 脚本配置
     * @return 结果
     */
    int updateScriptConfig(ScriptConfig scriptConfig);

    /**
     * 删除脚本配置
     *
     * @param id 脚本配置ID
     * @return 结果
     */
    int deleteScriptConfigById(Long id);

    /**
     * 批量删除脚本配置
     *
     * @param ids 需要删除的脚本配置ID数组
     * @return 结果
     */
    int deleteScriptConfigByIds(Long[] ids);

    /**
     * 验证脚本配置
     *
     * @param scriptConfig 脚本配置
     * @return 验证结果
     */
    boolean validateScriptConfig(ScriptConfig scriptConfig);

    /**
     * 获取查询配置属性
     *
     * @return 配置属性
     */
    Object getQueryProperties();
}
