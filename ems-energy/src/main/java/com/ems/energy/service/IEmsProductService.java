package com.ems.energy.service;

import com.ems.energy.domain.EmsProduct;

import java.util.List;


/**
 * 产品Service接口
 *
 * <AUTHOR>
 * @date 2022-01-18
 */
public interface IEmsProductService
{
    /**
     * 查询产品
     *
     * @param proId 产品主键
     * @return 产品
     */
    EmsProduct selectEmsProductByProId(Long proId);

    /**
     * 查询产品列表
     *
     * @param emsProduct 产品
     * @return 产品集合
     */
    List<EmsProduct> selectEmsProductList(EmsProduct emsProduct);

    /**
     * 新增产品
     *
     * @param emsProduct 产品
     * @return 结果
     */
    int insertEmsProduct(EmsProduct emsProduct);

    /**
     * 修改产品
     *
     * @param emsProduct 产品
     * @return 结果
     */
    int updateEmsProduct(EmsProduct emsProduct);

    /**
     * 批量删除产品
     *
     * @param proIds 需要删除的产品主键集合
     * @return 结果
     */
    int deleteEmsProductByProIds(Long[] proIds);

    /**
     * 删除产品信息
     *
     * @param proId 产品主键
     * @return 结果
     */
    int deleteEmsProductByProId(Long proId);
}
