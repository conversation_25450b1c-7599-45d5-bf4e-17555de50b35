package com.ems.energy.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.utils.DateUtils;
import com.ems.common.utils.StringUtils;
import com.ems.energy.constant.DataConstant;
import com.ems.energy.constant.EnergyConstant;
import com.ems.energy.domain.EmsDaily;
import com.ems.energy.domain.EmsDailyDTO;
import com.ems.energy.domain.EmsReport;
import com.ems.energy.domain.vo.EmsChartvotwo;
import com.ems.energy.enums.ElectTimePeriod;
import com.ems.energy.mapper.EmsDailyMapper;
import com.ems.energy.mapper.EmsReportMapper;
import com.ems.energy.service.IEmsDailyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;


/**
 * 日报Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-28
 */
@Service
public class EmsDailyServiceImpl implements IEmsDailyService
{
    @Resource
    private EmsDailyMapper emsDailyMapper;

    @Resource
    private EmsReportMapper emsReportMapper;

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 查询日报
     *
     * @param dailyId 日报主键
     * @return 日报
     */
    @Override
    public EmsDaily selectEmsDailyByDailyId(Long dailyId)
    {
        return emsDailyMapper.selectEmsDailyByDailyId(dailyId);
    }

    /**
     * 查询日报列表
     *
     * @param emsDaily 日报
     * @return 日报
     */
    @Override
    public List<EmsDaily> selectEmsDailyList(EmsDaily emsDaily)
    {
        return emsDailyMapper.selectEmsDailyList(emsDaily);
    }

    /**
     * 新增日报
     *
     * @param emsDaily 日报
     * @return 结果
     */
    @Override
    public int insertEmsDaily(EmsDaily emsDaily)
    {
        emsDaily.setCreateTime(DateUtils.getNowDate().toString());
        return emsDailyMapper.insertEmsDaily(emsDaily);
    }

    /**
     * 修改日报
     *
     * @param emsDaily 日报
     * @return 结果
     */
    @Override
    public int updateEmsDaily(EmsDaily emsDaily)
    {
        return emsDailyMapper.updateEmsDaily(emsDaily);
    }

    /**
     * 批量删除日报
     *
     * @param dailyIds 需要删除的日报主键
     * @return 结果
     */
    @Override
    public int deleteEmsDailyByDailyIds(Long[] dailyIds)
    {
        return emsDailyMapper.deleteEmsDailyByDailyIds(dailyIds);
    }

    /**
     * 删除日报信息
     *
     * @param dailyId 日报主键
     * @return 结果
     */
    @Override
    public int deleteEmsDailyByDailyId(Long dailyId)
    {
        return emsDailyMapper.deleteEmsDailyByDailyId(dailyId);
    }

    @Override
    public int selectEmsDailyListTo(EmsDaily emsDaily, EmsReport emsReport) {
        Date today = new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtils.YYYY_MM_DD);
        String yesterday = simpleDateFormat.format(today);
        List<EmsReport> emsReports=emsReportMapper.selectEmsDailyReport(emsReport,yesterday);
        List<EmsDaily> result = new ArrayList<>();
        List<String> dataJson = emsReports.stream().map(EmsReport::getDataJson).collect(Collectors.toList());
        dataJson.forEach(data->result.add(JSON.parseObject(data,EmsDaily.class)));
        return emsDailyMapper.insertEmsJson(result);
    }

    @Override
    public List<EmsDailyDTO> selectlistOne(EmsDailyDTO emsDaily) {
        return emsDailyMapper.selectlistOne(emsDaily);
    }

    @Override
    public AjaxResult selectDaily(EmsDailyDTO emsDailyDTO) {
       List<EmsDaily> emsDailyList= emsDailyMapper.selectDaily(emsDailyDTO);
        List<Long> collect = emsDailyList.stream().map(EmsDaily::getDeviceId).distinct().collect(Collectors.toList());
        List<EmsDailyDTO> emsDailyDtoS = new ArrayList<>();
        //计算每个设备的平均值
        computAvg(emsDailyList, collect, emsDailyDtoS);
        ArrayList<EmsDailyDTO> res = emsDailyDtoS.stream().collect(collectingAndThen(toCollection(() -> new TreeSet<>(comparing(EmsDailyDTO::getDeviceId))), ArrayList::new));
        return AjaxResult.success(res);
    }

    @Override
    public AjaxResult selectlistPeak(EmsDaily emsDaily,String time) throws ParseException {

        SimpleDateFormat df = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
        Date now;
        if(StringUtils.isNotEmpty(time)){
            now = DateUtils.parseDate(time);
        }else{
            now = new Date();
        }
        SimpleDateFormat ds = new SimpleDateFormat(DateUtils.YYYY_MM_DD);

        //高峰第一阶段
        List<EmsDaily> peakFirst = peakFirst(emsDaily,df,ds,now);
        //高峰第二阶段
        List<EmsDaily> peakSecond = peakSecond(emsDaily,df,ds,now);
        //平第一阶段
        List<EmsDaily> flatHumpFirst = flatHumpFirst(emsDaily,df,ds,now);
        //平第二阶段
        List<EmsDaily> flatHumpSecond = flatHumpSecond(emsDaily, df,ds,now);
        //低谷阶段
        List<EmsDaily> lowEbb = lowEbb(emsDaily, df,ds,now);
        ArrayList<String> tableHead = buildTableHead();
        //获取所有阶段的设备ID
        List<Long> deviceIds = buildDeviceIds(peakFirst,peakSecond,flatHumpFirst,flatHumpSecond,lowEbb);
        List<List<String>> result = new ArrayList<>();
        result.add(tableHead);
        deviceIds.forEach(r->{
            List<String> deviceIdRes = buildResult(r,peakFirst,peakSecond,flatHumpFirst,flatHumpSecond,lowEbb);
            result.add(deviceIdRes);
        });
        if (CollectionUtils.isNotEmpty(result)){
            return AjaxResult.success(result);
        }else {
            return null;
        }
    }

    /**
     * 饼图图表
     * @param emsDaily
     * @return
     * @throws ParseException
     */
    @Override
    public AjaxResult selectlistChart(EmsDaily emsDaily,String time) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
        Date now;
        if(StringUtils.isNotEmpty(time)){
            now = DateUtils.parseDate(time);
        }else{
            now = new Date();
        }
        SimpleDateFormat ds = new SimpleDateFormat(DateUtils.YYYY_MM_DD);
       //高峰第一阶段
        List<EmsDaily> peakFirst = peakFirst(emsDaily,df, ds,now);
        //高峰第二阶段
        List<EmsDaily> peakSecond = peakSecond(emsDaily,df, ds,now);
        //平第一阶段
        List<EmsDaily> flatHumpFirst = flatHumpFirst(emsDaily, df, ds,now);
        //平第二阶段
        List<EmsDaily> flatHumpSecond = flatHumpSecond(emsDaily, df, ds,now);
        //低谷阶段
        List<EmsDaily> lowEbb = lowEbb(emsDaily, df, ds,now);
        //拿出集合里面的值求和
        List<EmsChartvotwo> newListone = getEmsChartvotwos(peakFirst, peakSecond, flatHumpFirst, flatHumpSecond, lowEbb);
        return AjaxResult.success(newListone);
    }
    /**
     *
     * @param peakFirst 峰第一阶段总用电量
     * @param peakSecond 峰第二 阶段总用电量
     * @param flatHumpFirst 平第一阶段总用电量
     * @param flatHumpSecond 平第二阶段总用电量
     * @param lowEbb 谷第一阶段总用电量
     * @return
     */
    private List<EmsChartvotwo> getEmsChartvotwos(List<EmsDaily> peakFirst, List<EmsDaily> peakSecond, List<EmsDaily> flatHumpFirst, List<EmsDaily> flatHumpSecond, List<EmsDaily> lowEbb) {
        Long sumFenone= peakFirst.stream().mapToLong(EmsDaily::minuteCons).sum();
        Long sumFentwo= peakSecond.stream().mapToLong(EmsDaily::minuteCons).sum();
        Long sumPinone= flatHumpFirst.stream().mapToLong(EmsDaily::minuteCons).sum();
        Long sumPintwo= flatHumpSecond.stream().mapToLong(EmsDaily::minuteCons).sum();
        Long sumGuone= lowEbb.stream().mapToLong(EmsDaily::minuteCons).sum();
        BigDecimal l1 = BigDecimal.valueOf(sumFenone + sumFentwo);
        BigDecimal l2= BigDecimal.valueOf(sumPinone+sumPintwo);
        BigDecimal l3= BigDecimal.valueOf(sumGuone);
        BigDecimal pjzhi=l1.add(l2).add(l3);
        BigDecimal apt =new BigDecimal(EnergyConstant.ONE);
        BigDecimal l1one =l1.divide(pjzhi,EnergyConstant.HUNDRED, RoundingMode.HALF_UP);
        BigDecimal l1two =l2.divide(pjzhi,EnergyConstant.HUNDRED, RoundingMode.HALF_UP);
        BigDecimal l1three =l3.divide(pjzhi,EnergyConstant.HUNDRED, RoundingMode.HALF_UP);
        List<EmsChartvotwo> newListone = getEmsChartvotwos(apt, l1one, l1two, l1three);
        return newListone;
    }

    /**
     *
     * @param apt 保留两位小数
     * @param l1one 峰用电量占比
     * @param l1two 平用电量占比
     * @param l1three 谷用电量占比
     * @return 放进集合返回
     */
    private List<EmsChartvotwo> getEmsChartvotwos(BigDecimal apt, BigDecimal l1one, BigDecimal l1two, BigDecimal l1three) {
        List<EmsChartvotwo> newListone = new ArrayList<>();
        EmsChartvotwo emsChartvoone = new EmsChartvotwo();
        EmsChartvotwo emsChartvotwo = new EmsChartvotwo();
        EmsChartvotwo emsChartvothree = new EmsChartvotwo();
        emsChartvoone.setName(DataConstant.TimePeriod.PEAK);
        emsChartvotwo.setName(DataConstant.TimePeriod.FLAT_HUMP);
        emsChartvothree.setName(DataConstant.TimePeriod.LOW_EBB);
        BigDecimal multiplyone = l1one.multiply(apt);
        BigDecimal multiplytwo = l1two.multiply(apt);
        BigDecimal multiplythree = l1three.multiply(apt);
        emsChartvoone.setValue(multiplyone);
        emsChartvotwo.setValue(multiplytwo);
        emsChartvothree.setValue(multiplythree);
        newListone.add(emsChartvoone);
        newListone.add(emsChartvotwo);
        newListone.add(emsChartvothree);
        return newListone;
    }

    /**
     * 表头构建
     * @return
     */
    private ArrayList<String> buildTableHead() {
        ArrayList<String> tableHead = new ArrayList<>();
        tableHead.add(EnergyConstant.DEVI_NAME);
        tableHead.add(ElectTimePeriod.PEAK_FIRST.getPeriodName());
        tableHead.add(ElectTimePeriod.PEAK_SECOND.getPeriodName());
        tableHead.add(ElectTimePeriod.FLAT_HUMP_FIRST.getPeriodName());
        tableHead.add(ElectTimePeriod.FLAT_HUMP_SECOND.getPeriodName());
        tableHead.add(ElectTimePeriod.LOW_EBB.getPeriodName());
        return tableHead;
    }

    /**
     * 构建设备id
     * @param peakFirst 峰第一阶段总用电量
     * @param peakSecond 峰第二阶段总用电量
     * @param flatHumpFirst 平第一阶段总用电量
     * @param flatHumpSecond 平第二阶段总用电量
     * @param lowEbb 谷第一阶段总用电量
     * @return
     */
    private List<Long> buildDeviceIds(List<EmsDaily> peakFirst, List<EmsDaily> peakSecond, List<EmsDaily> flatHumpFirst, List<EmsDaily> flatHumpSecond, List<EmsDaily> lowEbb) {
        List<Long> peakFirstIds = peakFirst.stream().map(EmsDaily::getDeviceId).collect(Collectors.toList());
        List<Long> peakSecondIds = peakSecond.stream().map(EmsDaily::getDeviceId).collect(Collectors.toList());
        List<Long> flatHumpFirstIds = flatHumpFirst.stream().map(EmsDaily::getDeviceId).collect(Collectors.toList());
        List<Long> flatHumpSecondIds = flatHumpSecond.stream().map(EmsDaily::getDeviceId).collect(Collectors.toList());
        List<Long> lowEbbIds = lowEbb.stream().map(EmsDaily::getDeviceId).collect(Collectors.toList());
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.addAll(peakFirstIds);
        deviceIds.addAll(peakSecondIds);
        deviceIds.addAll(flatHumpFirstIds);
        deviceIds.addAll(flatHumpSecondIds);
        deviceIds.addAll(lowEbbIds);
        return deviceIds.stream().distinct().collect(Collectors.toList());
    }
    /**
     *
     * @param deviceId 设备ID
     * @param peakFirst 峰电1
     * @param peakSecond 峰电2
     * @param flatHumpFirst 平电1
     * @param flatHumpSecond 平电23
     * @param lowEbb  谷电
     * @return
     */
    private List<String> buildResult(Long deviceId, List<EmsDaily> peakFirst, List<EmsDaily> peakSecond, List<EmsDaily> flatHumpFirst, List<EmsDaily> flatHumpSecond, List<EmsDaily> lowEbb) {
        ArrayList<String> result = new ArrayList<>();
        buildEmsDailyResult(deviceId, peakFirst, result);
        buildEmsDailyResult(deviceId, peakSecond, result);
        buildEmsDailyResult(deviceId, flatHumpFirst, result);
        buildEmsDailyResult(deviceId, flatHumpSecond, result);
        buildEmsDailyResult(deviceId, lowEbb, result);
        return result;
    }

    private void buildEmsDailyResult(Long deviceId, List<EmsDaily> dailyList, ArrayList<String> result) {
        dailyList.forEach(res -> {
            if (res.getDeviceId().equals(deviceId)) {
                if (!contains(result, res.getDeviceId().toString())) {
                    result.add(0, res.getDeviceId().toString());
                }
                if (ObjectUtils.isNotEmpty(res.getMinuteCons())) {
                    result.add(res.getMinuteCons().toString());
                }
            }
        });
        if (!deviceContains(dailyList, deviceId)) {
            result.add(EnergyConstant.BASE_ANCESTORS);
        }
    }

    public boolean contains(ArrayList<String> result,String deviceId){
        for (String s : result) {
            if(StringUtils.isNotEmpty(s) && s.equals(deviceId)){
                return true;
            }
        }
        return false;
    }

    public boolean deviceContains(List<EmsDaily> result,Long deviceId){
        for (EmsDaily emsDaily : result) {
            if(emsDaily.getDeviceId().equals(deviceId)){
                return true;
            }
        }
        return false;
    }
    /**
     * 低谷阶段
     * @param emsDaily
     * @param df
     * @param ds
     * @param now
     * @return
     */
    private List<EmsDaily> lowEbb(EmsDaily emsDaily, SimpleDateFormat df ,SimpleDateFormat ds,Date now) throws ParseException {
        String low = df.format(df.parse(ds.format(now) + EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_LOW));
        String ebb = df.format(df.parse(ds.format(now) + EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_EBB));
        String ebbone = df.format(df.parse(ds.format(now) + EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_ONE));
        String lowtwo = df.format(df.parse(ds.format(now) + EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_TWO));

        return new ArrayList<>(emsDailyMapper.selectTimePeriodgrain(emsDaily, low, ebb,ebbone,lowtwo).stream().collect(Collectors.toMap(EmsDaily::getDeviceId, a -> a, (o1, o2) -> {
            o1.setMinuteCons(o1.getMinuteCons()+o2.getMinuteCons());
            return o1;
        })).values());
    }

    /**
     * 平第二阶段
     * @param emsDaily
     * @param df
     * @param ds
     * @param now
     * @return
     */
    private List<EmsDaily> flatHumpSecond(EmsDaily emsDaily,SimpleDateFormat df ,SimpleDateFormat ds,Date now ) throws ParseException {
        String sregularone = df.format(df.parse(ds.format(now) + EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_NDONE));
        String soverone = df.format(df.parse(ds.format(now) + EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_BEGINONE));
        return new ArrayList<>(emsDailyMapper.selectTimePeriod(emsDaily, sregularone, soverone).stream().collect(Collectors.toMap(EmsDaily::getDeviceId, a -> a, (o1, o2) -> {
            o1.setMinuteCons(o1.getMinuteCons() + o2.getMinuteCons());
            return o1;
        })).values());
    }

    /**
     * 平第一阶段
     * @param emsDaily
     * @param df
     * @param ds
     * @param now
     * @return
     */
    private List<EmsDaily> flatHumpFirst(EmsDaily emsDaily,SimpleDateFormat df ,SimpleDateFormat ds,Date now ) throws ParseException {
        String sregular = df.format(df.parse(ds.format(now) + EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_EBB));
        String sover = df.format(df.parse(ds.format(now)+ EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_BEGIN));
        return new ArrayList<>(emsDailyMapper.selectTimePeriod(emsDaily, sregular, sover).stream().collect(Collectors.toMap(EmsDaily::getDeviceId, a -> a, (o1, o2) -> {
            o1.setMinuteCons(o1.getMinuteCons() + o2.getMinuteCons());
            return o1;
        })).values());
    }

    /**
     * 高峰第二阶段
     * @param emsDaily
     * @param df
     * @param ds
     * @param now
     * @return
     */
    private List<EmsDaily> peakSecond(EmsDaily emsDaily,SimpleDateFormat df ,SimpleDateFormat ds,Date now ) throws ParseException {
        String sBeginone = df.format(df.parse(ds.format(now)+ EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_BEGINONE));
        String sEndone = df.format(df.parse(ds.format(now) + EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_LOW));
        return new ArrayList<>(emsDailyMapper.selectTimePeriod(emsDaily, sBeginone, sEndone).stream().collect(Collectors.toMap(EmsDaily::getDeviceId, a -> a, (o1, o2) -> {
            o1.setMinuteCons(o1.getMinuteCons() + o2.getMinuteCons());
            return o1;
        })).values());
    }

    /**
     * 高峰第一阶段
     * @param emsDaily
     * @param df
     * @param ds
     * @param now
     * @return
     */
    private List<EmsDaily> peakFirst(EmsDaily emsDaily, SimpleDateFormat df, SimpleDateFormat ds, Date now) throws ParseException {
        String sBegin = df.format(df.parse(ds.format(now) + EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_BEGIN));
        String sEnd = df.format(df.parse(ds.format(now)+ EnergyConstant.EMPTY_BLANK +EnergyConstant.DATETIME_NDONE));
        return new ArrayList<>(emsDailyMapper.selectTimePeriod(emsDaily, sBegin, sEnd).stream().collect(Collectors.toMap(EmsDaily::getDeviceId, a -> a, (o1, o2) -> {
            o1.setMinuteCons(o1.getMinuteCons() + o2.getMinuteCons());
            return o1;
        })).values());
    }

    /**
     * 计算报表的平均值
     * @param emsDailyList
     * @param collect
     * @param emsDailyDtos
     */
    private void computAvg(List<EmsDaily> emsDailyList, List<Long> collect, List<EmsDailyDTO> emsDailyDtos) {
        for (Long aLong : collect) {
            for (EmsDaily daily : emsDailyList){
                if(daily.getDeviceId().equals(aLong)){
                    EmsDailyDTO emsDailyDto1 = new EmsDailyDTO();
                    double outPutPiece = emsDailyList.stream().filter(r->r.getDeviceId().equals(aLong)).mapToLong(EmsDaily::getOutputPiece).average().getAsDouble();
                    double outPutModel = emsDailyList.stream().filter(r->r.getDeviceId().equals(aLong)).mapToLong(EmsDaily::getOutputModel).average().getAsDouble();
                    double allWeight = emsDailyList.stream().filter(r->r.getDeviceId().equals(aLong)).mapToLong(EmsDaily::getAllWeight).average().getAsDouble();
                    double prodTime = emsDailyList.stream().filter(r->r.getDeviceId().equals(aLong)).mapToLong(EmsDaily::getProdTime).average().getAsDouble();
                    double powerCons = emsDailyList.stream().filter(r->r.getDeviceId().equals(aLong)).mapToLong(EmsDaily::getPowerCons).average().getAsDouble();
                    double sum = emsDailyList.stream().filter(r->r.getDeviceId().equals(aLong)).mapToLong(EmsDaily::getMinuteCons).average().getAsDouble();
                    emsDailyDto1.setDeviceId(aLong);
                    emsDailyDto1.setCreateTime(daily.getCreateTime());
                    emsDailyDto1.setProdName(daily.getProdName());
                    emsDailyDto1.setCreateTime(daily.getCreateTime());
                    emsDailyDto1.setOutputPiece((long) outPutPiece);
                    emsDailyDto1.setOutputModel((long) outPutModel);
                    emsDailyDto1.setAllWeight((long) allWeight);
                    emsDailyDto1.setProdTime((long) prodTime);
                    emsDailyDto1.setPowerCons((long) powerCons);
                    emsDailyDto1.setMinuteCons((long) sum);
                    emsDailyDto1.setDeviceId(aLong);
                    emsDailyDtos.add(emsDailyDto1);
                }
            }
        }
    }
}
