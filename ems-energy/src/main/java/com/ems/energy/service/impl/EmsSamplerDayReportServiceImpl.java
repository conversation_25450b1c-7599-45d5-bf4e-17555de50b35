package com.ems.energy.service.impl;

import java.util.List;
import com.ems.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ems.energy.mapper.EmsSamplerDayReportMapper;
import com.ems.energy.domain.EmsSamplerDayReport;
import com.ems.energy.service.IEmsSamplerDayReportService;

/**
 * 日数据报Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-01
 */
@Service
public class EmsSamplerDayReportServiceImpl implements IEmsSamplerDayReportService
{
    @Autowired
    private EmsSamplerDayReportMapper emsSamplerDayReportMapper;

    /**
     * 查询日数据报
     *
     * @param reportId 日数据报主键
     * @return 日数据报
     */
    @Override
    public EmsSamplerDayReport selectEmsSamplerDayReportByReportId(Long reportId)
    {
        return emsSamplerDayReportMapper.selectEmsSamplerDayReportByReportId(reportId);
    }

    /**
     * 查询日数据报列表
     *
     * @param emsSamplerDayReport 日数据报
     * @return 日数据报
     */
    @Override
    public List<EmsSamplerDayReport> selectEmsSamplerDayReportList(EmsSamplerDayReport emsSamplerDayReport)
    {
        return emsSamplerDayReportMapper.selectEmsSamplerDayReportList(emsSamplerDayReport);
    }

    /**
     * 新增日数据报
     *
     * @param emsSamplerDayReport 日数据报
     * @return 结果
     */
    @Override
    public int insertEmsSamplerDayReport(EmsSamplerDayReport emsSamplerDayReport)
    {
        emsSamplerDayReport.setCreateTime(DateUtils.getNowDate());
        return emsSamplerDayReportMapper.insertEmsSamplerDayReport(emsSamplerDayReport);
    }

    /**
     * 修改日数据报
     *
     * @param emsSamplerDayReport 日数据报
     * @return 结果
     */
    @Override
    public int updateEmsSamplerDayReport(EmsSamplerDayReport emsSamplerDayReport)
    {
        emsSamplerDayReport.setUpdateTime(DateUtils.getNowDate());
        return emsSamplerDayReportMapper.updateEmsSamplerDayReport(emsSamplerDayReport);
    }

    /**
     * 批量删除日数据报
     *
     * @param reportIds 需要删除的日数据报主键
     * @return 结果
     */
    @Override
    public int deleteEmsSamplerDayReportByReportIds(Long[] reportIds)
    {
        return emsSamplerDayReportMapper.deleteEmsSamplerDayReportByReportIds(reportIds);
    }

    /**
     * 删除日数据报信息
     *
     * @param reportId 日数据报主键
     * @return 结果
     */
    @Override
    public int deleteEmsSamplerDayReportByReportId(Long reportId)
    {
        return emsSamplerDayReportMapper.deleteEmsSamplerDayReportByReportId(reportId);
    }
}
