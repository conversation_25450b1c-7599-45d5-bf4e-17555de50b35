package com.ems.energy.service;

import java.util.Date;
import java.util.List;
import com.ems.energy.domain.EmsCarbonFactor;

/**
 * 碳排放因子设置Service接口
 *
 * <AUTHOR>
 * @date 2022-06-02
 */
public interface IEmsCarbonFactorService
{
    /**
     * 查询碳排放因子设置
     *
     * @param factorId 碳排放因子设置主键
     * @return 碳排放因子设置
     */
    EmsCarbonFactor selectEmsCarbonFactorByFactorId(Long factorId);

    /**
     * 查询碳排放因子设置列表
     *
     * @param emsCarbonFactor 碳排放因子设置
     * @return 碳排放因子设置集合
     */
    List<EmsCarbonFactor> selectEmsCarbonFactorList(EmsCarbonFactor emsCarbonFactor);

    /**
     * 新增碳排放因子设置
     *
     * @param emsCarbonFactor 碳排放因子设置
     * @return 结果
     */
    int insertEmsCarbonFactor(EmsCarbonFactor emsCarbonFactor);

    /**
     * 修改碳排放因子设置
     *
     * @param emsCarbonFactor 碳排放因子设置
     * @return 结果
     */
    int updateEmsCarbonFactor(EmsCarbonFactor emsCarbonFactor);

    /**
     * 批量删除碳排放因子设置
     *
     * @param factorIds 需要删除的碳排放因子设置主键集合
     * @return 结果
     */
    int deleteEmsCarbonFactorByFactorIds(Long[] factorIds);

    /**
     * 删除碳排放因子设置信息
     *
     * @param factorId 碳排放因子设置主键
     * @return 结果
     */
    int deleteEmsCarbonFactorByFactorId(Long factorId);

    EmsCarbonFactor selectValidEmsCarbonFactor(Date time, String energyCategory);
}
