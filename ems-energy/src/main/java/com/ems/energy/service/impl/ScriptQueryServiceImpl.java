package com.ems.energy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ems.common.exception.ServiceException;
import com.ems.common.utils.StringUtils;
import com.ems.energy.adapter.QueryAdapter;
import com.ems.energy.config.ScriptQueryProperties;
import com.ems.energy.domain.ScriptConfig;
import com.ems.energy.domain.dto.ScriptQueryResponse;
import com.ems.energy.mapper.ScriptConfigMapper;
import com.ems.energy.service.IScriptQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 脚本查询Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class ScriptQueryServiceImpl implements IScriptQueryService {

    private static final Logger log = LoggerFactory.getLogger(ScriptQueryServiceImpl.class);

    @Autowired
    private ScriptConfigMapper scriptConfigMapper;

    @Autowired
    private List<QueryAdapter> queryAdapters;

    @Autowired
    private ScriptQueryProperties scriptQueryProperties;

    public ScriptQueryProperties getScriptQueryProperties() {
        return scriptQueryProperties;
    }

    @Override
    public ScriptQueryResponse executeQuery(String code, Map<String, Object> params) {
        ScriptQueryResponse response = new ScriptQueryResponse();

        try {
            // 1. 验证参数数量
            if (params != null && params.size() > scriptQueryProperties.getMaxParameterCount()) {
                response.setSuccess(false);
                response.setMessage("参数数量超过限制，最大允许：" + scriptQueryProperties.getMaxParameterCount());
                return response;
            }

            // 2. 权限验证（如果启用）
            if (scriptQueryProperties.isEnablePermissionCheck()) {
                if (!hasScriptExecutePermission(code)) {
                    response.setSuccess(false);
                    response.setMessage("没有执行该脚本的权限：" + code);
                    return response;
                }
            }

            // 3. 根据code查询脚本配置
            ScriptConfig scriptConfig = getScriptConfigByCode(code);
            if (scriptConfig == null) {
                response.setSuccess(false);
                response.setMessage("脚本配置不存在：" + code);
                return response;
            }

            // 3. 获取对应的查询适配器
            QueryAdapter adapter = getQueryAdapter(scriptConfig.getDbType());
            if (adapter == null) {
                response.setSuccess(false);
                response.setMessage("不支持的数据库类型：" + scriptConfig.getDbType());
                return response;
            }

            // 4. 执行查询（带超时控制）
            List<Map<String, Object>> data = executeQueryWithTimeout(adapter, scriptConfig.getScript(), params);

            // 5. 检查结果数量限制
            if (data.size() > scriptQueryProperties.getMaxResultSize()) {
                log.warn("查询结果数量超过限制，code: {}, 结果数量: {}, 限制: {}",
                        code, data.size(), scriptQueryProperties.getMaxResultSize());
                data = data.subList(0, scriptQueryProperties.getMaxResultSize());
                response.setMessage("查询成功，结果已截断到" + scriptQueryProperties.getMaxResultSize() + "条");
            } else {
                response.setMessage("查询成功");
            }

            // 6. 构建响应结果
            response.setSuccess(true);
            response.setData(data);
            response.setTotal(data.size());

            log.info("脚本查询成功，code: {}, 结果数量: {}", code, data.size());

        } catch (TimeoutException e) {
            log.error("脚本查询超时，code: {}", code, e);
            response.setSuccess(false);
            response.setMessage("查询超时");
        } catch (SecurityException e) {
            log.error("脚本安全验证失败，code: {}", code, e);
            response.setSuccess(false);
            response.setMessage("脚本安全验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("脚本查询异常，code: {}", code, e);
            response.setSuccess(false);
            response.setMessage("查询异常：" + e.getMessage());
        }

        return response;
    }

    /**
     * 带超时控制的查询执行
     */
    private List<Map<String, Object>> executeQueryWithTimeout(QueryAdapter adapter, String script, Map<String, Object> params) 
            throws Exception {
        CompletableFuture<List<Map<String, Object>>> future = CompletableFuture.supplyAsync(() -> {
            try {
                return adapter.executeQuery(script, params);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        return future.get(scriptQueryProperties.getQueryTimeout(), TimeUnit.SECONDS);
    }

    /**
     * 获取查询适配器
     */
    private QueryAdapter getQueryAdapter(String dbType) {
        return queryAdapters.stream()
                .filter(adapter -> adapter.getSupportedDbType().equals(dbType))
                .findFirst()
                .orElse(null);
    }

    @Override
    @Cacheable(value = "scriptConfig", key = "#code", condition = "#root.target.scriptQueryProperties.enableCache")
    public ScriptConfig getScriptConfigByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        return scriptConfigMapper.selectScriptConfigByCode(code);
    }

    @Override
    public ScriptConfig getScriptConfigById(Long id) {
        if (id == null) {
            return null;
        }
        return scriptConfigMapper.selectById(id);
    }

    @Override
    public List<ScriptConfig> selectScriptConfigList(ScriptConfig scriptConfig) {
        LambdaQueryWrapper<ScriptConfig> queryWrapper = new LambdaQueryWrapper<>();

        if (scriptConfig.getId() != null) {
            queryWrapper.eq(ScriptConfig::getId, scriptConfig.getId());
        }
        if (StringUtils.isNotEmpty(scriptConfig.getCode())) {
            queryWrapper.like(ScriptConfig::getCode, scriptConfig.getCode());
        }
        if (StringUtils.isNotEmpty(scriptConfig.getDbType())) {
            queryWrapper.eq(ScriptConfig::getDbType, scriptConfig.getDbType());
        }
        if (StringUtils.isNotEmpty(scriptConfig.getDescription())) {
            queryWrapper.like(ScriptConfig::getDescription, scriptConfig.getDescription());
        }

        queryWrapper.orderByDesc(ScriptConfig::getCreatedTime);

        return scriptConfigMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "scriptConfig", key = "#scriptConfig.code", condition = "#root.target.scriptQueryProperties.enableCache")
    public int insertScriptConfig(ScriptConfig scriptConfig) {
        // 验证脚本配置
        if (!validateScriptConfig(scriptConfig)) {
            throw new ServiceException("脚本配置验证失败");
        }

        // 检查代码是否已存在
        ScriptConfig existing = getScriptConfigByCode(scriptConfig.getCode());
        if (existing != null) {
            throw new ServiceException("脚本代码已存在：" + scriptConfig.getCode());
        }

        return scriptConfigMapper.insert(scriptConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "scriptConfig", key = "#scriptConfig.code", condition = "#root.target.scriptQueryProperties.enableCache")
    public int updateScriptConfig(ScriptConfig scriptConfig) {
        // 验证脚本配置
        if (!validateScriptConfig(scriptConfig)) {
            throw new ServiceException("脚本配置验证失败");
        }

        return scriptConfigMapper.updateById(scriptConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteScriptConfigById(Long id) {
        return scriptConfigMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteScriptConfigByIds(Long[] ids) {
        return scriptConfigMapper.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    public boolean validateScriptConfig(ScriptConfig scriptConfig) {
        if (scriptConfig == null) {
            return false;
        }
        
        // 验证必填字段
        if (StringUtils.isEmpty(scriptConfig.getCode()) || 
            StringUtils.isEmpty(scriptConfig.getScript()) || 
            StringUtils.isEmpty(scriptConfig.getDbType())) {
            return false;
        }
        
        // 验证数据库类型
        if (!"MYSQL".equals(scriptConfig.getDbType()) && !"INFLUXDB".equals(scriptConfig.getDbType())) {
            return false;
        }
        
        // 验证脚本安全性
        QueryAdapter adapter = getQueryAdapter(scriptConfig.getDbType());
        if (adapter != null && !adapter.validateScript(scriptConfig.getScript())) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查脚本执行权限
     */
    private boolean hasScriptExecutePermission(String code) {
        // 这里可以集成现有的权限系统
        // 例如检查用户是否有执行特定脚本的权限
        // 目前简单返回true，实际项目中应该根据具体权限系统实现

        // 示例：可以根据脚本代码前缀判断权限
        // if (code.startsWith("ADMIN_")) {
        //     return hasAdminPermission();
        // }

        return true; // 默认允许执行
    }

    @Override
    public Object getQueryProperties() {
        return scriptQueryProperties;
    }
}
