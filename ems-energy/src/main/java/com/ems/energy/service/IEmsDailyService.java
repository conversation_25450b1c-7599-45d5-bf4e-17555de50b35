package com.ems.energy.service;

import com.ems.common.core.domain.AjaxResult;
import com.ems.energy.domain.EmsDaily;
import com.ems.energy.domain.EmsDailyDTO;
import com.ems.energy.domain.EmsReport;

import java.text.ParseException;
import java.util.List;


/**
 * 日报Service接口
 *
 * <AUTHOR>
 * @date 2021-12-28
 */
public interface IEmsDailyService
{
    /**
     * 查询日报
     *
     * @param dailyId 日报主键
     * @return 日报
     */
    EmsDaily selectEmsDailyByDailyId(Long dailyId);

    /**
     * 查询日报列表
     *
     * @param emsDaily 日报
     * @return 日报集合
     */
    List<EmsDaily> selectEmsDailyList(EmsDaily emsDaily);

    /**
     * 新增日报
     *
     * @param emsDaily 日报
     * @return 结果
     */
    int insertEmsDaily(EmsDaily emsDaily);

    /**
     * 修改日报
     *
     * @param emsDaily 日报
     * @return 结果
     */
    int updateEmsDaily(EmsDaily emsDaily);

    /**
     * 批量删除日报
     *
     * @param dailyIds 需要删除的日报主键集合
     * @return 结果
     */
    int deleteEmsDailyByDailyIds(Long[] dailyIds);

    /**
     * 删除日报信息
     *
     * @param dailyId 日报主键
     * @return 结果
     */
    int deleteEmsDailyByDailyId(Long dailyId);

    int selectEmsDailyListTo(EmsDaily emsDaily, EmsReport emsReport);

    List<EmsDailyDTO> selectlistOne(EmsDailyDTO emsDaily);

    AjaxResult selectDaily(EmsDailyDTO emsDailyDTO);


    AjaxResult selectlistPeak(EmsDaily emsDaily,String time) throws ParseException;

    /**
     * 饼图图表
     * @param emsDaily
     * @return
     * @throws ParseException
     */
    AjaxResult selectlistChart(EmsDaily emsDaily,String time) throws ParseException;

}
