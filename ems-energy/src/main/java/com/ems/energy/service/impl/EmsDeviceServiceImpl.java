package com.ems.energy.service.impl;


import com.ems.energy.domain.EmsDevice;
import com.ems.energy.domain.vo.EmsDeviceVo;
import com.ems.energy.mapper.EmsDeviceMapper;
import com.ems.energy.service.IEmsDeviceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工厂设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Service
public class EmsDeviceServiceImpl implements IEmsDeviceService
{
    @Resource
    private EmsDeviceMapper emsDeviceMapper;

    /**
     * 查询工厂设备
     *
     * @param equId 工厂设备主键
     * @return 工厂设备
     */
    @Override
    public EmsDevice selectEmsDeviceByEquId(Long equId)
    {
        return emsDeviceMapper.selectEmsDeviceByEquId(equId);
    }

    /**
     * 查询工厂设备列表
     *
     * @param emsDevice 工厂设备
     * @return 工厂设备
     */
    @Override
    public List<EmsDeviceVo> selectEmsDeviceList(EmsDeviceVo emsDevice)
    {
        return emsDeviceMapper.selectEmsDeviceList(emsDevice);
    }

    /**
     * 新增工厂设备
     *
     * @param emsDevice 工厂设备
     * @return 结果
     */
    @Override
    public int insertEmsDevice(EmsDevice emsDevice)
    {
        return emsDeviceMapper.insertEmsDevice(emsDevice);
    }

    /**
     * 修改工厂设备
     *
     * @param emsDevice 工厂设备
     * @return 结果
     */
    @Override
    public int updateEmsDevice(EmsDevice emsDevice)
    {
        return emsDeviceMapper.updateEmsDevice(emsDevice);
    }

    /**
     * 批量删除工厂设备
     *
     * @param equIds 需要删除的工厂设备主键
     * @return 结果
     */
    @Override
    public int deleteEmsDeviceByEquIds(Long[] equIds)
    {
        return emsDeviceMapper.deleteEmsDeviceByEquIds(equIds);
    }

    /**
     * 删除工厂设备信息
     *
     * @param equId 工厂设备主键
     * @return 结果
     */
    @Override
    public int deleteEmsDeviceByEquId(Long equId)
    {
        return emsDeviceMapper.deleteEmsDeviceByEquId(equId);
    }
}
