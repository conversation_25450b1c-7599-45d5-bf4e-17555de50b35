package com.ems.energy.service.impl;

import com.ems.common.utils.DateUtils;
import com.ems.energy.domain.EmsMonthly;
import com.ems.energy.mapper.EmsMonthlyMapper;
import com.ems.energy.service.IEmsMonthlyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 月报Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Service
public class EmsMonthlyServiceImpl implements IEmsMonthlyService
{
    @Resource
    private EmsMonthlyMapper emsMonthlyMapper;

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 查询月报
     *
     * @param dailyId 月报主键
     * @return 月报
     */
    @Override
    public EmsMonthly selectEmsMonthlyByDailyId(Long dailyId)
    {
        return emsMonthlyMapper.selectEmsMonthlyByDailyId(dailyId);
    }

    /**
     * 查询月报列表
     *
     * @param emsMonthly 月报
     * @return 月报
     */
    @Override
    public List<EmsMonthly> selectEmsMonthlyList(EmsMonthly emsMonthly)
    {
        return emsMonthlyMapper.selectEmsMonthlyList(emsMonthly);
    }

    /**
     * 新增月报
     *
     * @param emsMonthly 月报
     * @return 结果
     */
    @Override
    public int insertEmsMonthly(EmsMonthly emsMonthly)
    {
        emsMonthly.setCreateTime(DateUtils.getNowDate());
        return emsMonthlyMapper.insertEmsMonthly(emsMonthly);
    }

    /**
     * 修改月报
     *
     * @param emsMonthly 月报
     * @return 结果
     */
    @Override
    public int updateEmsMonthly(EmsMonthly emsMonthly)
    {
        return emsMonthlyMapper.updateEmsMonthly(emsMonthly);
    }

    /**
     * 批量删除月报
     *
     * @param dailyIds 需要删除的月报主键
     * @return 结果
     */
    @Override
    public int deleteEmsMonthlyByDailyIds(Long[] dailyIds)
    {
        return emsMonthlyMapper.deleteEmsMonthlyByDailyIds(dailyIds);
    }

    /**
     * 删除月报信息
     *
     * @param dailyId 月报主键
     * @return 结果
     */
    @Override
    public int deleteEmsMonthlyByDailyId(Long dailyId)
    {
        return emsMonthlyMapper.deleteEmsMonthlyByDailyId(dailyId);
    }
}
