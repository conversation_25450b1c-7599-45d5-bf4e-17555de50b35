package com.ems.energy.service;

import java.util.List;
import com.ems.energy.domain.EmsPart;

/**
 * 产品管理Service接口
 *
 * <AUTHOR>
 * @date 2022-03-07
 */
public interface IEmsPartService
{
    /**
     * 查询产品管理
     *
     * @param partId 产品管理主键
     * @return 产品管理
     */
    EmsPart selectEmsPartByPartId(Long partId);

    /**
     * 查询产品管理列表
     *
     * @param emsPart 产品管理
     * @return 产品管理集合
     */
    List<EmsPart> selectEmsPartList(EmsPart emsPart);

    /**
     * 新增产品管理
     *
     * @param emsPart 产品管理
     * @return 结果
     */
    int insertEmsPart(EmsPart emsPart);

    /**
     * 修改产品管理
     *
     * @param emsPart 产品管理
     * @return 结果
     */
    int updateEmsPart(EmsPart emsPart);

    /**
     * 批量删除产品管理
     *
     * @param partIds 需要删除的产品管理主键集合
     * @return 结果
     */
    int deleteEmsPartByPartIds(Long[] partIds);

    /**
     * 删除产品管理信息
     *
     * @param partId 产品管理主键
     * @return 结果
     */
    int deleteEmsPartByPartId(Long partId);
}
