package com.ems.energy.service;

import java.util.List;

import com.ems.energy.domain.EmsMeasure;
import com.ems.energy.domain.EmsMesuringTool;
import com.ems.energy.domain.tree.Emstreecenter;

/**
 * 表具管理Service接口
 *
 * <AUTHOR>
 * @date 2022-03-06
 */
public interface IEmsMesuringToolService
{
    /**
     * 查询表具管理
     *
     * @param measuringToolId 表具管理主键
     * @return 表具管理
     */
    EmsMesuringTool selectEmsMesuringToolByMeasuringToolId(Long measuringToolId);

    /**
     * 查询表具管理列表
     *
     * @param emsMesuringTool 表具管理
     * @return 表具管理集合
     */
    List<EmsMesuringTool> selectEmsMesuringToolList(EmsMesuringTool emsMesuringTool);

    /**
     * 新增表具管理
     *
     * @param emsMesuringTool 表具管理
     * @return 结果
     */
    int insertEmsMesuringTool(EmsMesuringTool emsMesuringTool);

    /**
     * 修改表具管理
     *
     * @param emsMesuringTool 表具管理
     * @return 结果
     */
    int updateEmsMesuringTool(EmsMesuringTool emsMesuringTool);

    /**
     * 批量删除表具管理
     *
     * @param measuringToolIds 需要删除的表具管理主键集合
     * @return 结果
     */
    int deleteEmsMesuringToolByMeasuringToolIds(Long[] measuringToolIds);

    /**
     * 删除表具管理信息
     *
     * @param measuringToolId 表具管理主键
     * @return 结果
     */
    int deleteEmsMesuringToolByMeasuringToolId(Long measuringToolId);

    List<Emstreecenter> buildEmsMeasureTreeSelect(List<EmsMesuringTool> emsMesuringTools);

    List<EmsMesuringTool> buildEmsMeasureTree(List<EmsMesuringTool> emsMesuringTools);
}
