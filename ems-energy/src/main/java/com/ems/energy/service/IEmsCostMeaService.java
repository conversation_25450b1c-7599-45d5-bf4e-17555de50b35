package com.ems.energy.service;


import com.ems.energy.domain.EmsCostMea;

import java.util.List;

/**
 * 成本中心和计量器具中间Service接口
 * <AUTHOR>
 * @date 2022-01-04
 */
public interface IEmsCostMeaService
{
    /**
     * 查询成本中心和计量器具中间
     *
     * @param id 成本中心和计量器具中间主键
     * @return 成本中心和计量器具中间
     */
    EmsCostMea selectEmsCostMeaById(Long id);

    /**
     * 查询成本中心和计量器具中间列表
     * @param emsCostMea 成本中心和计量器具中间
     * @return 成本中心和计量器具中间集合
     */
    List<EmsCostMea> selectEmsCostMeaList(EmsCostMea emsCostMea);

    /**
     * 新增成本中心和计量器具中间
     * @param emsCostMea 成本中心和计量器具中间
     * @return 结果
     */
    int insertEmsCostMea(EmsCostMea emsCostMea);

    /**
     * 修改成本中心和计量器具中间
     * @param emsCostMea 成本中心和计量器具中间
     * @return 结果
     */
    int updateEmsCostMea(EmsCostMea emsCostMea);

    /**
     * 批量删除成本中心和计量器具中间
     * @param ids 需要删除的成本中心和计量器具中间主键集合
     * @return 结果
     */
    int deleteEmsCostMeaByIds(Long[] ids);

    /**
     * 删除成本中心和计量器具中间信息
     * @param id 成本中心和计量器具中间主键
     * @return 结果
     */
    int deleteEmsCostMeaById(Long id);

    int deleteEmsCostMeaByCostCenterIds(Long[] centerIds);
}
