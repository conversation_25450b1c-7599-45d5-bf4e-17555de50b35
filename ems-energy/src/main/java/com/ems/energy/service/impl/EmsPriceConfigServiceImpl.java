package com.ems.energy.service.impl;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import com.ems.common.utils.DateUtils;
import com.ems.energy.constant.EmsConstants;
import com.ems.energy.domain.EmsPricePeriod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.ems.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ems.energy.domain.EmsPrice;
import com.ems.energy.mapper.EmsPriceConfigMapper;
import com.ems.energy.domain.EmsPriceConfig;
import com.ems.energy.service.IEmsPriceConfigService;

/**
 * 电价配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-30
 */
@Service
public class EmsPriceConfigServiceImpl implements IEmsPriceConfigService
{
    @Autowired
    private EmsPriceConfigMapper emsPriceConfigMapper;

    /**
     * 查询电价配置
     *
     * @param configId 电价配置主键
     * @return 电价配置
     */
    @Override
    public EmsPriceConfig selectEmsPriceConfigByConfigId(Long configId)
    {
        return emsPriceConfigMapper.selectEmsPriceConfigByConfigId(configId);
    }

    /**
     * 查询电价配置列表
     *
     * @param emsPriceConfig 电价配置
     * @return 电价配置
     */
    @Override
    public List<EmsPriceConfig> selectEmsPriceConfigList(EmsPriceConfig emsPriceConfig)
    {
        return emsPriceConfigMapper.selectEmsPriceConfigList(emsPriceConfig);
    }

    /**
     * 新增电价配置
     *
     * @param emsPriceConfig 电价配置
     * @return 结果
     */
    @Transactional
    @Override
    public int insertEmsPriceConfig(EmsPriceConfig emsPriceConfig)
    {
        emsPriceConfig.setCreateTime(DateUtils.getNowDate());
        int rows = emsPriceConfigMapper.insertEmsPriceConfig(emsPriceConfig);
        insertEmsPrice(emsPriceConfig);
        insertEmsPricePeriod(emsPriceConfig);
        return rows;
    }

    /**
     * 修改电价配置
     *
     * @param emsPriceConfig 电价配置
     * @return 结果
     */
    @Transactional
    @Override
    public int updateEmsPriceConfig(EmsPriceConfig emsPriceConfig)
    {
        emsPriceConfig.setUpdateTime(DateUtils.getNowDate());
        emsPriceConfigMapper.deleteEmsPriceByConfigId(emsPriceConfig.getConfigId());
        emsPriceConfigMapper.deleteEmsPricePeriodByConfigId(emsPriceConfig.getConfigId());
        insertEmsPrice(emsPriceConfig);
        insertEmsPricePeriod(emsPriceConfig);
        return emsPriceConfigMapper.updateEmsPriceConfig(emsPriceConfig);
    }

    /**
     * 批量删除电价配置
     *
     * @param configIds 需要删除的电价配置主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteEmsPriceConfigByConfigIds(Long[] configIds)
    {
        emsPriceConfigMapper.deleteEmsPriceByConfigIds(configIds);
        emsPriceConfigMapper.deleteEmsPricePeriodByConfigIds(configIds);
        return emsPriceConfigMapper.deleteEmsPriceConfigByConfigIds(configIds);
    }

    /**
     * 删除电价配置信息
     *
     * @param configId 电价配置主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteEmsPriceConfigByConfigId(Long configId)
    {
        emsPriceConfigMapper.deleteEmsPriceByConfigId(configId);
        emsPriceConfigMapper.deleteEmsPricePeriodByConfigId(configId);
        return emsPriceConfigMapper.deleteEmsPriceConfigByConfigId(configId);
    }

    @Override
    public EmsPriceConfig selectValidEmsPriceConfigByTime(Date time) {
        return emsPriceConfigMapper.selectValidEmsPriceConfigByTime(time);
    }

    @Override
    public BigDecimal getElecPrice(Date time) {
        EmsPrice price = getPriceByTime(time);
        if (null != price){
            return price.getPrice();
        }
        return new BigDecimal(0);
    }

    @Override
    public EmsPrice getPriceByTime(Date time) {
        EmsPriceConfig priceConfig = selectValidEmsPriceConfigByTime(time);
        if (null != priceConfig){
            String category = getCategory(priceConfig, time);
            for (EmsPrice price : priceConfig.getEmsPriceList()){
                if (price.getCategory().equals(category)){
                    return price;
                }
            }
        }
        return null;
    }

    @Override
    public String getCategory(Date time) {
        EmsPriceConfig priceConfig = selectValidEmsPriceConfigByTime(time);
        if (null != priceConfig) {
            return getCategory(priceConfig, time);
        }
        return null;
    }

    private String getCategory(EmsPriceConfig priceConfig, Date time){
        long minutes = DateUtils.getFragmentInMinutes(time, Calendar.DATE);
        String type = getTypeTag(time);
        for (EmsPricePeriod period : priceConfig.getEmsPricePeriodList()){
            if (type.equals(period.getType())){
                if (minutes>=period.getStart() && minutes < period.getEnd()){
                    return period.getCategory();
                }
            }
        }
        return "";
    }

    private String getTypeTag(Date time){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        int month = calendar.get(Calendar.MONTH);
        return (month>=7 && month<=9)? EmsConstants.SUMMER:EmsConstants.NONSUMER;
    }

    /**
     * 新增价格信息
     *
     * @param emsPriceConfig 电价配置对象
     */
    public void insertEmsPrice(EmsPriceConfig emsPriceConfig)
    {
        List<EmsPrice> emsPriceList = emsPriceConfig.getEmsPriceList();
        Long configId = emsPriceConfig.getConfigId();
        if (StringUtils.isNotNull(emsPriceList))
        {
            List<EmsPrice> list = new ArrayList<EmsPrice>();
            for (EmsPrice emsPrice : emsPriceList)
            {
                emsPrice.setConfigId(configId);
                emsPrice.setCreateTime(new Date());
                list.add(emsPrice);
            }
            if (list.size() > 0)
            {
                emsPriceConfigMapper.batchEmsPrice(list);
            }
        }
    }

    public void insertEmsPricePeriod(EmsPriceConfig emsPriceConfig)
    {
        List<EmsPricePeriod> emsPricePeriodList = emsPriceConfig.getEmsPricePeriodList();
        Long configId = emsPriceConfig.getConfigId();
        if (StringUtils.isNotNull(emsPricePeriodList))
        {
            List<EmsPricePeriod> list = new ArrayList<EmsPricePeriod>();
            for (EmsPricePeriod emsPricePeriod : emsPricePeriodList)
            {
                emsPricePeriod.setConfigId(configId);
                emsPricePeriod.setCreateTime(new Date());
                list.add(emsPricePeriod);
            }
            if (list.size() > 0)
            {
                emsPriceConfigMapper.batchEmsPricePeriod(list);
            }
        }
    }
}
