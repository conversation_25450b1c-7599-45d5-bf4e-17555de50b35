package com.ems.energy.service;

import java.util.List;
import com.ems.energy.domain.EmsEquipment;

/**
 * 设备管理Service接口
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
public interface IEmsEquipmentService
{
    /**
     * 查询设备管理
     *
     * @param equipmentId 设备管理主键
     * @return 设备管理
     */
    EmsEquipment selectEmsEquipmentByEquipmentId(Long equipmentId);

    /**
     * 查询设备管理列表
     *
     * @param emsEquipment 设备管理
     * @return 设备管理集合
     */
    List<EmsEquipment> selectEmsEquipmentList(EmsEquipment emsEquipment);

    /**
     * 新增设备管理
     *
     * @param emsEquipment 设备管理
     * @return 结果
     */
    int insertEmsEquipment(EmsEquipment emsEquipment);

    /**
     * 修改设备管理
     *
     * @param emsEquipment 设备管理
     * @return 结果
     */
    int updateEmsEquipment(EmsEquipment emsEquipment);

    /**
     * 批量删除设备管理
     *
     * @param equipmentIds 需要删除的设备管理主键集合
     * @return 结果
     */
    int deleteEmsEquipmentByEquipmentIds(Long[] equipmentIds);

    /**
     * 删除设备管理信息
     *
     * @param equipmentId 设备管理主键
     * @return 结果
     */
    int deleteEmsEquipmentByEquipmentId(Long equipmentId);

    public List<EmsEquipment> selectEquipmentByTag(String tag);
}
