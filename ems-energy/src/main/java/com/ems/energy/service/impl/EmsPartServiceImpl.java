package com.ems.energy.service.impl;

import java.util.List;
import com.ems.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ems.energy.mapper.EmsPartMapper;
import com.ems.energy.domain.EmsPart;
import com.ems.energy.service.IEmsPartService;

/**
 * 产品管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-07
 */
@Service
public class EmsPartServiceImpl implements IEmsPartService
{
    @Autowired
    private EmsPartMapper emsPartMapper;

    /**
     * 查询产品管理
     *
     * @param partId 产品管理主键
     * @return 产品管理
     */
    @Override
    public EmsPart selectEmsPartByPartId(Long partId)
    {
        return emsPartMapper.selectEmsPartByPartId(partId);
    }

    /**
     * 查询产品管理列表
     *
     * @param emsPart 产品管理
     * @return 产品管理
     */
    @Override
    public List<EmsPart> selectEmsPartList(EmsPart emsPart)
    {
        return emsPartMapper.selectEmsPartList(emsPart);
    }

    /**
     * 新增产品管理
     *
     * @param emsPart 产品管理
     * @return 结果
     */
    @Override
    public int insertEmsPart(EmsPart emsPart)
    {
        emsPart.setCreateTime(DateUtils.getNowDate());
        return emsPartMapper.insertEmsPart(emsPart);
    }

    /**
     * 修改产品管理
     *
     * @param emsPart 产品管理
     * @return 结果
     */
    @Override
    public int updateEmsPart(EmsPart emsPart)
    {
        emsPart.setUpdateTime(DateUtils.getNowDate());
        return emsPartMapper.updateEmsPart(emsPart);
    }

    /**
     * 批量删除产品管理
     *
     * @param partIds 需要删除的产品管理主键
     * @return 结果
     */
    @Override
    public int deleteEmsPartByPartIds(Long[] partIds)
    {
        return emsPartMapper.deleteEmsPartByPartIds(partIds);
    }

    /**
     * 删除产品管理信息
     *
     * @param partId 产品管理主键
     * @return 结果
     */
    @Override
    public int deleteEmsPartByPartId(Long partId)
    {
        return emsPartMapper.deleteEmsPartByPartId(partId);
    }
}
