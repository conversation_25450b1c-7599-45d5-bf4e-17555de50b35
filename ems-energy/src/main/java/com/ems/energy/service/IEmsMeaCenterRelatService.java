package com.ems.energy.service;

import com.ems.energy.domain.EmsMeaCenterRelat;
import com.ems.energy.domain.EmsMeaCenterRelatListDTO;
import com.ems.energy.domain.vo.EmsMeaCenterRelatVo;

import java.util.List;

/**
 * 计量器具和成本中心关系Service接口
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface IEmsMeaCenterRelatService
{
    /**
     * 查询计量器具和成本中心关系
     *
     * @param relatId 计量器具和成本中心关系主键
     * @return 计量器具和成本中心关系
     */
    EmsMeaCenterRelat selectEmsMeaCenterRelatByRelatId(Long relatId);

    /**
     * 查询计量器具和成本中心关系列表
     *
     * @param emsMeaCenterRelat 计量器具和成本中心关系
     * @return 计量器具和成本中心关系集合
     */
    List<EmsMeaCenterRelat> selectEmsMeaCenterRelatList(EmsMeaCenterRelat emsMeaCenterRelat);

    /**
     * 新增计量器具和成本中心关系
     *
     * @param emsMeaCenterRelat 计量器具和成本中心关系
     * @return 结果
     */
    int insertEmsMeaCenterRelat(EmsMeaCenterRelatListDTO emsMeaCenterRelat);

    /**
     * 修改计量器具和成本中心关系
     *
     * @param emsMeaCenterRelat 计量器具和成本中心关系
     * @return 结果
     */
    int updateEmsMeaCenterRelat(EmsMeaCenterRelatListDTO emsMeaCenterRelat);

    /**
     * 批量删除计量器具和成本中心关系
     *
     * @param relatIds 需要删除的计量器具和成本中心关系主键集合
     * @return 结果
     */
    int deleteEmsMeaCenterRelatByRelatIds(Long[] relatIds);

    /**
     * 删除计量器具和成本中心关系信息
     *
     * @param relatId 计量器具和成本中心关系主键
     * @return 结果
     */
    int deleteEmsMeaCenterRelatByRelatId(Long relatId);

    List<EmsMeaCenterRelatVo> selectEmsMeaCenterRelatpagelist(EmsMeaCenterRelatListDTO emsMeaCenterRelat);


}
