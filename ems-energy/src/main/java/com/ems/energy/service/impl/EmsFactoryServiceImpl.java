package com.ems.energy.service.impl;

import com.ems.common.constant.UserConstants;
import com.ems.common.exception.ServiceException;
import com.ems.common.utils.DateUtils;
import com.ems.common.utils.StringUtils;
import com.ems.energy.constant.EnergyConstant;
import com.ems.energy.domain.EmsFactory;
import com.ems.energy.domain.tree.Emstreefactory;
import com.ems.energy.mapper.EmsFactoryMapper;
import com.ems.energy.service.IEmsFactoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业架构Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Service
public class EmsFactoryServiceImpl implements IEmsFactoryService

{


    @Resource
    private EmsFactoryMapper emsFactoryMapper;

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询企业架构
     *
     * @param deptId 企业架构主键
     * @return 企业架构
     */
    @Override
    public EmsFactory selectEmsFactoryByDeptId(Long deptId)
    {
        return emsFactoryMapper.selectEmsFactoryByDeptId(deptId);
    }

    /**
     * 查询企业架构列表
     *
     * @param emsFactory 企业架构
     * @return 企业架构
     */
    @Override
    public List<EmsFactory> selectEmsFactoryList(EmsFactory emsFactory)
    {
        return emsFactoryMapper.selectEmsFactoryList(emsFactory);
    }

    /**
     * 新增企业架构
     *
     * @param emsFactory 企业架构
     * @return 结果
     */
    @Override
    public int insertEmsFactory(EmsFactory emsFactory)
    {
        emsFactory.setCreateTime(DateUtils.getNowDate());
        return emsFactoryMapper.insertEmsFactory(emsFactory);
    }

    /**
     * 修改企业架构
     *
     * @param emsFactory 企业架构
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEmsFactory(EmsFactory emsFactory)
    {
        emsFactory.setUpdateTime(DateUtils.getNowDate());
        return emsFactoryMapper.updateEmsFactory(emsFactory);
    }

    /**
     * 批量删除企业架构
     *
     * @param deptIds 需要删除的企业架构主键
     * @return 结果
     */
    @Override
    public int deleteEmsFactoryByDeptIds(Long[] deptIds)
    {
        return emsFactoryMapper.deleteEmsFactoryByDeptIds(deptIds);
    }

    /**
     * 删除企业架构信息
     *
     * @param deptId 企业架构主键
     * @return 结果
     */
    @Override
    public int deleteEmsFactoryByDeptId(Long deptId)
    {
        return emsFactoryMapper.deleteEmsFactoryByDeptId(deptId);
    }

    /**
     * 构建前端所需要是下拉数结构
     * @param emsFactories 企业列表
     * @return 下拉列表
     */

    @Override
    public List<Emstreefactory> buildFactoryselect(List<EmsFactory> emsFactories) {
        List<EmsFactory> factory= builFacatoryselect(emsFactories);
        return factory.stream().map(Emstreefactory::new).collect(Collectors.toList());
    }

    /**
     *  判断企业是否存在
     * @param emsFactory 企业学习
     * @return 结果
     */
    @Override
    public String checkDeptNameUnique(EmsFactory emsFactory) {
        Long deptId = StringUtils.isNull(emsFactory.getDeptId()) ? -1L : emsFactory.getDeptId();
        EmsFactory info = emsFactoryMapper.checkDeptNameUnique(emsFactory.getDeptName(), emsFactory.getParentId());
        if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 企业架构添加
     * @param emsFactory 企业实体类
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertFactory(EmsFactory emsFactory) {

        EmsFactory info = emsFactoryMapper.selectFactoryById(emsFactory.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus()))
        {
            throw new ServiceException("部门停用，不允许新增");
        }
        emsFactory.setAncestors(info.getAncestors() + EnergyConstant.BASE_SEPARATOR + emsFactory.getParentId());
        //添加逻辑删除属性
        emsFactory.setDelFlag(EnergyConstant.NORMAL_DEL_FLAG);
        return emsFactoryMapper.insertEmsFactory(emsFactory);
    }


    @Override
    public List<EmsFactory> builFacatoryselect(List<EmsFactory> emsFactories)
    {
        List<EmsFactory> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<>();
        for (EmsFactory emsFactory : emsFactories)
        {
            tempList.add(emsFactory.getDeptId());
        }
        for (EmsFactory emsFactory : emsFactories) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(emsFactory.getParentId())) {
                recursionFn(emsFactories, emsFactory);
                returnList.add(emsFactory);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = emsFactories;
        }
        return returnList;
    }

    private void recursionFn(List<EmsFactory> emsFactories, EmsFactory fatory) {
        // 得到子节点列表
        List<EmsFactory> childList = getChildList(emsFactories, fatory);
        fatory.setChildren(childList);
        for (EmsFactory tChild : childList)
        {
            if (hasChild(emsFactories, tChild))
            {
                recursionFn(emsFactories, tChild);
            }
        }
    }
    private boolean hasChild(List<EmsFactory> emsFactories, EmsFactory emsFactory) {
        return getChildList(emsFactories, emsFactory).size() > 0;
    }

    private List<EmsFactory> getChildList(List<EmsFactory> emsFactories, EmsFactory fatory) {
        List<EmsFactory> tlist = new ArrayList<>();
        for (EmsFactory n : emsFactories) {
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == fatory.getDeptId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }
}
