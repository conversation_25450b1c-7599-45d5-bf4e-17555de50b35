package com.ems.energy.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ems.common.constant.ErrorConstant;
import com.ems.common.enums.KeepType;
import com.ems.common.exception.ServiceException;
import com.ems.energy.constant.DataConstant;
import com.ems.energy.constant.EnergyConstant;
import com.ems.energy.domain.EmsCostCenter;
import com.ems.energy.domain.EmsFactory;
import com.ems.energy.domain.EmsMeaCenterRelat;
import com.ems.energy.domain.EmsMeaCenterRelatListDTO;
import com.ems.energy.domain.EmsMeaCenterRelatListPO;
import com.ems.energy.domain.vo.EmsMeaCenterRelatVo;
import com.ems.energy.mapper.EmsCostCenterMapper;
import com.ems.energy.mapper.EmsFactoryMapper;
import com.ems.energy.mapper.EmsMeaCenterRelatMapper;
import com.ems.energy.mapper.EmsMeasureMapper;
import com.ems.energy.service.IEmsMeaCenterRelatService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 计量器具和成本中心关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Service
public class EmsMeaCenterRelatServiceImpl implements IEmsMeaCenterRelatService
{


    @Resource
    private EmsMeaCenterRelatMapper emsMeaCenterRelatMapper;
    @Resource
    private EmsMeasureMapper emsMeasureMapper;
    @Resource
    private EmsFactoryMapper emsFactoryMapper;
    @Resource
    private EmsCostCenterMapper costCenterMapper;

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询计量器具和成本中心关系
     *
     * @param relatId 计量器具和成本中心关系主键
     * @return 计量器具和成本中心关系
     */
    @Override
    public EmsMeaCenterRelat selectEmsMeaCenterRelatByRelatId(Long relatId)
    {
        EmsMeaCenterRelat relat = emsMeaCenterRelatMapper.selectEmsMeaCenterRelatByRelatId(relatId);
        if(StringUtils.isEmpty(relat.getRelationExpression())){
            log.info(ErrorConstant.RELAT_IS_NULL);
            return null;
        }
        //判断算式内是否有常量
        if(checkFormulaHaveConstant(relat.getRelationExpression())){
            //有常量的情况下,需要处理数据库中表达式逗号后面的常量,返回numberList给前端
            String[] split = relat.getRelationExpression().split(EnergyConstant.BASE_SEPARATOR);
            String[] numberArr = split[1].split(EnergyConstant.EMPTY_STR);
            relat.setRelationExpression(split[0]);
            relat.setNumberList(Arrays.stream(numberArr).map(Integer::new).collect(Collectors.toList()));
        }
        return relat;
    }

    /**
     * 判断表达式是否有常量
     * @param expression
     * @return
     */
    public boolean checkFormulaHaveConstant(String expression){
        int i = expression.indexOf(DataConstant.FORMAT_D);
        return i != -1;
    }
    /**
     * 查询计量器具和成本中心关系列表
     *
     * @param emsMeaCenterRelat 计量器具和成本中心关系
     * @return 计量器具和成本中心关系
     */
    @Override
    public List<EmsMeaCenterRelat> selectEmsMeaCenterRelatList(EmsMeaCenterRelat emsMeaCenterRelat)
    {
        return emsMeaCenterRelatMapper.selectEmsMeaCenterRelatList(emsMeaCenterRelat);
    }

    /**
     * TODO: 通过自定义名称这个标签可以作为搜索条件去搜索
     * 新增计量器具和成本中心关系
     * 不需要判断维护类别是自定义还是成本中心,自定义名称只是维护关系的标签
      * @param emsMeaCenterRelat 计量器具和成本中心关系
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertEmsMeaCenterRelat(EmsMeaCenterRelatListDTO emsMeaCenterRelat)
    {
        //添加逻辑删除属性
        emsMeaCenterRelat.setDelFlag(EnergyConstant.NORMAL_DEL_FLAG);
        //设置维护类别
        emsMeaCenterRelat.setKeepType(KeepType.COST_CENTER.getStatus());
        if(StringUtils.isEmpty(emsMeaCenterRelat.getRelationExpression())){
            log.info(ErrorConstant.RELAT_IS_NULL);
            return 0;
        }
        String expression = emsMeaCenterRelat.getRelationExpression();
        //检验前端发送过来的算式是否正确
        if(checkFormula(expression,emsMeaCenterRelat)){
            log.info(ErrorConstant.FORMULA_IS_INVALID);
            return 0;
        }
        //生成计量器具关系编号
        getRelatCode(emsMeaCenterRelat);
        //判断算式内是否有常量
        if(checkFormulaHaveConstant(expression,emsMeaCenterRelat)){
            EmsMeaCenterRelatListPO po = dealWithConstant(emsMeaCenterRelat, expression);
            return emsMeaCenterRelatMapper.insertEmsMeaCenterRelat(po);
        }
        //检验算式内的计量器具id是否存在
        if(checkMeasureIsExist(expression)){
            log.info(ErrorConstant.MEASURE_NOT_EXIST);
            return 0;
        }
        //构建持久化对象
        EmsMeaCenterRelatListPO po = buildMeaCenterRelatPo(emsMeaCenterRelat);
        return emsMeaCenterRelatMapper.insertEmsMeaCenterRelat(po);
    }

    /**
     * 生成计量器具关系编号
     * @param emsMeaCenterRelat
     */
    private void getRelatCode(EmsMeaCenterRelatListDTO emsMeaCenterRelat) {
        // 计量器具关系编号生成规则: 企业名称缩写 + 成本中心名称缩写 + 序号
        // 根据企业id查询企业名称缩写
        String factorySubName = getFactorySubName(emsMeaCenterRelat.getFactoryFirmId());
        if(StringUtils.isEmpty(factorySubName)){
            throw new ServiceException(ErrorConstant.BUSINESS_IS_INVALID);
        }
        int count = emsMeaCenterRelatMapper.selectCount();
        // 根据成本中心id查询成本中心名称缩写
        String costCenterName = getCostCenterSubName(emsMeaCenterRelat.getCostCenterId());
        if(StringUtils.isEmpty(costCenterName)){
            throw new ServiceException(ErrorConstant.COST_CENTER_NOT_EXIST);
        }
        //设置计量器具关系编号
        emsMeaCenterRelat.setRelatCode(factorySubName
                + EnergyConstant.CODE_BASE_SEPARATOR
                + costCenterName
                + EnergyConstant.CODE_BASE_SEPARATOR
                +String.format(DataConstant.NUMBER_03D,count+1));
    }

    /**
     * 对象转换
     * @param emsMeaCenterRelat
     * @return
     */
    private EmsMeaCenterRelatListPO buildMeaCenterRelatPo(EmsMeaCenterRelatListDTO emsMeaCenterRelat) {
        EmsMeaCenterRelatListPO po = new EmsMeaCenterRelatListPO();
        String recordList = JSON.toJSONString(emsMeaCenterRelat.getRecordList());
        BeanUtils.copyProperties(emsMeaCenterRelat, po);
        po.setRecordList(recordList);
        return po;
    }

    /**
     * 处理关系表达式内的常量
     * @param emsMeaCenterRelat
     * @param expression
     * @return
     */
    private EmsMeaCenterRelatListPO dealWithConstant(EmsMeaCenterRelatListDTO emsMeaCenterRelat, String expression) {
        StringBuilder sb = new StringBuilder();
        sb.append(expression).append(EnergyConstant.BASE_SEPARATOR);
        emsMeaCenterRelat.getNumberList().forEach(sb::append);
        emsMeaCenterRelat.setRelationExpression(sb.toString());
        return buildMeaCenterRelatPo(emsMeaCenterRelat);
    }

    /**
     * 获取成本中心的缩写名称
     * @param costCenterId
     * @return
     */
    private String getCostCenterSubName(Long costCenterId) {
        LambdaQueryWrapper<EmsCostCenter> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmsCostCenter::getCenterId,costCenterId);
        EmsCostCenter center = costCenterMapper.selectOne(queryWrapper);
        if(ObjectUtils.isEmpty(center)){
            return null;
        }
        return center.getSubName();
    }

    /**
     * 获取企业的缩写名称
     * @param factoryFirmId
     * @return
     */
    private String getFactorySubName(Long factoryFirmId) {
        EmsFactory factory = emsFactoryMapper.selectFactoryById(factoryFirmId);
        if(ObjectUtils.isEmpty(factory) || StringUtils.isEmpty(factory.getSubName())){
            return null;
        }
        return factory.getSubName();
    }

    /**
     * 判断算式中是否有常量存在
     * @param expression
     * @param emsMeaCenterRelat
     * @return
     */
    private boolean checkFormulaHaveConstant(String expression, EmsMeaCenterRelatListDTO emsMeaCenterRelat) {
        //判断条件，前端传来的数字列表为空并且算式内搜索不到%d的符号
        if(CollectionUtils.isEmpty(emsMeaCenterRelat.getNumberList())){
            return false;
        }
        int i = expression.indexOf(DataConstant.FORMAT_D);
        return i != -1;
    }

    /**
     * 检验算式是否合法
     * @param expression
     * @param dto
     * @return
     */
    private boolean checkFormula(String expression, EmsMeaCenterRelatListDTO dto) {
        ScriptEngineManager engineManager = new ScriptEngineManager();
        ScriptEngine js = engineManager.getEngineByName(EnergyConstant.JS);
        try {
            String checkExpression;
            if(CollectionUtils.isNotEmpty(dto.getNumberList())){
                checkExpression = String.format(expression,dto.getNumberList().toArray());
            }else{
                checkExpression = expression;
            }
            js.eval(checkExpression);
        } catch (ScriptException e) {
            log.info(ErrorConstant.FORMULA_IS_INVALID);
            return true;
        }
        return false;
    }

    /**
     * 检验计量器具id是否存在
     * @param expression
     * @return
     */
    private boolean checkMeasureIsExist(String expression) {
        //匹配指定范围内的数字
        Pattern p = Pattern.compile(EnergyConstant.NUMBER_ROUND_REG);
        Matcher m = p.matcher(expression);
        List<String> ids = new ArrayList<>();
        while(m.find()) {
            ids.add(m.group());
        }
        ids = ids.stream().distinct().collect(Collectors.toList());
        int i = emsMeasureMapper.countEmsMeaCenterRelatByIds(ids);
        return ids.size() != i;
    }

    /**
     * 修改计量器具和成本中心关系
     * @param emsMeaCenterRelat 计量器具和成本中心关系
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEmsMeaCenterRelat(EmsMeaCenterRelatListDTO emsMeaCenterRelat)
    {
        //自定义名称 和 能源成本中心 二选一 进行判断
        if (StringUtils.isEmpty(emsMeaCenterRelat.getCustName()) && ObjectUtils.isEmpty(emsMeaCenterRelat.getCostCenterId())){
            return 0;
        }
        if(StringUtils.isEmpty(emsMeaCenterRelat.getRelationExpression())){
            log.info(ErrorConstant.RELAT_IS_NULL);
            return 0;
        }
        String expression = emsMeaCenterRelat.getRelationExpression();
        //检验前端发送过来的算式是否正确
        if(checkFormula(expression,emsMeaCenterRelat)){
            log.info(ErrorConstant.FORMULA_IS_INVALID);
            return 0;
        }
        //检验算式内的计量器具id是否存在
        if(checkMeasureIsExist(expression)){
            log.info(ErrorConstant.MEASURE_NOT_EXIST);
            return 0;
        }
        EmsMeaCenterRelatListPO po = new EmsMeaCenterRelatListPO();
        //判断算式内是否有常量
        if(checkFormulaHaveConstant(expression,emsMeaCenterRelat)){
            StringBuilder sb = new StringBuilder();
            sb.append(expression).append(EnergyConstant.BASE_SEPARATOR);
            emsMeaCenterRelat.getNumberList().forEach(sb::append);
            emsMeaCenterRelat.setRelationExpression(sb.toString());
            BeanUtils.copyProperties(emsMeaCenterRelat,po);
            po.setRecordList(JSON.toJSONString(emsMeaCenterRelat.getRecordList()));
            return emsMeaCenterRelatMapper.updateEmsMeaCenterRelat(po);
        }
        BeanUtils.copyProperties(emsMeaCenterRelat,po);
        po.setRecordList(JSON.toJSONString(emsMeaCenterRelat.getRecordList()));
        return emsMeaCenterRelatMapper.updateEmsMeaCenterRelat(po);
    }

    /**
     * 批量删除计量器具和成本中心关系
     *
     * @param relatIds 需要删除的计量器具和成本中心关系主键
     * @return 结果
     */
    @Override
    public int deleteEmsMeaCenterRelatByRelatIds(Long[] relatIds)
    {
        return emsMeaCenterRelatMapper.deleteEmsMeaCenterRelatByRelatIds(relatIds);
    }

    /**
     * 删除计量器具和成本中心关系信息
     *
     * @param relatId 计量器具和成本中心关系主键
     * @return 结果
     */
    @Override
    public int deleteEmsMeaCenterRelatByRelatId(Long relatId)
    {
        return emsMeaCenterRelatMapper.deleteEmsMeaCenterRelatByRelatId(relatId);
    }

    @Override
    public List<EmsMeaCenterRelatVo> selectEmsMeaCenterRelatpagelist(EmsMeaCenterRelatListDTO emsMeaCenterRelat) {
        List<EmsMeaCenterRelatVo> emsMeaCenterRelatVos = emsMeaCenterRelatMapper.selectEmsMeaCenterRelatpagelist(emsMeaCenterRelat);
        emsMeaCenterRelatVos.forEach(vo->{
            List<Integer> numberList = null;
            String expression;
            //是否有分隔符
            boolean haveSeparator = vo.getRelationExpression().contains(EnergyConstant.BASE_SEPARATOR);
            //包含分隔符, 有常量
            if(haveSeparator){
                String[] relationExpression = vo.getRelationExpression().split(EnergyConstant.BASE_SEPARATOR);
                expression = relationExpression[0];
                String numStr = relationExpression[1];
                numberList = Arrays.stream(numStr.split(EnergyConstant.EMPTY_STR)).map(Integer::parseInt).collect(Collectors.toList());
            }else{
                //没有分隔符，没有常量。直接替换编号
                expression = vo.getRelationExpression();
            }
            Pattern p = Pattern.compile(EnergyConstant.REG_NUMBER);
            Matcher m = p.matcher(expression);
            AtomicReference<String> res = new AtomicReference<>(expression);
            String trim = m.replaceAll(EnergyConstant.BASE_SEPARATOR).trim();
            String[] split = trim.split(EnergyConstant.BASE_SEPARATOR);
            ArrayList<String> list1 = new ArrayList<>(Arrays.asList(split));
            list1.removeIf(StringUtils::isEmpty);
            HashMap<Integer, String> hashMap = new HashMap<>(32);
            List<Integer> collect1 = list1.stream().map(Integer::parseInt).collect(Collectors.toList());
            for (Integer integer : collect1) {
                hashMap.put(integer,emsMeaCenterRelatMapper.selectEmscenterRealt(integer));
            }
            hashMap.forEach((k,v)->{
                if(ObjectUtils.isEmpty(v)){
                    throw new ServiceException(ErrorConstant.MEASURE_NOT_EXIST);
                }
                String replace = res.get().replace(k.toString(), v);
                res.set(replace);
            });
            String replaceExpress = res.get();
            if(haveSeparator){
                replaceExpress = String.format(replaceExpress, numberList.toArray());
            }
            vo.setRelationExpression(replaceExpress);
            vo.setNumberList(numberList);
        });
        return emsMeaCenterRelatVos;
    }

}
