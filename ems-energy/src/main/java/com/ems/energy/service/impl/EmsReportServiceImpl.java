package com.ems.energy.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ems.energy.domain.EmsReport;
import com.ems.energy.mapper.EmsReportMapper;
import com.ems.energy.service.IEmsReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 能耗报Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-21
 */
@Service
public class EmsReportServiceImpl implements IEmsReportService
{
    @Resource
    private EmsReportMapper emsReportMapper;

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 查询能耗报
     *
     * @param reportId 能耗报主键
     * @return 能耗报
     */
    @Override
    public EmsReport selectEmsReportByReportId(Long reportId)
    {
        return emsReportMapper.selectEmsReportByReportId(reportId);
    }

    /**
     * 查询能耗报列表
     *
     * @param emsReport 能耗报
     * @return 能耗报
     */
    @Override
    public List<EmsReport> selectEmsReportList(EmsReport emsReport)
    {

        List<EmsReport> emsReports = emsReportMapper.selectEmsReportList(emsReport);
        List<String> collect = emsReports.stream().map(EmsReport::getDataJson).collect(Collectors.toList());
        JSONArray jsonArray = new JSONArray();
        for (String s : collect) {
            jsonArray.add(s);
            try
            {
                for (int i=0; i < jsonArray.size(); i++)    {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    String id = jsonObject.getString("id");
                    String name = jsonObject.getString("name");
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
        return emsReports;
    }

    /**
     * 新增能耗报
     *
     * @param emsReport 能耗报
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertEmsReport(EmsReport emsReport)
    {
        return emsReportMapper.insertEmsReport(emsReport);
    }

    /**
     * 修改能耗报
     *
     * @param emsReport 能耗报
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEmsReport(EmsReport emsReport)
    {
        return emsReportMapper.updateEmsReport(emsReport);
    }

    /**
     * 批量删除能耗报
     *
     * @param reportIds 需要删除的能耗报主键
     * @return 结果
     */
    @Override
    public int deleteEmsReportByReportIds(Long[] reportIds)
    {
        return emsReportMapper.deleteEmsReportByReportIds(reportIds);
    }

    /**
     * 删除能耗报信息
     *
     * @param reportId 能耗报主键
     * @return 结果
     */
    @Override
    public int deleteEmsReportByReportId(Long reportId)
    {
        return emsReportMapper.deleteEmsReportByReportId(reportId);
    }
}
