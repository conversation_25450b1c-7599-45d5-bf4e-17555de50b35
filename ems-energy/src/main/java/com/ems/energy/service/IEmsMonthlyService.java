package com.ems.energy.service;

import com.ems.energy.domain.EmsMonthly;

import java.util.List;


/**
 * 月报Service接口
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
public interface IEmsMonthlyService
{
    /**
     * 查询月报
     *
     * @param dailyId 月报主键
     * @return 月报
     */
    EmsMonthly selectEmsMonthlyByDailyId(Long dailyId);

    /**
     * 查询月报列表
     *
     * @param emsMonthly 月报
     * @return 月报集合
     */
    List<EmsMonthly> selectEmsMonthlyList(EmsMonthly emsMonthly);

    /**
     * 新增月报
     *
     * @param emsMonthly 月报
     * @return 结果
     */
    int insertEmsMonthly(EmsMonthly emsMonthly);

    /**
     * 修改月报
     *
     * @param emsMonthly 月报
     * @return 结果
     */
    int updateEmsMonthly(EmsMonthly emsMonthly);

    /**
     * 批量删除月报
     *
     * @param dailyIds 需要删除的月报主键集合
     * @return 结果
     */
    int deleteEmsMonthlyByDailyIds(Long[] dailyIds);

    /**
     * 删除月报信息
     *
     * @param dailyId 月报主键
     * @return 结果
     */
    int deleteEmsMonthlyByDailyId(Long dailyId);
}
