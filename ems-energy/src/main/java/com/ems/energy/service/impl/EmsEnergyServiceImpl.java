package com.ems.energy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ems.common.constant.Constants;
import com.ems.common.core.redis.RedisCache;
import com.ems.common.exception.ServiceException;
import com.ems.common.utils.StringUtils;
import com.ems.energy.constant.DataConstant;
import com.ems.energy.constant.EnergyConstant;
import com.ems.energy.constant.RedisKeyConstant;
import com.ems.energy.domain.EmsEnergy;
import com.ems.energy.mapper.EmsEnergyMapper;
import com.ems.energy.service.IEmsEnergyService;
import com.ems.system.service.ISysConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 能源介质Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Service
public class EmsEnergyServiceImpl implements IEmsEnergyService
{
    @Resource
    private EmsEnergyMapper emsEnergyMapper;
    @Resource
    private RedisCache redisCache;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ISysConfigService configService;

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询能源介质
     *
     * @param energyId 能源介质主键
     * @return 能源介质
     */
    @Override
    public EmsEnergy selectEmsEnergyByEnergyId(Long energyId)
    {
        return emsEnergyMapper.selectEmsEnergyByEnergyId(energyId);
    }

    /**
     * 查询能源介质列表
     *
     * @param emsEnergy 能源介质
     * @return 能源介质
     */
    @Override
    public List<EmsEnergy> selectEmsEnergyList(EmsEnergy emsEnergy)
    {
        return emsEnergyMapper.selectEmsEnergyList(emsEnergy);
    }

    /**
     * 新增能源介质
     *
     * @param emsEnergy 能源介质
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertEmsEnergy(EmsEnergy emsEnergy)
    {
        //添加逻辑删除属性
        emsEnergy.setDelFlag(EnergyConstant.NORMAL_DEL_FLAG);
        String preFix = stringRedisTemplate.boundValueOps(Constants.SYS_CONFIG_KEY + EnergyConstant.ENERGY_CODE).get();
        if(StringUtils.isEmpty(preFix)){
            //刷新缓存
            preFix = reloadCache();
            if(StringUtils.isEmpty(preFix)){
                throw new ServiceException("缓存中没有配置该key");
            }
        }
        int count = emsEnergyMapper.selectCount();
        emsEnergy.setEnergyCode(preFix + String.format(DataConstant.NUMBER_03D,count+1));
        return emsEnergyMapper.insertEmsEnergy(emsEnergy);
    }

    /**
     * 刷新缓存
     * @return
     */
    private String reloadCache() {
        String preFix;
        configService.resetConfigCache();
        preFix = stringRedisTemplate.boundValueOps(Constants.SYS_CONFIG_KEY + EnergyConstant.ENERGY_CODE).get();
        return preFix;
    }
    /**
     * 修改能源介质
     *
     * @param emsEnergy 能源介质
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEmsEnergy(EmsEnergy emsEnergy)
    {
        return emsEnergyMapper.updateEmsEnergy(emsEnergy);
    }

    /**
     * 批量删除能源介质
     *
     * @param energyIds 需要删除的能源介质主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteEmsEnergyByEnergyIds(Long[] energyIds)
    {
        return emsEnergyMapper.deleteEmsEnergyByEnergyIds(energyIds);
    }

    /**
     * 删除能源介质信息
     *
     * @param energyId 能源介质主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteEmsEnergyByEnergyId(Long energyId)
    {
        return emsEnergyMapper.deleteEmsEnergyByEnergyId(energyId);
    }

    /**
     * 查询所有能源介质
     * @return
     */
    @Override
    public List<EmsEnergy> selectAllEnergy() {
        List<EmsEnergy> cacheList = redisCache.getCacheList(RedisKeyConstant.ENERGY_KEY);
        if(CollectionUtils.isNotEmpty(cacheList)){
            return cacheList;
        }
        LambdaQueryWrapper<EmsEnergy> queryWrapper = new LambdaQueryWrapper<>();
        cacheList = emsEnergyMapper.selectList(queryWrapper);
        redisCache.setCacheList(RedisKeyConstant.ENERGY_KEY,cacheList);
        return cacheList;
    }
}
