package com.ems.energy.service;


import com.ems.energy.domain.EmsDevice;
import com.ems.energy.domain.vo.EmsDeviceVo;

import java.util.List;

/**
 * 工厂设备Service接口
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
public interface IEmsDeviceService
{
    /**
     * 查询工厂设备
     *
     * @param equId 工厂设备主键
     * @return 工厂设备
     */
    EmsDevice selectEmsDeviceByEquId(Long equId);

    /**
     * 查询工厂设备列表
     *
     * @param emsDevice 工厂设备
     * @return 工厂设备集合
     */
    List<EmsDeviceVo> selectEmsDeviceList(EmsDeviceVo emsDevice);

    /**
     * 新增工厂设备
     *
     * @param emsDevice 工厂设备
     * @return 结果
     */
    int insertEmsDevice(EmsDevice emsDevice);

    /**
     * 修改工厂设备
     *
     * @param emsDevice 工厂设备
     * @return 结果
     */
    int updateEmsDevice(EmsDevice emsDevice);

    /**
     * 批量删除工厂设备
     *
     * @param equIds 需要删除的工厂设备主键集合
     * @return 结果
     */
    int deleteEmsDeviceByEquIds(Long[] equIds);

    /**
     * 删除工厂设备信息
     *
     * @param equId 工厂设备主键
     * @return 结果
     */
    int deleteEmsDeviceByEquId(Long equId);
}
