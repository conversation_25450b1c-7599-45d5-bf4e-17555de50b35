package com.ems.energy.service;

import com.ems.energy.domain.vo.PartEnergyVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IReportService {
    List<List<Object>> queryTopEnergyCostByTag(String tag, Date time);

    List<List<Object>> energyReportWithCategoryByEquipment(Long equipmentId, Date start, Date end);

    List<List<Object>> factoryEnergyReportWithCategory(Date start, Date end);

    List<List<Object>> queryMonthDayValueBySampler( String sampler, List<String> categories, String month);

    List<List<Object>> queryMonthDayEnergyBySampler( String sampler, String month);

    List<List<Object>> queryMonthDayElecAmountBySampler( String sampler, String month);

    List<List<Object>> queryMonthDayCarbonBySampler( String sampler, String month);

    List<List<Object>> queryFactoryMonthDayEnergy(String month);

    List<List<Object>> queryFactoryMonthDayElecAmount(String month);

    List<List<Map<String, Object>>> queryMonthEnergyReport(String year);

    List<List<Object>> queryFactoryMonthDayCarbon(String month);

    List<List<Object>> queryOneMonthCategoryEnergyReport(String month);

    List<List<Object>> queryOneMonthCategoryCarbonReport(String month);

    List<List<Object>> queryOneMonthCategoryElecAmountReport(String month);

    BigDecimal queryOneMonthTotalEnergy(String month);

    BigDecimal queryOneMonthTotalCarbon(String month);

    BigDecimal queryOneMonthTotalElecAmount(String month);

    List<List<Object>> queryFactoryMd(String start, String end);

    List<List<Object>> queryPartsEquivalentCarbon(List<Integer> partIds, Date start, Date end);

    public List<PartEnergyVo> queryPcfReport(Long partId, Date start, Date end);

    Map<String, Object> queryEquipmentCarbonDayReport(Long equipmentId, String day);

    List<List<Object>> queryEquipmentsEquivalentCarbonReport(List<Integer> equipmentIds, Date start, Date end);

    List<List<Object>> queryEquipmentsOeeReport(List<Integer> equipmentIds, Date start, Date end);

    List<List<Object>> queryEquipmentStatusEnergyReport(Long equipmentId, Date start, Date end);
}
