package com.ems.energy.service;

import com.ems.common.core.domain.AjaxResult;
import com.ems.energy.domain.EmsDailyData;

import java.util.List;


/**
 * 日报Service接口
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
public interface IEmsDailyDataService
{
    /**
     * 查询日报
     *
     * @param dailyId 日报主键
     * @return 日报
     */
    EmsDailyData selectEmsDailyDataByDailyId(Long dailyId);

    /**
     * 查询日报列表
     *
     * @param emsDailyData 日报
     * @return 日报集合
     */
    List<EmsDailyData> selectEmsDailyDataList(EmsDailyData emsDailyData);

    /**
     * 新增日报
     *
     * @param emsDailyData 日报
     * @return 结果
     */
    int insertEmsDailyData(EmsDailyData emsDailyData);

    /**
     * 修改日报
     *
     * @param emsDailyData 日报
     * @return 结果
     */
    int updateEmsDailyData(EmsDailyData emsDailyData);

    /**
     * 批量删除日报
     *
     * @param dailyIds 需要删除的日报主键集合
     * @return 结果
     */
    int deleteEmsDailyDataByDailyIds(Long[] dailyIds);

    /**
     * 删除日报信息
     *
     * @param dailyId 日报主键
     * @return 结果
     */
    int deleteEmsDailyDataByDailyId(Long dailyId);

    AjaxResult selectEmsDailyDatapageList(EmsDailyData emsDailyData);

}
