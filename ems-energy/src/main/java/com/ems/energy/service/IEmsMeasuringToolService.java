package com.ems.energy.service;

import java.util.List;
import com.ems.energy.domain.EmsMeasuringTool;
import org.apache.ibatis.annotations.Param;

/**
 * 表具Service接口
 *
 * <AUTHOR>
 * @date 2022-03-07
 */
public interface IEmsMeasuringToolService
{
    /**
     * 查询表具
     *
     * @param measuringToolId 表具主键
     * @return 表具
     */
    EmsMeasuringTool selectEmsMeasuringToolByMeasuringToolId(Long measuringToolId);

    /**
     * 查询表具列表
     *
     * @param emsMeasuringTool 表具
     * @return 表具集合
     */
    List<EmsMeasuringTool> selectEmsMeasuringToolList(EmsMeasuringTool emsMeasuringTool);

    /**
     * 新增表具
     *
     * @param emsMeasuringTool 表具
     * @return 结果
     */
    int insertEmsMeasuringTool(EmsMeasuringTool emsMeasuringTool);

    /**
     * 修改表具
     *
     * @param emsMeasuringTool 表具
     * @return 结果
     */
    int updateEmsMeasuringTool(EmsMeasuringTool emsMeasuringTool);

    /**
     * 批量删除表具
     *
     * @param measuringToolIds 需要删除的表具主键集合
     * @return 结果
     */
    int deleteEmsMeasuringToolByMeasuringToolIds(Long[] measuringToolIds);

    /**
     * 删除表具信息
     *
     * @param measuringToolId 表具主键
     * @return 结果
     */
    int deleteEmsMeasuringToolByMeasuringToolId(Long measuringToolId);

    /**
     * 查询实体表具列表
     *
     * @param emsMeasuringTool 表具
     * @return 表具集合
     */
    public List<EmsMeasuringTool> selectRealEmsMeasuringToolList(EmsMeasuringTool emsMeasuringTool);

    public List<EmsMeasuringTool> selectEmsMeasuringToolByTag(String tag);
}
