package com.ems.energy.service.impl;

import java.util.Date;
import java.util.List;
import com.ems.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ems.energy.mapper.EmsCarbonFactorMapper;
import com.ems.energy.domain.EmsCarbonFactor;
import com.ems.energy.service.IEmsCarbonFactorService;

/**
 * 碳排放因子设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-02
 */
@Service
public class EmsCarbonFactorServiceImpl implements IEmsCarbonFactorService
{
    @Autowired
    private EmsCarbonFactorMapper emsCarbonFactorMapper;

    /**
     * 查询碳排放因子设置
     *
     * @param factorId 碳排放因子设置主键
     * @return 碳排放因子设置
     */
    @Override
    public EmsCarbonFactor selectEmsCarbonFactorByFactorId(Long factorId)
    {
        return emsCarbonFactorMapper.selectEmsCarbonFactorByFactorId(factorId);
    }

    /**
     * 查询碳排放因子设置列表
     *
     * @param emsCarbonFactor 碳排放因子设置
     * @return 碳排放因子设置
     */
    @Override
    public List<EmsCarbonFactor> selectEmsCarbonFactorList(EmsCarbonFactor emsCarbonFactor)
    {
        return emsCarbonFactorMapper.selectEmsCarbonFactorList(emsCarbonFactor);
    }

    /**
     * 新增碳排放因子设置
     *
     * @param emsCarbonFactor 碳排放因子设置
     * @return 结果
     */
    @Override
    public int insertEmsCarbonFactor(EmsCarbonFactor emsCarbonFactor)
    {
        emsCarbonFactor.setCreateTime(DateUtils.getNowDate());
        return emsCarbonFactorMapper.insertEmsCarbonFactor(emsCarbonFactor);
    }

    /**
     * 修改碳排放因子设置
     *
     * @param emsCarbonFactor 碳排放因子设置
     * @return 结果
     */
    @Override
    public int updateEmsCarbonFactor(EmsCarbonFactor emsCarbonFactor)
    {
        emsCarbonFactor.setUpdateTime(DateUtils.getNowDate());
        return emsCarbonFactorMapper.updateEmsCarbonFactor(emsCarbonFactor);
    }

    /**
     * 批量删除碳排放因子设置
     *
     * @param factorIds 需要删除的碳排放因子设置主键
     * @return 结果
     */
    @Override
    public int deleteEmsCarbonFactorByFactorIds(Long[] factorIds)
    {
        return emsCarbonFactorMapper.deleteEmsCarbonFactorByFactorIds(factorIds);
    }

    /**
     * 删除碳排放因子设置信息
     *
     * @param factorId 碳排放因子设置主键
     * @return 结果
     */
    @Override
    public int deleteEmsCarbonFactorByFactorId(Long factorId)
    {
        return emsCarbonFactorMapper.deleteEmsCarbonFactorByFactorId(factorId);
    }

    @Override
    public EmsCarbonFactor selectValidEmsCarbonFactor(Date time, String energyCategory) {
        return emsCarbonFactorMapper.selectValidEmsCarbonFactor(time, energyCategory);
    }
}
