package com.ems.energy.service;

import com.ems.energy.domain.EmsEnergy;

import java.util.List;

/**
 * 能源介质Service接口
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface IEmsEnergyService
{
    /**
     * 查询能源介质
     *
     * @param energyId 能源介质主键
     * @return 能源介质
     */
    EmsEnergy selectEmsEnergyByEnergyId(Long energyId);

    /**
     * 查询能源介质列表
     *
     * @param emsEnergy 能源介质
     * @return 能源介质集合
     */
    List<EmsEnergy> selectEmsEnergyList(EmsEnergy emsEnergy);

    /**
     * 新增能源介质
     *
     * @param emsEnergy 能源介质
     * @return 结果
     */
    int insertEmsEnergy(EmsEnergy emsEnergy);

    /**
     * 修改能源介质
     *
     * @param emsEnergy 能源介质
     * @return 结果
     */
    int updateEmsEnergy(EmsEnergy emsEnergy);

    /**
     * 批量删除能源介质
     *
     * @param energyIds 需要删除的能源介质主键集合
     * @return 结果
     */
    int deleteEmsEnergyByEnergyIds(Long[] energyIds);

    /**
     * 删除能源介质信息
     *
     * @param energyId 能源介质主键
     * @return 结果
     */
    int deleteEmsEnergyByEnergyId(Long energyId);

    List<EmsEnergy> selectAllEnergy();

}
