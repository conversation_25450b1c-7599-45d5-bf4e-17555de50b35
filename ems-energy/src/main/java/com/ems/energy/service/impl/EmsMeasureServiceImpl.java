package com.ems.energy.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ems.common.constant.Constants;
import com.ems.common.constant.ErrorConstant;
import com.ems.common.constant.UserConstants;
import com.ems.common.exception.ServiceException;
import com.ems.common.utils.StringUtils;
import com.ems.energy.constant.DataConstant;
import com.ems.energy.constant.EnergyConstant;
import com.ems.energy.domain.EmsCostMea;
import com.ems.energy.domain.EmsMeasure;
import com.ems.energy.domain.EmsMeasureListDTO;
import com.ems.energy.domain.tree.Emstreecenter;
import com.ems.energy.domain.vo.EmsMeasureVo;
import com.ems.energy.domain.vo.EmsMeasureVotwo;
import com.ems.energy.mapper.EmsMeasureMapper;
import com.ems.energy.service.IEmsCostMeaService;
import com.ems.energy.service.IEmsMeasureService;
import com.ems.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 计量器具Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Service
public class EmsMeasureServiceImpl implements IEmsMeasureService
{
    @Resource
    private EmsMeasureMapper emsMeasureMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private IEmsCostMeaService emsCostMeaService;

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询计量器具
     *
     * @param meaId 计量器具主键
     * @return 计量器具
     */
    @Override
    public EmsMeasure selectEmsMeasureByMeaId(Long meaId)
    {
        return emsMeasureMapper.selectEmsMeasureByMeaId(meaId);
    }

    /**
     * 查询计量器具列表
     *
     * @param emsMeasure 计量器具
     * @return 计量器具
     */
    @Override
    public List<EmsMeasure> selectEmsMeasureList(EmsMeasure emsMeasure)
    {
        return emsMeasureMapper.selectEmsMeasureList(emsMeasure);
    }

    /**
     * 新增计量器具
     * 1.添加成本中心和计量器具关系中间表(ems_cost_center_mea)
     * 2.添加计量器具表(ems_measure)
     * @param emsMeasure 计量器具
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertEmsMeasure(EmsMeasure emsMeasure)
    {
        EmsMeasure info = emsMeasureMapper.selectEmsMeasureByMeaId(emsMeasure.getMeaParentId());
        //通过父节点查询不到数据,添加到第一层
        if(ObjectUtils.isEmpty(info)){
            emsMeasure.setAncestors(EnergyConstant.BASE_ANCESTORS);
            //添加计量器具
            emsMeasureMapper.insertEmsMeasure(emsMeasure);
            //添加成本中心和计量器具关系表的数据
            EmsCostMea mea = new EmsCostMea();
            mea.setCostCenterId(emsMeasure.getCostCenterId());
            mea.setMeaId(emsMeasure.getMeaId());
            //添加成本中心和计量器具关系表的数据
            return emsCostMeaService.insertEmsCostMea(mea);
        }
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_DISABLE.equals(info.getStatus()))
        {
            throw new ServiceException("计量器具停用，不允许新增");
        }
        String preFix = stringRedisTemplate.boundValueOps(Constants.SYS_CONFIG_KEY + EnergyConstant.MEASURE_CODE).get();
        if(StringUtils.isEmpty(preFix)){
            //刷新缓存
            preFix = reloadCache();
            if(StringUtils.isEmpty(preFix)){
                throw new ServiceException(ErrorConstant.KEY_IS_NOT_FOUND);
            }
        }
        preFix = preFix.replace(EnergyConstant.DOUBLE_QUOT_MARK,EnergyConstant.EMPTY_STR);
        Integer count = emsMeasureMapper.selectCount(null);
        emsMeasure.setMeaCode(preFix + String.format(DataConstant.NUMBER_03D,count+1));
        emsMeasure.setAncestors(info.getAncestors() + EnergyConstant.BASE_SEPARATOR + emsMeasure.getMeaParentId());
        //添加逻辑删除属性
        emsMeasure.setDelFlag(EnergyConstant.NORMAL_DEL_FLAG);
        //添加计量器具表
        emsMeasureMapper.insertEmsMeasure(emsMeasure);
        EmsCostMea mea = new EmsCostMea();
        mea.setCostCenterId(emsMeasure.getCostCenterId());
        mea.setMeaId(emsMeasure.getMeaId());
        //添加成本中心和计量器具关系中间表
        return emsCostMeaService.insertEmsCostMea(mea);
    }

    /**
     * 刷新缓存
     * @return
     */
    private String reloadCache() {
        String preFix;
        sysConfigService.resetConfigCache();
        preFix = stringRedisTemplate.boundValueOps(Constants.SYS_CONFIG_KEY + EnergyConstant.MEASURE_CODE).get();
        return preFix;
    }

    /**
     * 修改计量器具
     *
     * @param emsMeasure 计量器具
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEmsMeasure(EmsMeasure emsMeasure)
    {
        return emsMeasureMapper.updateEmsMeasure(emsMeasure);
    }

    /**
     * 批量删除计量器具
     *
     * @param meaIds 需要删除的计量器具主键
     * @return 结果
     */
    @Override
    public int deleteEmsMeasureByMeaIds(Long[] meaIds)
    {
        return emsMeasureMapper.deleteEmsMeasureByMeaIds(meaIds);
    }

    /**
     * 删除计量器具信息
     *
     * @param meaId 计量器具主键
     * @return 结果
     */
    @Override
    public int deleteEmsMeasureByMeaId(Long meaId)
    {
        return emsMeasureMapper.deleteEmsMeasureByMeaId(meaId);
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param emsMeasures 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<Emstreecenter> buildEmsMeasureTreeSelect(List<EmsMeasure> emsMeasures)
    {
        List<EmsMeasure> emsMeasureTree = buildEmsMeasureTree(emsMeasures);
        return emsMeasureTree.stream().map(Emstreecenter::new).collect(Collectors.toList());
    }

    @Override
    public List<EmsMeasure> buildEmsMeasureTree(List<EmsMeasure> emsCostCenters) {
        List<EmsMeasure> returnList = new ArrayList
                <>();
        List<Long> tempList = new ArrayList<>();
        for (EmsMeasure costCenter : emsCostCenters) {
            tempList.add(costCenter.getMeaId());
        }
        for (EmsMeasure costCenter : emsCostCenters) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(costCenter.getMeaParentId())) {
                recursionFn(emsCostCenters, costCenter);
                returnList.add(costCenter);
            }
        }
        if (returnList.isEmpty()) {
            returnList = emsCostCenters;
        }
        return returnList;
    }

    /**
     * 计量器具列表查询
     * @param listDTO
     * @return
     */
    @Override
    public List<EmsMeasureListDTO> selectEmsMeasureListDTO(EmsMeasureListDTO listDTO) {
        return emsMeasureMapper.selectEmsMeasureListDTO(listDTO);
    }

    @Override
    public List<EmsMeasureVo> selectEmsMeasureListPage(EmsMeasureVo emsMeasure) {
        return emsMeasureMapper.selectEmsMeasureListPage(emsMeasure);
    }

    /**
     * 检验计量器具名称是否唯一
     * @param emsMeasure
     * @return
     */
    @Override
    public String checkMeasureNameUnique(EmsMeasure emsMeasure) {
        Long centerId = StringUtils.isNull(emsMeasure.getMeaId()) ? -1L : emsMeasure.getMeaId();
        EmsMeasure info = emsMeasureMapper.checkDeptNameUnique(emsMeasure.getMeaName(), emsMeasure.getMeaParentId());
        if (StringUtils.isNotNull(info) && info.getMeaParentId().longValue() != centerId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public boolean selectByMeaid(Long meaId) {
      Integer result= emsMeasureMapper.selectByMeaid(meaId);
      return result != null;
    }

    @Override
    public List<EmsMeasureVotwo> selectEmsMeasureBycostCent(Long costCenterId) {
        List<EmsMeasure> result=emsMeasureMapper.selectEmsMeasureBycostCent(costCenterId);
        List<EmsMeasureVotwo> list = new ArrayList<>();
        for (EmsMeasure emsMeasure : result) {
            if(ObjectUtils.isEmpty(emsMeasure)){
                throw new ServiceException("该成本中心下没有计量器具");
            }
            EmsMeasureVotwo emsMeasureVotwo = new EmsMeasureVotwo();
            emsMeasureVotwo.setId(emsMeasure.getMeaId());
            emsMeasureVotwo.setName(emsMeasure.getMeaName());
            list.add(emsMeasureVotwo);
        }
        return  list;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<EmsMeasure> list, EmsMeasure t)
    {
        // 得到子节点列表
        List<EmsMeasure> childList = getChildList(list, t);
        t.setChildren(childList);
        for (EmsMeasure tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }
    /**
     * 得到子节点列表
     */
    private List<EmsMeasure> getChildList(List<EmsMeasure> list, EmsMeasure t)
    {
        List<EmsMeasure> tlist = new ArrayList<>();
        for (EmsMeasure n : list) {
            if (StringUtils.isNotNull(n.getMeaParentId()) && n.getMeaParentId().longValue() == t.getMeaId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<EmsMeasure> list, EmsMeasure t)
    {
        return getChildList(list, t).size() > 0;
    }
}
