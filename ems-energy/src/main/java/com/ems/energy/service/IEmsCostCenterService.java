package com.ems.energy.service;

import com.ems.energy.domain.EmsCostCenter;
import com.ems.energy.domain.tree.EmsCenterVoTree;
import com.ems.energy.domain.tree.Emstreecenter;
import com.ems.energy.domain.vo.EmsCostCenterVo;

import java.util.List;

/**
 * 成本中心Service接口
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface IEmsCostCenterService
{
    /**
     * 查询成本中心
     *
     * @param centerId 成本中心主键
     * @return 成本中心
     */
    EmsCostCenter selectEmsCostCenterByCenterId(Long centerId);

    /**
     * 查询成本中心列表
     *
     * @param emsCostCenter 成本中心
     * @return 成本中心集合
     */
    List<EmsCostCenter> selectEmsCostCenterList(EmsCostCenter emsCostCenter);

    /**
     * 新增成本中心
     *
     * @param emsCostCenter 成本中心
     * @return 结果
     */
    int insertEmsCostCenter(EmsCostCenter emsCostCenter);

    /**
     * 修改成本中心
     *
     * @param emsCostCenter 成本中心
     * @return 结果
     */
    int updateEmsCostCenter(EmsCostCenter emsCostCenter);

    /**
     * 批量删除成本中心
     *
     * @param centerIds 需要删除的成本中心主键集合
     * @return 结果
     */
    int deleteEmsCostCenterByCenterIds(Long[] centerIds);

    /**
     * 删除成本中心信息
     *
     * @param centerId 成本中心主键
     * @return 结果
     */
    int deleteEmsCostCenterByCenterId(Long centerId);

    /**
     * 构建成本中心树形结构
     * @param emsCostCenters
     * @return
     */
    List<Emstreecenter> buildcenterselect(List<EmsCostCenter> emsCostCenters);

    /**
     * 根据条件查询成本中心
     * @param emsCostCenter
     * @return
     */
    List<EmsCostCenterVo> pagelist(EmsCostCenter emsCostCenter);

    /**
     * 检查成本中心名称是否唯一
     * @param emsCostCenter
     * @return
     */
    String checkCenterNameUnique(EmsCostCenter emsCostCenter);

    List<EmsCenterVoTree> buildcenterVoselect(List<EmsCostCenterVo> emsCostCenters);
}
