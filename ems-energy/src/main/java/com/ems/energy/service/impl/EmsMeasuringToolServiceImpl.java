package com.ems.energy.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ems.common.constant.ErrorConstant;
import com.ems.common.utils.DateUtils;
import com.ems.energy.constant.DataConstant;
import com.ems.energy.constant.EnergyConstant;
import com.ems.energy.domain.EmsMeaCenterRelatListDTO;
import com.ems.energy.domain.EmsMeaCenterRelatListPO;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ems.energy.mapper.EmsMeasuringToolMapper;
import com.ems.energy.domain.EmsMeasuringTool;
import com.ems.energy.service.IEmsMeasuringToolService;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

/**
 * 表具Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-07
 */
@Service
public class EmsMeasuringToolServiceImpl implements IEmsMeasuringToolService
{
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private EmsMeasuringToolMapper emsMeasuringToolMapper;

    /**
     * 查询表具
     *
     * @param measuringToolId 表具主键
     * @return 表具
     */
    @Override
    public EmsMeasuringTool selectEmsMeasuringToolByMeasuringToolId(Long measuringToolId)
    {
        EmsMeasuringTool measuringTool = emsMeasuringToolMapper.selectEmsMeasuringToolByMeasuringToolId(measuringToolId);
        if (null != measuringTool && !Strings.isNullOrEmpty(measuringTool.getVirtualExpression())){
            if(checkFormulaHaveConstant(measuringTool.getVirtualExpression())){
                //有常量的情况下,需要处理数据库中表达式逗号后面的常量,返回numberList给前端
                String[] split = measuringTool.getVirtualExpression().split(EnergyConstant.BASE_SEPARATOR);
                String[] numberArr = split[1].split(EnergyConstant.EMPTY_STR);
                measuringTool.setVirtualExpression(split[0]);
                measuringTool.setNumberList(Arrays.stream(numberArr).map(Integer::new).collect(Collectors.toList()));
            }
        }
        //判断算式内是否有常量

        return measuringTool;
    }

    /**
     * 查询表具列表
     *
     * @param emsMeasuringTool 表具
     * @return 表具
     */
    @Override
    public List<EmsMeasuringTool> selectEmsMeasuringToolList(EmsMeasuringTool emsMeasuringTool)
    {
        return emsMeasuringToolMapper.selectEmsMeasuringToolList(emsMeasuringTool);
    }

    /**
     * 新增表具
     *
     * @param emsMeasuringTool 表具
     * @return 结果
     */
    @Override
    public int insertEmsMeasuringTool(EmsMeasuringTool emsMeasuringTool)
    {
        boolean result = makeVirtualTool(emsMeasuringTool);
        if(result){
            emsMeasuringTool.setCreateTime(DateUtils.getNowDate());
            return emsMeasuringToolMapper.insertEmsMeasuringTool(emsMeasuringTool);
        }else{
            return 0;
        }

    }

    private boolean makeVirtualTool(EmsMeasuringTool emsMeasuringTool) {
        if (emsMeasuringTool.getType().equalsIgnoreCase("virtual")){
            if(StringUtils.isEmpty(emsMeasuringTool.getVirtualExpression())){
                log.info(ErrorConstant.RELAT_IS_NULL);
                return false;
            }
            String expression = emsMeasuringTool.getVirtualExpression();
            //检验前端发送过来的算式是否正确
            if(checkFormula(expression, emsMeasuringTool)){
                log.info(ErrorConstant.FORMULA_IS_INVALID);
                return false;
            }
            //判断算式内是否有常量
            if(checkFormulaHaveConstant(expression, emsMeasuringTool)){
                dealWithConstant(emsMeasuringTool, expression);
            }
            //检验算式内的计量器具id是否存在
            if(checkMeasureIsExist(expression)){
                log.info(ErrorConstant.MEASURE_NOT_EXIST);
                return false;
            }
            emsMeasuringTool.setVirtualJson(JSON.toJSONString(emsMeasuringTool.getRecordList()));

        }
        return true;
    }

    /**
     * 修改表具
     *
     * @param emsMeasuringTool 表具
     * @return 结果
     */
    @Override
    public int updateEmsMeasuringTool(EmsMeasuringTool emsMeasuringTool)
    {
        boolean result = makeVirtualTool(emsMeasuringTool);
        if(result) {
            emsMeasuringTool.setUpdateTime(DateUtils.getNowDate());
            return emsMeasuringToolMapper.updateEmsMeasuringTool(emsMeasuringTool);
        }else{
            return 0;
        }
    }

    /**
     * 批量删除表具
     *
     * @param measuringToolIds 需要删除的表具主键
     * @return 结果
     */
    @Override
    public int deleteEmsMeasuringToolByMeasuringToolIds(Long[] measuringToolIds)
    {
        return emsMeasuringToolMapper.deleteEmsMeasuringToolByMeasuringToolIds(measuringToolIds);
    }

    /**
     * 删除表具信息
     *
     * @param measuringToolId 表具主键
     * @return 结果
     */
    @Override
    public int deleteEmsMeasuringToolByMeasuringToolId(Long measuringToolId)
    {
        return emsMeasuringToolMapper.deleteEmsMeasuringToolByMeasuringToolId(measuringToolId);
    }

    @Override
    public List<EmsMeasuringTool> selectRealEmsMeasuringToolList(EmsMeasuringTool emsMeasuringTool) {
        return emsMeasuringToolMapper.selectRealEmsMeasuringToolList(emsMeasuringTool);
    }

    @Override
    public List<EmsMeasuringTool> selectEmsMeasuringToolByTag(String tag) {
        return emsMeasuringToolMapper.selectEmsMeasuringToolByTag(tag);
    }

    /**
     * 判断表达式是否有常量
     * @param expression
     * @return
     */
    public boolean checkFormulaHaveConstant(String expression){
        int i = expression.indexOf(DataConstant.FORMAT_D);
        return i != -1;
    }

    /**
     * 判断算式中是否有常量存在
     * @param expression
     * @param measuringTool
     * @return
     */
    private boolean checkFormulaHaveConstant(String expression, EmsMeasuringTool measuringTool) {
        //判断条件，前端传来的数字列表为空并且算式内搜索不到%d的符号
        if(CollectionUtils.isEmpty(measuringTool.getNumberList())){
            return false;
        }
        int i = expression.indexOf(DataConstant.FORMAT_D);
        return i != -1;
    }

    /**
     * 检验算式是否合法
     * @param expression
     * @param dto
     * @return
     */
    private boolean checkFormula(String expression, EmsMeasuringTool dto) {
        ScriptEngineManager engineManager = new ScriptEngineManager();
        ScriptEngine js = engineManager.getEngineByName(EnergyConstant.JS);
        try {
            String checkExpression;
            if(CollectionUtils.isNotEmpty(dto.getNumberList())){
                checkExpression = String.format(expression,dto.getNumberList().toArray());
            }else{
                checkExpression = expression;
            }
            js.eval(checkExpression);
        } catch (ScriptException e) {
            log.info(ErrorConstant.FORMULA_IS_INVALID);
            return true;
        }
        return false;
    }

    /**
     * 检验计量器具id是否存在
     * @param expression
     * @return
     */
    private boolean checkMeasureIsExist(String expression) {
        //匹配指定范围内的数字
        Pattern p = Pattern.compile(EnergyConstant.NUMBER_ROUND_REG);
        Matcher m = p.matcher(expression);
        List<String> ids = new ArrayList<>();
        while(m.find()) {
            ids.add(m.group());
        }
        ids = ids.stream().distinct().collect(Collectors.toList());
        int i = emsMeasuringToolMapper.counEmsMeasuringToolByIds(ids);
        return ids.size() != i;
    }

    /**
     * 处理关系表达式内的常量
     * @param measuringTool
     * @param expression
     * @return
     */
    private void dealWithConstant(EmsMeasuringTool measuringTool, String expression) {
        StringBuilder sb = new StringBuilder();
        sb.append(expression).append(EnergyConstant.BASE_SEPARATOR);
        measuringTool.getNumberList().forEach(sb::append);
        measuringTool.setVirtualExpression(sb.toString());
    }
}
