package com.ems.energy.service;

import com.ems.energy.domain.EmsMeasure;
import com.ems.energy.domain.EmsMeasureListDTO;
import com.ems.energy.domain.tree.Emstreecenter;
import com.ems.energy.domain.vo.EmsMeasureVo;
import com.ems.energy.domain.vo.EmsMeasureVotwo;

import java.util.List;

/**
 * 计量器具Service接口
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface IEmsMeasureService
{
    /**
     * 查询计量器具
     *
     * @param meaId 计量器具主键
     * @return 计量器具
     */
    EmsMeasure selectEmsMeasureByMeaId(Long meaId);

    /**
     * 查询计量器具列表
     *
     * @param emsMeasure 计量器具
     * @return 计量器具集合
     */
    List<EmsMeasure> selectEmsMeasureList(EmsMeasure emsMeasure);

    /**
     * 新增计量器具
     *
     * @param emsMeasure 计量器具
     * @return 结果
     */
    int insertEmsMeasure(EmsMeasure emsMeasure);

    /**
     * 修改计量器具
     *
     * @param emsMeasure 计量器具
     * @return 结果
     */
    int updateEmsMeasure(EmsMeasure emsMeasure);

    /**
     * 批量删除计量器具
     *
     * @param meaIds 需要删除的计量器具主键集合
     * @return 结果
     */
    int deleteEmsMeasureByMeaIds(Long[] meaIds);

    /**
     * 删除计量器具信息
     *
     * @param meaId 计量器具主键
     * @return 结果
     */
    int deleteEmsMeasureByMeaId(Long meaId);

    List<Emstreecenter> buildEmsMeasureTreeSelect(List<EmsMeasure> emsMeasures);

    /**
     * 构建前端所需要树结构
     *
     * @param emsMeasures 部门列表
     * @return 树结构列表
     */
    List<EmsMeasure> buildEmsMeasureTree(List<EmsMeasure> emsMeasures);

    /**
     * 查询计量器具列表
     * @param listDTO
     * @return
     */
    List<EmsMeasureListDTO> selectEmsMeasureListDTO(EmsMeasureListDTO listDTO);

    List<EmsMeasureVo> selectEmsMeasureListPage(EmsMeasureVo emsMeasure);

    /**
     * 检验计量器具名称是否唯一
     * @param emsMeasure
     * @return
     */
    String checkMeasureNameUnique(EmsMeasure emsMeasure);

    boolean selectByMeaid(Long meaId);

    /**
     * 根据成本中心id查询计量器具
     * @param costCenterId
     * @return
     */
    List<EmsMeasureVotwo> selectEmsMeasureBycostCent(Long costCenterId);

}
