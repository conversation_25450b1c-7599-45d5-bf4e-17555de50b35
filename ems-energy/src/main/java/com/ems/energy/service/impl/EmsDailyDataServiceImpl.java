package com.ems.energy.service.impl;

import com.ems.common.core.domain.AjaxResult;
import com.ems.common.utils.DateUtils;
import com.ems.energy.domain.EmsDailyData;
import com.ems.energy.domain.EmsDailyDataDTO;
import com.ems.energy.mapper.EmsDailyDataMapper;
import com.ems.energy.service.IEmsDailyDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.valueOf;
import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;



/**
 * 日报Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Service
public class EmsDailyDataServiceImpl implements IEmsDailyDataService
{
    @Resource
    private EmsDailyDataMapper emsDailyDataMapper;

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询日报
     *
     * @param dailyId 日报主键
     * @return 日报
     */
    @Override
    public EmsDailyData selectEmsDailyDataByDailyId(Long dailyId)
    {
        return emsDailyDataMapper.selectEmsDailyDataByDailyId(dailyId);
    }

    /**
     * 查询日报列表
     *
     * @param emsDailyData 日报
     * @return 日报
     */
    @Override
    public List<EmsDailyData> selectEmsDailyDataList(EmsDailyData emsDailyData)
    {
        return emsDailyDataMapper.selectEmsDailyDataList(emsDailyData);
    }

    /**
     * 新增日报
     *
     * @param emsDailyData 日报
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertEmsDailyData(EmsDailyData emsDailyData)
    {
        emsDailyData.setCreateTime(DateUtils.getNowDate());
        return emsDailyDataMapper.insertEmsDailyData(emsDailyData);
    }

    /**
     * 修改日报
     *
     * @param emsDailyData 日报
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEmsDailyData(EmsDailyData emsDailyData)
    {
        return emsDailyDataMapper.updateEmsDailyData(emsDailyData);
    }

    /**
     * 批量删除日报
     *
     * @param dailyIds 需要删除的日报主键
     * @return 结果
     */
    @Override
    public int deleteEmsDailyDataByDailyIds(Long[] dailyIds)
    {
        return emsDailyDataMapper.deleteEmsDailyDataByDailyIds(dailyIds);
    }

    /**
     * 删除日报信息
     *
     * @param dailyId 日报主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteEmsDailyDataByDailyId(Long dailyId)
    {
        return emsDailyDataMapper.deleteEmsDailyDataByDailyId(dailyId);
    }

    @Override
    public AjaxResult selectEmsDailyDatapageList(EmsDailyData emsDailyData) {
        Date date = new Date();
        SimpleDateFormat dateFormat= new SimpleDateFormat(DateUtils.YYYY_MM);
        String format = dateFormat.format(date);
        List<EmsDailyDataDTO> emsDailyData1 = emsDailyDataMapper.selectEmsDailyDatapageList(format);
        List<Long> collect = emsDailyData1.stream().map(EmsDailyDataDTO::getDeviceId).distinct().collect(Collectors.toList());
        List<EmsDailyDataDTO> emsDailyDtoS = new ArrayList<>();
        computAvg(emsDailyData1, collect, emsDailyDtoS);
        ArrayList<EmsDailyDataDTO> res = emsDailyDtoS.stream().collect(collectingAndThen(toCollection(() -> new TreeSet<>(comparing(EmsDailyDataDTO::getDeviceId))), ArrayList::new));
        return AjaxResult.success(res);
    }

    /**
     *
     * @param emsDailyData 查询出来所有的值
     * @param collect 符合的个数
     * @param emsDailyDtoS DTO空对象
     */
    private void computAvg(List<EmsDailyDataDTO> emsDailyData, List<Long> collect, List<EmsDailyDataDTO> emsDailyDtoS) {
        for (Long aLong : collect) {
            for (EmsDailyDataDTO daily : emsDailyData){
                if(daily.getDeviceId().equals(aLong)){
                    setDaily(emsDailyData, emsDailyDtoS, aLong, daily);
                }
            }
        }
    }

    private void setDaily(List<EmsDailyDataDTO> emsDailyData, List<EmsDailyDataDTO> emsDailyDtoS, Long aLong, EmsDailyDataDTO daily) {
        EmsDailyDataDTO emsDailyDataDTO = new EmsDailyDataDTO();
        BigDecimal outPutPiece = valueOf(emsDailyData.stream().filter(r -> r.getDeviceId().equals(aLong)).mapToLong(EmsDailyDataDTO::getOutputPiece).average().getAsDouble());
        BigDecimal outPutModel = valueOf(emsDailyData.stream().filter(r->r.getDeviceId().equals(aLong)).mapToLong(EmsDailyDataDTO::getOutputModel).average().getAsDouble());
        BigDecimal allWeight = valueOf(emsDailyData.stream().filter(r->r.getDeviceId().equals(aLong)).mapToLong(EmsDailyDataDTO::getAllWeight).average().getAsDouble());
        BigDecimal prodTime = valueOf(emsDailyData.stream().filter(r->r.getDeviceId().equals(aLong)).mapToLong(EmsDailyDataDTO::getProdTime).average().getAsDouble());
        BigDecimal powerCons = valueOf(emsDailyData.stream().filter(r->r.getDeviceId().equals(aLong)).mapToLong(EmsDailyDataDTO::getPowerCons).average().getAsDouble());
        BigDecimal sum = valueOf(emsDailyData.stream().filter(r->r.getDeviceId().equals(aLong)).mapToLong(EmsDailyDataDTO::getMinuteCons).average().getAsDouble());
        emsDailyDataDTO.setDeviceId(aLong);
        emsDailyDataDTO.setCreateTime(daily.getCreateTime());
        emsDailyDataDTO.setProdName(daily.getProdName());
        emsDailyDataDTO.setCreateTime(daily.getCreateTime());
        emsDailyDataDTO.setOutputPiece(outPutPiece.longValue());
        emsDailyDataDTO.setOutputModel(outPutModel.longValue());
        emsDailyDataDTO.setAllWeight(allWeight.longValue());
        emsDailyDataDTO.setProdTime(prodTime.longValue());
        emsDailyDataDTO.setPowerCons(powerCons.longValue());
        emsDailyDataDTO.setMinuteCons(sum.longValue());
        emsDailyDataDTO.setDeviceId(aLong);
        emsDailyDtoS.add(emsDailyDataDTO);
    }
}
