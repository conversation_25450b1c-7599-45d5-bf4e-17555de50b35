package com.ems.energy.service;

import com.ems.energy.domain.EmsFactory;
import com.ems.energy.domain.tree.Emstreefactory;

import java.util.List;

/**
 * 企业架构Service接口
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface IEmsFactoryService
{
    /**
     * 查询企业架构
     *
     * @param deptId 企业架构主键
     * @return 企业架构
     */
    EmsFactory selectEmsFactoryByDeptId(Long deptId);

    /**
     * 查询企业架构列表
     *
     * @param emsFactory 企业架构
     * @return 企业架构集合
     */
    List<EmsFactory> selectEmsFactoryList(EmsFactory emsFactory);

    /**
     * 新增企业架构
     *
     * @param emsFactory 企业架构
     * @return 结果
     */
    int insertEmsFactory(EmsFactory emsFactory);

    /**
     * 修改企业架构
     *
     * @param emsFactory 企业架构
     * @return 结果
     */
    int updateEmsFactory(EmsFactory emsFactory);

    /**
     * 批量删除企业架构
     *
     * @param deptIds 需要删除的企业架构主键集合
     * @return 结果
     */
    int deleteEmsFactoryByDeptIds(Long[] deptIds);

    /**
     * 删除企业架构信息
     *
     * @param deptId 企业架构主键
     * @return 结果
     */
    int deleteEmsFactoryByDeptId(Long deptId);


    /**
     * 构建前端所需要树结构
     *
     * @param emsFactories 部门列表
     * @return 树结构列表
     */
    List<EmsFactory> builFacatoryselect(List<EmsFactory> emsFactories);


    List<Emstreefactory>  buildFactoryselect(List<EmsFactory> emsFactories);

    String checkDeptNameUnique(EmsFactory emsFactory);

    int insertFactory(EmsFactory emsFactory);

}
