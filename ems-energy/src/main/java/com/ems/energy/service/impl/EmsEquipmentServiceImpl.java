package com.ems.energy.service.impl;

import java.util.List;
import com.ems.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ems.energy.mapper.EmsEquipmentMapper;
import com.ems.energy.domain.EmsEquipment;
import com.ems.energy.service.IEmsEquipmentService;

/**
 * 设备管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
@Service
public class EmsEquipmentServiceImpl implements IEmsEquipmentService
{
    @Autowired
    private EmsEquipmentMapper emsEquipmentMapper;


    /**
     * 查询设备管理
     *
     * @param equipmentId 设备管理主键
     * @return 设备管理
     */
    @Override
    public EmsEquipment selectEmsEquipmentByEquipmentId(Long equipmentId)
    {
        return emsEquipmentMapper.selectEmsEquipmentByEquipmentId(equipmentId);
    }

    /**
     * 查询设备管理列表
     *
     * @param emsEquipment 设备管理
     * @return 设备管理
     */
    @Override
    public List<EmsEquipment> selectEmsEquipmentList(EmsEquipment emsEquipment)
    {
        return emsEquipmentMapper.selectEmsEquipmentList(emsEquipment);
    }

    /**
     * 新增设备管理
     *
     * @param emsEquipment 设备管理
     * @return 结果
     */
    @Override
    public int insertEmsEquipment(EmsEquipment emsEquipment)
    {
        emsEquipment.setCreateTime(DateUtils.getNowDate());
        return emsEquipmentMapper.insertEmsEquipment(emsEquipment);
    }

    /**
     * 修改设备管理
     *
     * @param emsEquipment 设备管理
     * @return 结果
     */
    @Override
    public int updateEmsEquipment(EmsEquipment emsEquipment)
    {
        emsEquipment.setUpdateTime(DateUtils.getNowDate());
        return emsEquipmentMapper.updateEmsEquipment(emsEquipment);
    }

    /**
     * 批量删除设备管理
     *
     * @param equipmentIds 需要删除的设备管理主键
     * @return 结果
     */
    @Override
    public int deleteEmsEquipmentByEquipmentIds(Long[] equipmentIds)
    {
        return emsEquipmentMapper.deleteEmsEquipmentByEquipmentIds(equipmentIds);
    }

    /**
     * 删除设备管理信息
     *
     * @param equipmentId 设备管理主键
     * @return 结果
     */
    @Override
    public int deleteEmsEquipmentByEquipmentId(Long equipmentId)
    {
        return emsEquipmentMapper.deleteEmsEquipmentByEquipmentId(equipmentId);
    }

    @Override
    public List<EmsEquipment> selectEquipmentByTag(String tag) {
        return emsEquipmentMapper.selectEquipmentByTag(tag);
    }
}
