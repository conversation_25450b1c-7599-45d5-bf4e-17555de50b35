package com.ems.energy.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.ems.energy.domain.EmsPrice;
import com.ems.energy.domain.EmsPriceConfig;

/**
 * 电价配置Service接口
 *
 * <AUTHOR>
 * @date 2022-05-30
 */
public interface IEmsPriceConfigService
{
    /**
     * 查询电价配置
     *
     * @param configId 电价配置主键
     * @return 电价配置
     */
    EmsPriceConfig selectEmsPriceConfigByConfigId(Long configId);

    /**
     * 查询电价配置列表
     *
     * @param emsPriceConfig 电价配置
     * @return 电价配置集合
     */
    List<EmsPriceConfig> selectEmsPriceConfigList(EmsPriceConfig emsPriceConfig);

    /**
     * 新增电价配置
     *
     * @param emsPriceConfig 电价配置
     * @return 结果
     */
    int insertEmsPriceConfig(EmsPriceConfig emsPriceConfig);

    /**
     * 修改电价配置
     *
     * @param emsPriceConfig 电价配置
     * @return 结果
     */
    int updateEmsPriceConfig(EmsPriceConfig emsPriceConfig);

    /**
     * 批量删除电价配置
     *
     * @param configIds 需要删除的电价配置主键集合
     * @return 结果
     */
    int deleteEmsPriceConfigByConfigIds(Long[] configIds);

    /**
     * 删除电价配置信息
     *
     * @param configId 电价配置主键
     * @return 结果
     */
    int deleteEmsPriceConfigByConfigId(Long configId);

    EmsPriceConfig selectValidEmsPriceConfigByTime(Date time);

    BigDecimal getElecPrice(Date time);

    EmsPrice getPriceByTime(Date time);

    //获取时间峰平谷分类
    String getCategory(Date time);
}
