package com.ems.energy.service.impl;


import com.ems.energy.domain.EmsCostMea;
import com.ems.energy.mapper.EmsCostMeaMapper;
import com.ems.energy.service.IEmsCostMeaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 成本中心和计量器具中间Service业务层处理
 * <AUTHOR>
 * @date 2022-01-04
 */
@Service
public class EmsCostMeaServiceImpl implements IEmsCostMeaService
{
    @Resource
    private EmsCostMeaMapper emsCostMeaMapper;

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 查询成本中心和计量器具中间
     * @param id 成本中心和计量器具中间主键
     * @return 成本中心和计量器具中间
     */
    @Override
    public EmsCostMea selectEmsCostMeaById(Long id)
    {
        return emsCostMeaMapper.selectEmsCostMeaById(id);
    }

    /**
     * 查询成本中心和计量器具中间列表
     * @param emsCostMea 成本中心和计量器具中间
     * @return 成本中心和计量器具中间
     */
    @Override
    public List<EmsCostMea> selectEmsCostMeaList(EmsCostMea emsCostMea)
    {
        return emsCostMeaMapper.selectEmsCostMeaList(emsCostMea);
    }

    /**
     * 新增成本中心和计量器具中间
     * @param emsCostMea 成本中心和计量器具中间
     * @return 结果
     */
    @Override
    public int insertEmsCostMea(EmsCostMea emsCostMea)
    {
        return emsCostMeaMapper.insertEmsCostMea(emsCostMea);
    }

    /**
     * 修改成本中心和计量器具中间
     * @param emsCostMea 成本中心和计量器具中间
     * @return 结果
     */
    @Override
    public int updateEmsCostMea(EmsCostMea emsCostMea)
    {
        return emsCostMeaMapper.updateEmsCostMea(emsCostMea);
    }

    /**
     * 批量删除成本中心和计量器具中间
     * @param ids 需要删除的成本中心和计量器具中间主键
     * @return 结果
     */
    @Override
    public int deleteEmsCostMeaByIds(Long[] ids)
    {
        return emsCostMeaMapper.deleteEmsCostMeaByIds(ids);
    }

    /**
     * 删除成本中心和计量器具中间信息
     * @param id 成本中心和计量器具中间主键
     * @return 结果
     */
    @Override
    public int deleteEmsCostMeaById(Long id)
    {
        return emsCostMeaMapper.deleteEmsCostMeaById(id);
    }

    /**
     * 根据成本中心ID删除承办中心和计量器具中间信息表
     * @param centerIds
     * @return
     */
    @Override
    public int deleteEmsCostMeaByCostCenterIds(Long[] centerIds) {
        return emsCostMeaMapper.deleteEmsCostMeaByCostCenterIds(centerIds);
    }
}
