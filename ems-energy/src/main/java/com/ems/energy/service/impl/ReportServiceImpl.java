package com.ems.energy.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ems.common.core.domain.entity.SysDictData;
import com.ems.common.utils.DateUtils;
import com.ems.energy.constant.EmsConstants;
import com.ems.energy.domain.*;
import com.ems.energy.domain.vo.InfluxDBPropertyVo;
import com.ems.energy.domain.vo.PartEnergyVo;
import com.ems.energy.mapper.*;
import com.ems.energy.service.IReportService;
import com.ems.energy.task.DataCompute;
import com.ems.system.service.ISysDictDataService;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class ReportServiceImpl implements IReportService {
    @Resource
    private EmsMeasuringToolMapper measuringToolMapper;

    @Resource
    private EmsEquipmentMapper equipmentMapper;

    @Resource
    private EmsSamplerDayReportMapper samplerDayReportMapper;

    @Resource
    private EmsCarbonFactorMapper carbonFactorMapper;

    @Resource
    private ISysDictDataService dictDataService;

    @Resource
    private InfluxDBClient influxDBClient;

    @Resource
    private ReportMapper reportMapper;

    @Resource
    private EmsPartMapper partMapper;

    @Resource
    private InfluxDBPropertyVo influxDBProperty;

    @Override
    public List<List<Object>> queryTopEnergyCostByTag(String tag, Date time) {
        List<List<Object>> result = new ArrayList<>();
        result.add(Arrays.asList("设备", "能耗（KWh）"));
        List<EmsEquipment> equipments = equipmentMapper.selectEquipmentByTag(tag);
        Map<Long, EmsEquipment> equipmentMap = new HashMap<>();
        equipments.forEach(emsEquipment -> {
            JSONObject jsonObject = JSON.parseObject(emsEquipment.getMeasuringToolConfig());
            if(jsonObject.containsKey("Energy")){
                equipmentMap.put(Long.parseLong(jsonObject.get("Energy").toString()), emsEquipment);
            }
        });
        if (equipmentMap.size() > 0) {
            List<EmsMeasuringTool> measuringTools = measuringToolMapper.selectEmsMeasuringToolListByIds(equipmentMap.keySet().toArray(new Long[0]));
            String start = DateFormatUtils.formatUTC(time, "yyyy-MM-dd'T'HH:mm:ss'Z'");
            Date endTime = DateUtils.addDays(time, 1);
            String end = DateFormatUtils.formatUTC(endTime, "yyyy-MM-dd'T'HH:mm:ss'Z'");
            String samplerListStr = JSON.toJSONString(measuringTools.stream().map(EmsMeasuringTool::getRealSampler).collect(Collectors.toList()));
            String influxQuery = "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                    "  |> range(start: " + start + ", stop: " + end + ")\n" +
                    "  |> filter(fn: (r) => r[\"_measurement\"] == \""+EmsConstants.INFLUX_POSITIVE_ENERGY_DAY+"\")\n" +
                    "  |> filter(fn: (r) => contains(value: r[\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"], set:" + samplerListStr + "))" +
                    "  |> group()" +
                    "  |> sort(desc: true)" +
                    "  |> limit(n:5)";
            System.out.println(influxQuery);
            List<FluxTable> influxData = influxDBClient.getQueryApi().query(influxQuery);
            if (influxData.size() > 0) {
                influxData.get(0).getRecords().forEach(fluxRecord -> {
                    String sampler = fluxRecord.getValueByKey(EmsConstants.INFLUX_SAMPLER_TAG).toString();
                    EmsMeasuringTool measuringTool = measuringTools.stream().filter(tool -> tool.getType().equals("virtual") ? tool.getMeasuringToolCode().equals(sampler) : tool.getSampler().equals(sampler)).findFirst().get();
                    result.add(Arrays.asList(equipmentMap.get(measuringTool.getMeasuringToolId()).getEquipmentName(), fluxRecord.getValue()));
                });
            }
        }
        return result;
    }

    private List<List<Object>> energyReportWithCategoryByMeasuringTool(Long measuringToolId, Date start, Date end){
        List<List<Object>> result = Lists.newArrayList();
        SysDictData search = new SysDictData();
        search.setStatus("0");
        search.setDictType(EmsConstants.ELEC_PRICE_PERIOD_CATEGORY_TYPE);
        List<SysDictData> dictDataList = dictDataService.selectDictDataList(search);
        List<String> categories = dictDataList.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
//        categories.add(EmsConstants.REPORT_ELEC_AMOUNT);
        List<Object> header = Lists.newArrayList();
        header.add("时间");
        header.addAll(dictDataList.stream().map(SysDictData::getDictLabel).collect(Collectors.toList()));
        header.add("平均电价");
        result.add(header);
        List<EmsSamplerDayReport> reports = new ArrayList<>();
        EmsMeasuringTool measuringTool = measuringToolMapper.selectEmsMeasuringToolByMeasuringToolId(measuringToolId);
        if (null != measuringTool) {
            EmsSamplerDayReport report = new EmsSamplerDayReport();
            report.setSampler(measuringTool.getRealSampler());
            report.getParams().put("beginReportDate", DateUtils.dateTime(start));
            report.getParams().put("endReportDate", DateUtils.dateTime(end));
            reports = samplerDayReportMapper.selectEmsSamplerDayReportList(report);
        }
        for (Date time = start; time.before(end); time = DateUtils.addDays(time, 1)){
            List<Object> dataList = Lists.newArrayList();
            dataList.add(DateUtils.dateTime(time));
            Date finalTime = time;
            List<EmsSamplerDayReport> dayReports = reports.stream().filter(emsSamplerDayReport -> DateUtils.isSameDay(emsSamplerDayReport.getReportDate(), finalTime)).collect(Collectors.toList());
            AtomicReference<BigDecimal> totalEnergy = new AtomicReference<>(new BigDecimal(0));
            categories.forEach(category->{
                BigDecimal energy = dayReports.stream().filter(emsSamplerDayReport -> emsSamplerDayReport.getCategory().equals(category)).findFirst().map(EmsSamplerDayReport::getValue).orElse(new BigDecimal(0));
                dataList.add(energy);
                totalEnergy.set(totalEnergy.get().add(energy));
            });
            if(totalEnergy.get().compareTo(new BigDecimal(0))<=0){
                dataList.add(new BigDecimal(0));
            }else{
                BigDecimal amount = dayReports.stream().filter(emsSamplerDayReport -> emsSamplerDayReport.getCategory().equals(EmsConstants.REPORT_ELEC_AMOUNT)).findFirst().map(EmsSamplerDayReport::getValue).orElse(new BigDecimal(0));
                dataList.add(amount.divide(totalEnergy.get(), 4, RoundingMode.HALF_UP));
            }

            result.add(dataList);
        }
        return result;
    }

    @Override
    public List<List<Object>> energyReportWithCategoryByEquipment(Long equipmentId, Date start, Date end) {
        EmsEquipment equipment = equipmentMapper.selectEmsEquipmentByEquipmentId(equipmentId);
        List<EmsSamplerDayReport> reports = new ArrayList<>();
        if (null != equipment){
            JSONObject jsonObject = JSON.parseObject(equipment.getMeasuringToolConfig());
            if(jsonObject.containsKey("Energy")) {
                Long measuringToolId = Long.parseLong(jsonObject.get("Energy").toString());
                return energyReportWithCategoryByMeasuringTool(measuringToolId, start, end);
            }
        }

        return Lists.newArrayList();
    }

    @Override
    public List<List<Object>> factoryEnergyReportWithCategory(Date start, Date end) {
        List<EmsMeasuringTool> list = measuringToolMapper.selectEmsMeasuringToolByTag(EmsConstants.TOTAL_ELEC_TAG);
        if (list.size() > 0){
            return energyReportWithCategoryByMeasuringTool(list.get(0).getMeasuringToolId(), start, end);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<List<Object>> queryMonthDayValueBySampler(String sampler, List<String> categories, String month) {
        List<List<Object>> result = Lists.newArrayList();
        List<Map<String, Object>> mapList = reportMapper.queryMonthDayEnergyBySampler(sampler, categories, month);
//        mapList.forEach(map->{
//            result.add(Arrays.asList(map.get("time"), map.get("value")));
//        });
        Date start = DateUtils.parseDate(month);
        for (Date time = start ;time.before(DateUtils.addMonths(start, 1)); time = DateUtils.addDays(time, 1)){
            List<Object> dataList = Lists.newArrayList();
            String timeTag = DateUtils.dateTime(time);
            dataList.add(timeTag);
            Object value = "";
            for (Map<String, Object> map : mapList){
                if (timeTag.equals(map.get("time"))){
                    value = map.get("value");
                    break;
                }
            }
            dataList.add(value);
            result.add(dataList);
        }
        return result;
    }

    @Override
    public List<List<Object>> queryMonthDayEnergyBySampler(String sampler, String month) {
//        List<String> categories = getElecPricePeriodCategories();
//        List<List<Object>> result = queryMonthDayValueBySampler(sampler, categories, month);
//        result.add(0, Arrays.asList("时间", "能耗"));
//        return result;
        List<List<Object>> result = Lists.newArrayList();
        result.add(Arrays.asList("时间", "用电量"));
        result.addAll(queryMonthDayDataFromInflux(sampler, month, EmsConstants.INFLUX_POSITIVE_ENERGY_HALF_HOUR));
        return result;
    }

    @NotNull
    private List<String> getElecPricePeriodCategories() {
        List<SysDictData> dictDataList = getDictDataList(EmsConstants.ELEC_PRICE_PERIOD_CATEGORY_TYPE);
        return dictDataList.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
    }

    private List<SysDictData> getDictDataList(String dictType){
        SysDictData search = new SysDictData();
        search.setStatus("0");
        search.setDictType(dictType);
        return dictDataService.selectDictDataList(search);
    }


    @Override
    public List<List<Object>> queryMonthDayElecAmountBySampler(String sampler, String month) {
//        List<List<Object>> result = queryMonthDayValueBySampler(sampler, Arrays.asList(EmsConstants.REPORT_ELEC_AMOUNT), month);
//        result.add(0, Arrays.asList("时间", "电费"));
//        return result;
        List<List<Object>> result = Lists.newArrayList();
        result.add(Arrays.asList("时间", "电费"));
        result.addAll(queryMonthDayDataFromInflux(sampler, month, EmsConstants.INFLUX_ELEC_AMOUNT_HALF_HOUR));
        return result;
    }

    private List<List<Object>> queryMonthDayDataFromInflux(String sampler, String month, String measurement){
        List<List<Object>> result = Lists.newArrayList();
//        result.add(Arrays.asList("时间", "碳排放"));
        Date startDate = DateUtils.parseDate(month);
        Date endDate = DateUtils.addMonths(startDate, 1);
        String start = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(startDate);
        String end = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(endDate);
        String influxQuery = "import \"timezone\"\n" +
                "option location = timezone.fixed(offset: 8h)\n"+
                "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                "  |> range(start: " + start + ", stop: " + end + ")\n" +
                "  |> filter(fn: (r) => r[\"_measurement\"] == \""+measurement+"\")\n" +
                "  |> filter(fn: (r) => r[\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"]==\"" + sampler + "\")" +
                "  |> aggregateWindow(every: 1d, fn: sum, timeSrc:\"_start\")";
        List<FluxTable> tables = influxDBClient.getQueryApi().query(influxQuery);
        if (tables.size() > 0){
            for (FluxRecord record : tables.get(0).getRecords()){
                List<Object> list = Lists.newArrayList();
                list.add(DateUtils.dateTime(Date.from(record.getTime())));
                list.add(null == record.getValue()?"": new BigDecimal(record.getValue().toString()).setScale(2, RoundingMode.HALF_UP));
                result.add(list);
            }
        }else{
            for (Date time = startDate ;time.before(endDate); time = DateUtils.addDays(time, 1)){
                List<Object> dataList = Lists.newArrayList();
                String timeTag = DateUtils.dateTime(time);
                dataList.add(timeTag);
                dataList.add("");
                result.add(dataList);
            }
        }
        return result;
    }

    @Override
    public List<List<Object>> queryMonthDayCarbonBySampler(String sampler, String month) {
        List<List<Object>> result = Lists.newArrayList();
        result.add(Arrays.asList("时间", "碳排放"));
        result.addAll(queryMonthDayDataFromInflux(sampler, month, EmsConstants.INFLUX_CARBON_HALF_HOUR));
        return result;
    }

    @Override
    public List<List<Object>> queryFactoryMonthDayEnergy(String month) {
        List<EmsMeasuringTool> tools = measuringToolMapper.selectEmsMeasuringToolByTag(EmsConstants.TOTAL_ELEC_TAG);
        if (null != tools && tools.size() > 0){
            return queryMonthDayEnergyBySampler(tools.get(0).getRealSampler(), month);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<List<Object>> queryFactoryMonthDayElecAmount(String month) {
        List<EmsMeasuringTool> tools = measuringToolMapper.selectEmsMeasuringToolByTag(EmsConstants.TOTAL_ELEC_TAG);
        if (null != tools && tools.size() > 0){
            return queryMonthDayElecAmountBySampler(tools.get(0).getRealSampler(), month);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<List<Map<String, Object>>> queryMonthEnergyReport(String year) {
        List<List<Map<String, Object>>> result = Lists.newArrayList();
        Date startDate = DateUtils.dateTime("yyyy", year);
        Date endDate = DateUtils.addYears(startDate, 1);
        String start = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(startDate);
        String end = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(endDate);

        List<SysDictData> dictDataList = getDictDataList(EmsConstants.TOP_CATEGORY_DICT);
        List<String> categories = dictDataList.stream().map(SysDictData::getDictLabel).collect(Collectors.toList());
        Map<String, String> samplerMap = getCategorySamplerMap(categories);
        List<EmsMeasuringTool> tools = measuringToolMapper.selectEmsMeasuringToolByTag(EmsConstants.TOTAL_ELEC_TAG);
        if (null != tools && tools.size() > 0){
            samplerMap.put("总和", tools.get(0).getRealSampler());
        }
        categories.add("总和");
        List<FluxTable> tables = querySamplerMonthData(start, end, samplerMap, EmsConstants.INFLUX_POSITIVE_ENERGY_HALF_HOUR);
        result.add(getDataList(samplerMap, categories, tables));
        tables = querySamplerMonthData(start, end, samplerMap, EmsConstants.INFLUX_CARBON_HALF_HOUR);
        result.add(getDataList(samplerMap, categories, tables));
        tables = querySamplerMonthData(start, end, samplerMap, EmsConstants.INFLUX_ELEC_AMOUNT_HALF_HOUR);
        result.add(getDataList(samplerMap, categories, tables));
        return result;
    }

    @NotNull
    private List<Map<String, Object>> getDataList(Map<String, String> samplerMap, List<String> categories, List<FluxTable> tables) {
        List<Map<String, Object>> subList = Lists.newArrayList();
        for (String category : categories){
            Map<String, Object> map = new HashMap<>();
            map.put("category", category);
            for (FluxTable table : tables) {
                if (table.getRecords().size() > 0) {
                    if (table.getRecords().get(0).getValueByKey(EmsConstants.INFLUX_SAMPLER_TAG).equals(samplerMap.get(category))){
                        for (FluxRecord record : table.getRecords()){
                            String key = "m"+DateUtils.parseDateToStr("MM", Date.from(record.getTime()));
                            if (null == record.getValue()){
                                map.put(key, "");
                            }else{
                                map.put(key, new BigDecimal(record.getValue().toString()).setScale(2, RoundingMode.HALF_UP).toString());
                            }
                        }
                        break;
                    }
                }
            }
            subList.add(map);

        }
        return subList;
    }

    @Override
    public List<List<Object>> queryFactoryMonthDayCarbon(String month) {
        List<EmsMeasuringTool> tools = measuringToolMapper.selectEmsMeasuringToolByTag(EmsConstants.TOTAL_ELEC_TAG);
        if (null != tools && tools.size() > 0){
            return queryMonthDayCarbonBySampler(tools.get(0).getRealSampler(), month);
        }
        return Lists.newArrayList();
    }

    private List<List<Object>> queryOneMonthCategoryDataReport(String month, String measurement, String valueName){
        List<List<Object>> result = Lists.newArrayList();
        List<SysDictData> dictDataList = getDictDataList(EmsConstants.TOP_CATEGORY_DICT);
        List<String> categories = dictDataList.stream().map(SysDictData::getDictLabel).collect(Collectors.toList());
//        categories.add(EmsConstants.REPORT_ELEC_AMOUNT);
        List<Object> header = Lists.newArrayList();
        header.add("分类");
        header.add(valueName);
        result.add(header);
        Date startDate = DateUtils.parseDate(month);
        Date endDate = DateUtils.addMonths(startDate, 1);
        String start = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(startDate);
        String end = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(endDate);
        Map<String, String> samplerMap = getCategorySamplerMap(categories);
        List<FluxTable> tables = querySamplerMonthData(start, end, samplerMap, measurement);

        categories.forEach(category->{
            List<Object> data = Lists.newArrayList();
            data.add(category);
            String sampler = samplerMap.get(category);
            if (null != sampler){
                for(FluxTable table : tables){
                    if (table.getRecords().size() > 0 && sampler.equals(table.getRecords().get(0).getValueByKey(EmsConstants.INFLUX_SAMPLER_TAG))){
                        if (null != table.getRecords().get(0).getValue()){
                            data.add(new BigDecimal(table.getRecords().get(0).getValue().toString()).setScale(2, RoundingMode.HALF_UP));
                        }else{
                            data.add(new BigDecimal(0));
                        }
                        break;
                    }
                }
            }else{
                data.add(new BigDecimal(0));
            }
            result.add(data);
        });

        return result;
    }

    @Override
    public List<List<Object>> queryOneMonthCategoryEnergyReport(String month) {
        return queryOneMonthCategoryDataReport(month, EmsConstants.INFLUX_POSITIVE_ENERGY_HALF_HOUR,"用电量");
    }


    private List<FluxTable> querySamplerMonthData(String start, String end, Map<String, String> samplerMap, String measurement) {
//        String samplerListStr = JSON.toJSONString(samplerMap.values());
        String samplerFilter = samplerMap.values().stream().map(sampler->"r[\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"]==\""+sampler+"\"").collect(Collectors.joining(" or "));
        String influxQuery = "import \"timezone\"\n" +
                "option location = timezone.fixed(offset: 8h)\n"+
                "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                "  |> range(start: " + start + ", stop: " + end + ")\n" +
                "  |> filter(fn: (r) => r[\"_measurement\"] == \""+measurement+"\")\n" +
                "  |> filter(fn: (r) => " + samplerFilter +")\n" +
                "  |> aggregateWindow(every: 1mo, fn: sum, timeSrc:\"_start\")";
        System.out.println(influxQuery);
        return influxDBClient.getQueryApi().query(influxQuery);
    }

    private Map<String, String> getCategorySamplerMap(List<String> categories) {
        Map<String, String> samplerMap = new HashMap<>();
        categories.forEach(category->{
            String tag = category + EmsConstants.SUB_TOTAL_ELEC_TAG;
            List<EmsMeasuringTool> toolList = measuringToolMapper.selectEmsMeasuringToolByTag(tag);
            if (null != toolList && toolList.size() > 0){
                samplerMap.put(category, toolList.get(0).getRealSampler());
            }
        });
        return samplerMap;
    }

    @Override
    public List<List<Object>> queryOneMonthCategoryCarbonReport(String month) {
        return queryOneMonthCategoryDataReport(month, EmsConstants.INFLUX_CARBON_HALF_HOUR, "碳排放");
    }

    @Override
    public List<List<Object>> queryOneMonthCategoryElecAmountReport(String month) {
        return queryOneMonthCategoryDataReport(month, EmsConstants.INFLUX_ELEC_AMOUNT_HALF_HOUR, "电费");
    }

    @Override
    public BigDecimal queryOneMonthTotalEnergy(String month) {
        List<EmsMeasuringTool> tools = measuringToolMapper.selectEmsMeasuringToolByTag(EmsConstants.TOTAL_ELEC_TAG);
        if (null != tools && tools.size() > 0){
            return querySamplerOneMonthData(month, EmsConstants.INFLUX_POSITIVE_ENERGY_HALF_HOUR, tools.get(0).getRealSampler());
        }
        return BigDecimal.ZERO;
    }

    @Override
    public BigDecimal queryOneMonthTotalCarbon(String month) {
        List<EmsMeasuringTool> tools = measuringToolMapper.selectEmsMeasuringToolByTag(EmsConstants.TOTAL_ELEC_TAG);
        if (null != tools && tools.size() > 0){
            return querySamplerOneMonthData(month, EmsConstants.INFLUX_CARBON_HALF_HOUR, tools.get(0).getRealSampler());
        }
        return BigDecimal.ZERO;
    }

    @Override
    public BigDecimal queryOneMonthTotalElecAmount(String month) {
        List<EmsMeasuringTool> tools = measuringToolMapper.selectEmsMeasuringToolByTag(EmsConstants.TOTAL_ELEC_TAG);
        if (null != tools && tools.size() > 0){
            return querySamplerOneMonthData(month, EmsConstants.INFLUX_ELEC_AMOUNT_HALF_HOUR, tools.get(0).getRealSampler());
        }
        return BigDecimal.ZERO;
    }

    @Override
    public List<List<Object>> queryFactoryMd(String start, String end) {
        List<EmsMeasuringTool> tools = measuringToolMapper.selectEmsMeasuringToolByTag(EmsConstants.TOTAL_MD_TAG);
        if (tools.size()>0){
            return queryMdBySampler(tools.get(0).getRealSampler(), start, end);
        }
        return Lists.newArrayList();
    }

    private BigDecimal querySamplerOneMonthData(String month, String measurement, String sampler){
        Date startDate = DateUtils.parseDate(month);
        Date endDate = DateUtils.addMonths(startDate, 1);
        String start = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(startDate);
        String end = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(endDate);
        String influxQuery = "import \"timezone\"\n" +
                "option location = timezone.fixed(offset: 8h)\n"+
                "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                "  |> range(start: " + start + ", stop: " + end + ")\n" +
                "  |> filter(fn: (r) => r[\"_measurement\"] == \""+measurement+"\")\n" +
                "  |> filter(fn: (r) => r[\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"]==\"" + sampler + "\")" +
                "  |> aggregateWindow(every: 1mo, fn: sum, timeSrc:\"_start\")";
        System.out.println(influxQuery);
        List<FluxTable> tables = influxDBClient.getQueryApi().query(influxQuery);
        if (tables.size() > 0 && tables.get(0).getRecords().size() > 0 && null != tables.get(0).getRecords().get(0).getValue()){
            return new BigDecimal(tables.get(0).getRecords().get(0).getValue().toString()).setScale(2, RoundingMode.HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    private List<List<Object>> queryMdBySampler(String sampler, String start, String end){
        List<List<Object>> result = Lists.newArrayList();
        result.add(Arrays.asList("时间", "MD值", "MD峰值"));
        String queryStart = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(DateUtils.parseDate(start));
        String queryEnd = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(DateUtils.parseDate(end));
        String influxQuery =
                "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                "  |> range(start: " + queryStart + ", stop: " + queryEnd + ")\n" +
                "  |> filter(fn: (r) => r[\"_measurement\"] == \""+EmsConstants.INFLUX_MD_HALF_HOUR+"\")\n" +
                "  |> filter(fn: (r) => r[\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"]==\"" + sampler + "\")";
        System.out.println(influxQuery);
        List<FluxTable> tables = influxDBClient.getQueryApi().query(influxQuery);
        if (tables.size() > 0){
            BigDecimal max = BigDecimal.ZERO;
            for (int i = 0; i < tables.get(0).getRecords().size(); i++){
                FluxRecord record = tables.get(0).getRecords().get(i);
                if (null != record.getValue()){
                    List<Object> dataList = Lists.newArrayList();
                    dataList.add(Date.from(record.getTime()));
                    BigDecimal value = new BigDecimal(record.getValue().toString()).setScale(2,RoundingMode.HALF_UP);
                    dataList.add(value);
                    if (max.compareTo(value) < 0){
                        max = value;

                    }
                    dataList.add(max);
                    result.add(dataList);
                }
            }
        }
        return result;
    }

    private BigDecimal queryPartCount(EmsPart part, Date start, Date end){
        String queryStart = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(start);
        String queryEnd = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(end);
        String influxQuery =
                "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                "  |> range(start: " + queryStart + ", stop: " + queryEnd + ")\n" +
                "  |> filter(fn: (r) => r[\"_measurement\"] == \""+EmsConstants.INFLUX_PROCESS_PART_COUNT_HALF_HOUR+"\")\n" +
                "  |> filter(fn: (r) => r[\""+EmsConstants.INFLUX_PART_PATTERN_TAG+"\"]==\"" + part.getPattern() + "\")" +
                "  |> group()" +
                "  |> sum()";
        List<FluxTable> tables = influxDBClient.getQueryApi().query(influxQuery);
        if (tables.size() > 0 && tables.get(0).getRecords().size() > 0){
            Object value = tables.get(0).getRecords().get(0).getValue();
            if (null != value){
                return new BigDecimal(value.toString()).divide(new BigDecimal(Float.toString(part.getProcessCount())), 2, RoundingMode.HALF_UP);
            }
        }
        return BigDecimal.ZERO;
    }

    private Map<String, BigDecimal> queryPartCountMap(List<? extends EmsPart> parts, Date start, Date end){
        Map<String, BigDecimal> result = new HashMap<>();
        String queryStart = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(start);
        String queryEnd = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(end);
        List<Map<String, Object>> partPatternMapList = reportMapper.querySamplerListByPartIds(parts.stream().map(EmsPart::getPartId).toArray(Long[]::new));
        String patternList = JSON.toJSONString(partPatternMapList.stream().map(partPatternMap->partPatternMap.get("part_pattern")).collect(Collectors.toList()));
        String influxQuery =
                "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                        "  |> range(start: " + queryStart + ", stop: " + queryEnd + ")\n" +
                        "  |> filter(fn: (r) => r[\"_measurement\"] == \""+EmsConstants.INFLUX_PROCESS_PART_COUNT_HALF_HOUR+"\")\n" +
                        "  |> filter(fn: (r) => contains(value: (r[\""+EmsConstants.INFLUX_PART_PATTERN_TAG+"\"]+\"|\"+r[\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"]), set:" + patternList + "))" +
                        "  |> keep(columns:[\"_time\",\"_value\",\""+EmsConstants.INFLUX_PART_PATTERN_TAG+"\",\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"])" +
                        "  |> sum()";
        List<FluxTable> tables = influxDBClient.getQueryApi().query(influxQuery);
        if (tables.size() > 0){
            for (FluxTable table : tables){
                if (table.getRecords().size() > 0){
                    Object value = table.getRecords().get(0).getValue();
                    if (null != value){
                        BigDecimal total = new BigDecimal(value.toString());
                        String pattern = table.getRecords().get(0).getValueByKey(EmsConstants.INFLUX_PART_PATTERN_TAG).toString()+"|"+table.getRecords().get(0).getValueByKey(EmsConstants.INFLUX_SAMPLER_TAG).toString();
//                        EmsPart part = parts.stream().filter(emsPart -> emsPart.getPattern().equals(pattern)).findFirst().get();
                        Long partId = Long.parseLong(partPatternMapList.stream().filter(partPatternMap->pattern.equals(partPatternMap.get("part_pattern"))).findFirst().get().get("part_id").toString());
                        EmsPart part = parts.stream().filter(emsPart -> emsPart.getPartId().equals(partId)).findFirst().get();
                        result.put(partId.toString(), total.divide(new BigDecimal(Float.toString(part.getProcessCount())), 4, RoundingMode.HALF_UP));
                    }
                }
            }
        }
        return result;
    }

    private Map<String, BigDecimal> queryPartEnergyMap(List<? extends EmsPart> parts, Date start, Date end){
        Map<String, BigDecimal> result = new HashMap<>();
        String queryStart = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(start);
        String queryEnd = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(end);
        List<Map<String, Object>> partPatternMapList = reportMapper.querySamplerListByPartIds(parts.stream().map(EmsPart::getPartId).toArray(Long[]::new));
        String patternList = JSON.toJSONString(partPatternMapList.stream().map(partPatternMap->partPatternMap.get("part_pattern")).collect(Collectors.toList()));
        String influxQuery =
                "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                        "  |> range(start: " + queryStart + ", stop: " + queryEnd + ")\n" +
                        "  |> filter(fn: (r) => r[\"_measurement\"] == \""+EmsConstants.INFLUX_PROCESS_PART_ENERGY_HALF_HOUR+"\")\n" +
                        "  |> filter(fn: (r) => r[\"_field\"] == \"value\")" +
                        "  |> filter(fn: (r) => contains(value: (r[\""+EmsConstants.INFLUX_PART_PATTERN_TAG+"\"]+\"|\"+r[\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"]), set:" + patternList + "))" +
                        "  |> keep(columns:[\"_time\",\"_value\",\""+EmsConstants.INFLUX_PART_PATTERN_TAG+"\",\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"])" +
                        "  |> sum()";
        List<FluxTable> tables = influxDBClient.getQueryApi().query(influxQuery);
        if (tables.size() > 0){
            for (FluxTable table : tables){
                if (table.getRecords().size() > 0){
                    Object value = table.getRecords().get(0).getValue();
                    if (null != value){
                        String pattern = table.getRecords().get(0).getValueByKey(EmsConstants.INFLUX_PART_PATTERN_TAG).toString()+"|"+table.getRecords().get(0).getValueByKey(EmsConstants.INFLUX_SAMPLER_TAG).toString();
//                        EmsPart part = parts.stream().filter(emsPart -> emsPart.getPattern().equals(pattern)).findFirst().get();
                        String partId = partPatternMapList.stream().filter(partPatternMap->pattern.equals(partPatternMap.get("part_pattern"))).findFirst().get().get("part_id").toString();
                        result.put(partId.toString(), new BigDecimal(value.toString()));
                    }
                }
            }
        }
        return result;
    }

    private void setCarbonFactor(PartEnergyVo part, EmsCarbonFactor carbonFactor){
        if (null != carbonFactor){
            part.setCarbonFactor(new BigDecimal(Float.toString(carbonFactor.getCarbonFactor())));
            part.setCarbonFactorUnit(carbonFactor.getUnit());
        }else{
            part.setCarbonFactorUnit("");
            part.setCarbonFactor(BigDecimal.ZERO);
        }
    }

    @Override
    public List<PartEnergyVo> queryPcfReport(Long partId, Date start, Date end){
        List<PartEnergyVo> partList = Lists.newArrayList();
        EmsPart part = partMapper.selectEmsPartByPartId(partId);
        EmsCarbonFactor carbonFactor = carbonFactorMapper.selectValidEmsCarbonFactor(start, EmsConstants.ENERGY_CATEGORY_ELECTRICITY);

        if (null != part){
            PartEnergyVo parentPart = new PartEnergyVo(part);
            parentPart.setTotalCount(BigDecimal.ONE);
            partList.add(parentPart);
            setCarbonFactor(parentPart, carbonFactor);
            partList.addAll(getAllSubPartList(parentPart, carbonFactor));
            Map<String, BigDecimal> energyMap = queryPartEnergyMap(partList, start, end);
            Map<String, BigDecimal> countMap = queryPartCountMap(partList, start, end);
            for (PartEnergyVo partVo : partList){
                BigDecimal count = countMap.get(partVo.getPartId().toString());
                if (null != count && count.compareTo(BigDecimal.ZERO)>0){
                    BigDecimal energy = energyMap.getOrDefault(partVo.getPartId().toString(), BigDecimal.ZERO);
                    partVo.setSelfEnergy(energy.divide(count, 4, RoundingMode.HALF_UP));
                }else{
                    partVo.setSelfEnergy(BigDecimal.ZERO);
                }
            }
            parentPart.setTotalEnergy(computePartTotalEnergy(parentPart, partList));
        }
        return partList;
    }

    private BigDecimal computePartTotalEnergy(PartEnergyVo part, List<PartEnergyVo> partList){
        List<PartEnergyVo> subParts = partList.stream().filter(partEnergyVo -> Objects.equals(partEnergyVo.getParentId(), part.getPartId())).collect(Collectors.toList());
        BigDecimal result = part.getSelfEnergy();
        for(PartEnergyVo subPart : subParts){
            if (null == subPart.getTotalEnergy()){
                subPart.setTotalEnergy(computePartTotalEnergy(subPart, partList));
            }
            result = result.add(subPart.getTotalEnergy().multiply(new BigDecimal(Float.toString(subPart.getUseCount()))));
        }
        return result;
    }

    private List<PartEnergyVo> getAllSubPartList(PartEnergyVo parentPart, EmsCarbonFactor carbonFactor){
        List<PartEnergyVo> result = Lists.newArrayList();
        EmsPart search = new EmsPart();
        search.setParentId(parentPart.getPartId());
        List<EmsPart> childList = partMapper.selectEmsPartList(search);
        for (EmsPart part : childList){
            PartEnergyVo subPart = new PartEnergyVo(part);
            subPart.setTotalCount(parentPart.getTotalCount().multiply(new BigDecimal(Float.toString(subPart.getUseCount()))));
            setCarbonFactor(subPart, carbonFactor);
            result.add(subPart);
            result.addAll(getAllSubPartList(subPart, carbonFactor));
        }
        return result;
    }

    @Override
    public List<List<Object>> queryPartsEquivalentCarbon(@NotNull List<Integer> partIds, Date start, Date end){
        List<List<Object>> result = Lists.newArrayList();
        result.add(Arrays.asList("零件", "当量碳排放"));
        for (Integer partId : partIds){
            List<PartEnergyVo> pcfList = queryPcfReport(partId.longValue(), start, end);
            if (pcfList.size() > 0){
                PartEnergyVo part = pcfList.get(0);
                List<Object> dataList = Lists.newArrayList();
                dataList.add(part.getPartName());
                dataList.add(part.getTotalEnergy().divide(part.getTotalCount().multiply(new BigDecimal(Float.toString(part.getEquivalent()))), 2, RoundingMode.HALF_UP));
                result.add(dataList);
            }
        }
        return result;
    }

    private BigDecimal queryEquipmentEquivalent(Long equipmentId, String sampler, Date start, Date end){
        String queryStart = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(start);
        String queryEnd = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(end);
        String influxQuery =
                "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                        "  |> range(start: " + queryStart + ", stop: " + queryEnd + ")\n" +
                        "  |> filter(fn: (r) => r[\"_measurement\"] == \""+EmsConstants.INFLUX_PROCESS_PART_COUNT_HALF_HOUR+"\")\n" +
                        "  |> filter(fn: (r) => r[\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"]==\"" + sampler + "\")" +
                        "  |> sum()";
        List<FluxTable> tables = influxDBClient.getQueryApi().query(influxQuery);
        for (FluxTable table : tables){
            if (table.getRecords().size() > 0 && null != table.getRecords().get(0).getValue()){
                String partPattern = table.getRecords().get(0).getValueByKey(EmsConstants.INFLUX_PART_PATTERN_TAG).toString();
                BigDecimal count = new BigDecimal(table.getRecords().get(0).getValue().toString());
                EmsPart part = queryPartByPattern(partPattern, equipmentId);
                if (null != part){
                    return new BigDecimal(part.getEquivalent().toString()).multiply(count).setScale(2, RoundingMode.HALF_UP);
                }
            }
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal queryEquipmentProcessTime(String sampler, Date start, Date end){
        return querySamplerSumData(sampler, start, end, EmsConstants.INFLUX_PROCESS_PART_ENERGY_HALF_HOUR, EmsConstants.INFLUX_PROCESS_TIME_COL);
    }

    private EmsPart queryPartByPattern(String partPattern, Long equipmentId){
        return partMapper.selectEmsPartByEquipmentIdAndPartPattern(equipmentId, partPattern);
    }

    private String getEquipmentSampler(EmsEquipment equipment, String type){
        JSONObject jsonObject = JSON.parseObject(equipment.getMeasuringToolConfig());
        if(jsonObject.containsKey(type)){
            Long measuringToolId = Long.parseLong(jsonObject.get(type).toString());
            EmsMeasuringTool measuringTool = measuringToolMapper.selectEmsMeasuringToolByMeasuringToolId(measuringToolId);
            if (null != measuringTool){
                return measuringTool.getRealSampler();
            }
        }

        return null;
    }

    private BigDecimal querySamplerSumData(String sampler, Date start, Date end, String measurement, String valueCol){
        String queryStart = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(start);
        String queryEnd = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(end);
        String influxQuery =
                "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                        "  |> range(start: " + queryStart + ", stop: " + queryEnd + ")\n" +
                        "  |> filter(fn: (r) => r[\"_measurement\"] == \""+measurement+"\")\n" +
                        "  |> filter(fn: (r) => r[\"_field\"] == \""+valueCol+"\")"+
                        "  |> filter(fn: (r) => r[\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"]==\"" + sampler + "\")" +
                        "  |> group()" +
                        "  |> sum()";
        List<FluxTable> tables = influxDBClient.getQueryApi().query(influxQuery);
        if (tables.size() > 0 && tables.get(0).getRecords().size() > 0){
            Object value = tables.get(0).getRecords().get(0).getValue();
            if (null != value){
                return new BigDecimal(value.toString());
            }
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal querySamplerSumData(String sampler, Date start, Date end, String measurement){
        return querySamplerSumData(sampler, start, end, measurement, "value");
    }

    private BigDecimal querySamplerEnergy(String sampler, Date start, Date end){
        return querySamplerSumData(sampler, start, end, EmsConstants.INFLUX_POSITIVE_ENERGY_HALF_HOUR);
    }

    @Override
    public Map<String, Object> queryEquipmentCarbonDayReport(Long equipmentId, String day){
        Date start = DateUtils.parseDate(day);
        Date end = DateUtils.addDays(start, 1);
        Map<String, Object> result = new HashMap<>();
        EmsEquipment equipment = equipmentMapper.selectEmsEquipmentByEquipmentId(equipmentId);
        if (null != equipment){
            result.put("equipmentName", equipment.getEquipmentName());
            String partCountSampler = getEquipmentSampler(equipment, EmsConstants.PART_COUNT);
            BigDecimal processTime = BigDecimal.ZERO;
            BigDecimal equivalent = BigDecimal.ZERO;
            if (null != partCountSampler){
                equivalent = queryEquipmentEquivalent(equipmentId, partCountSampler, start, end);
                processTime = queryEquipmentProcessTime(partCountSampler, start, end);
            }
            result.put("equivalent", equivalent);
            result.put("processTime", processTime);
            String energySampler = getEquipmentSampler(equipment, EmsConstants.ENERGY);
            BigDecimal energy = BigDecimal.ZERO;
            BigDecimal carbon = BigDecimal.ZERO;
            if (null != energySampler){
                energy = querySamplerEnergy(energySampler, start, end).setScale(2, RoundingMode.HALF_UP);
                carbon = querySamplerSumData(energySampler, start, end, EmsConstants.INFLUX_CARBON_HALF_HOUR).setScale(2, RoundingMode.HALF_UP);
            }
            result.put("energy", energy);
            result.put("carbon", carbon);


        }
        return result;
    }

    @Override
    public List<List<Object>> queryEquipmentsEquivalentCarbonReport(List<Integer> equipmentIds, Date start, Date end){
        List<List<Object>> result = Lists.newArrayList();
        result.add(Arrays.asList("设备","当量碳排放"));
        for (Integer equipmentId : equipmentIds){
            EmsEquipment equipment = equipmentMapper.selectEmsEquipmentByEquipmentId(equipmentId.longValue());
            if (null != equipment){
                String energySampler = getEquipmentSampler(equipment, EmsConstants.ENERGY);
                String partCountSampler = getEquipmentSampler(equipment, EmsConstants.PART_COUNT);
                BigDecimal equivalentCarbon = BigDecimal.ZERO;
                if (null != energySampler && null != partCountSampler){
                    BigDecimal carbon = querySamplerSumData(energySampler, start, end, EmsConstants.INFLUX_CARBON_HALF_HOUR);
                    BigDecimal equivalent = queryEquipmentEquivalent(equipment.getEquipmentId(), partCountSampler, start, end);
                    if (!BigDecimal.ZERO.equals(equivalent)){
                        equivalentCarbon = carbon.divide(equivalent, 4, RoundingMode.HALF_UP);
                    }
                }
                result.add(Arrays.asList(equipment.getEquipmentName(), equivalentCarbon));
            }
        }
        return result;
    }

    @Override
    public List<List<Object>> queryEquipmentsOeeReport(List<Integer> equipmentIds, Date start, Date end){
        List<List<Object>> result = Lists.newArrayList();
        result.add(Arrays.asList("设备","能源有效利用率", "设备有效利用率"));
        for (Integer equipmentId : equipmentIds) {
            EmsEquipment equipment = equipmentMapper.selectEmsEquipmentByEquipmentId(equipmentId.longValue());
            if (null != equipment) {
                String energySampler = getEquipmentSampler(equipment, EmsConstants.ENERGY);
                String partCountSampler = getEquipmentSampler(equipment, EmsConstants.PART_COUNT);
                if (null != energySampler && null != partCountSampler){
                    BigDecimal energy = querySamplerEnergy(energySampler, start, end);
                    BigDecimal processEnergy = querySamplerSumData(partCountSampler, start, end, EmsConstants.INFLUX_PROCESS_PART_ENERGY_HALF_HOUR);
                    BigDecimal processTime = queryEquipmentProcessTime(partCountSampler, start, end);
                    result.add(Arrays.asList(equipment.getEquipmentName(), processEnergy.multiply(new BigDecimal(100)).divide(energy, 2, RoundingMode.HALF_UP), processTime.multiply(new BigDecimal(100)).divide(new BigDecimal(end.getTime() - start.getTime()), 2, RoundingMode.HALF_UP)));
                }else{
                    result.add(Arrays.asList(equipment.getEquipmentName(), BigDecimal.ZERO, BigDecimal.ZERO));
                }
            }
        }
        return result;
    }

    @Override
    public List<List<Object>> queryEquipmentStatusEnergyReport(Long equipmentId, Date start, Date end){
        List<List<Object>> result = new ArrayList<>();
        result.add(Arrays.asList("时间","加工能耗","待机能耗"));
        EmsEquipment equipment = equipmentMapper.selectEmsEquipmentByEquipmentId(equipmentId);
        Map<String, BigDecimal> dayEnergyMap = new HashMap<>();
        Map<String, BigDecimal> dayProcessEnergyMap = new HashMap<>();
        if (null != equipment) {
            String partCountSampler = getEquipmentSampler(equipment, EmsConstants.PART_COUNT);
            String energySampler = getEquipmentSampler(equipment, EmsConstants.ENERGY);
            if (null != energySampler){
                dayEnergyMap = getSamplerDayEnergyData(energySampler, start, end);
            }
            if (null != partCountSampler){
                dayProcessEnergyMap = getSamplerDayProcessEnergyData(partCountSampler, start, end);
            }
        }

        for(Date date = start; date.before(end); date = DateUtils.addDays(date, 1)){
            String day = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, date);
            BigDecimal energy = dayEnergyMap.getOrDefault(day, BigDecimal.ZERO);
            BigDecimal processEnergy = dayProcessEnergyMap.getOrDefault(day, BigDecimal.ZERO);
            BigDecimal idleEnergy = energy.subtract(processEnergy);
            result.add(Arrays.asList(day, processEnergy.setScale(2, RoundingMode.HALF_UP), idleEnergy.setScale(2, RoundingMode.HALF_UP)));
        }
        return result;
    }

    private Map<String, BigDecimal> getSamplerDayEnergyData(String sampler, Date start, Date end){
        Map<String, BigDecimal> result = new HashMap<>();
        String queryStart = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(start);
        String queryEnd = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(end);
        String influxQuery = "import \"timezone\"\n" +
                "option location = timezone.fixed(offset: 8h)\n"+
                "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                "  |> range(start: " + queryStart + ", stop: " + queryEnd + ")\n" +
                "  |> filter(fn: (r) => r[\"_measurement\"] == \""+EmsConstants.INFLUX_POSITIVE_ENERGY_HALF_HOUR+"\")\n" +
                "  |> filter(fn: (r) => r[\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"]==\"" + sampler + "\")" +
                "  |> aggregateWindow(every: 1d, fn: sum, timeSrc:\"_start\")";
        List<FluxTable> tables = influxDBClient.getQueryApi().query(influxQuery);
        if (tables.size() > 0){
            for (FluxRecord record : tables.get(0).getRecords()){
                BigDecimal energy = BigDecimal.ZERO;
                if (null != record.getValue()){
                    energy = new BigDecimal(record.getValue().toString());
                }
                String day = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, Date.from(record.getTime()));
                result.put(day, energy);
            }
        }
        return result;
    }

    private Map<String, BigDecimal> getSamplerDayProcessEnergyData(String sampler, Date start, Date end){
        Map<String, BigDecimal> result = new HashMap<>();
        String queryStart = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(start);
        String queryEnd = DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.format(end);
        String influxQuery = "import \"timezone\"\n" +
                "option location = timezone.fixed(offset: 8h)\n"+
                "from(bucket: \""+ influxDBProperty.getReportBucket() +"\")\n" +
                "  |> range(start: " + queryStart + ", stop: " + queryEnd + ")\n" +
                "  |> filter(fn: (r) => r[\"_measurement\"] == \""+EmsConstants.INFLUX_PROCESS_PART_ENERGY_HALF_HOUR+"\")\n" +
                "  |> filter(fn: (r) => r[\"_field\"] == \"value\")"+
                "  |> filter(fn: (r) => r[\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"]==\"" + sampler + "\")" +
                "  |> keep(columns:[\"_time\",\"_value\",\""+EmsConstants.INFLUX_SAMPLER_TAG+"\"])" +
                "  |> aggregateWindow(every: 1d, fn: sum, timeSrc:\"_start\")";
        List<FluxTable> tables = influxDBClient.getQueryApi().query(influxQuery);
        if (tables.size() > 0){
            for (FluxRecord record : tables.get(0).getRecords()){
                BigDecimal energy = BigDecimal.ZERO;
                if (null != record.getValue()){
                    energy = new BigDecimal(record.getValue().toString());
                }
                String day = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, Date.from(record.getTime()));
                result.put(day, energy);
            }
        }
        return result;
    }

}
