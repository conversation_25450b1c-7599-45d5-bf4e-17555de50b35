package com.ems.energy.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 脚本查询配置属性
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Component
@ConfigurationProperties(prefix = "ems.script.query")
public class ScriptQueryProperties {

    /** 查询超时时间（秒） */
    private int queryTimeout = 30;

    /** 最大结果数量 */
    private int maxResultSize = 10000;

    /** 是否启用脚本缓存 */
    private boolean enableCache = true;

    /** 缓存过期时间（分钟） */
    private int cacheExpireMinutes = 10;

    /** 是否启用权限验证 */
    private boolean enablePermissionCheck = true;

    /** 允许的最大参数数量 */
    private int maxParameterCount = 50;

    public int getQueryTimeout() {
        return queryTimeout;
    }

    public void setQueryTimeout(int queryTimeout) {
        this.queryTimeout = queryTimeout;
    }

    public int getMaxResultSize() {
        return maxResultSize;
    }

    public void setMaxResultSize(int maxResultSize) {
        this.maxResultSize = maxResultSize;
    }

    public boolean isEnableCache() {
        return enableCache;
    }

    public void setEnableCache(boolean enableCache) {
        this.enableCache = enableCache;
    }

    public int getCacheExpireMinutes() {
        return cacheExpireMinutes;
    }

    public void setCacheExpireMinutes(int cacheExpireMinutes) {
        this.cacheExpireMinutes = cacheExpireMinutes;
    }

    public boolean isEnablePermissionCheck() {
        return enablePermissionCheck;
    }

    public void setEnablePermissionCheck(boolean enablePermissionCheck) {
        this.enablePermissionCheck = enablePermissionCheck;
    }

    public int getMaxParameterCount() {
        return maxParameterCount;
    }

    public void setMaxParameterCount(int maxParameterCount) {
        this.maxParameterCount = maxParameterCount;
    }
}
