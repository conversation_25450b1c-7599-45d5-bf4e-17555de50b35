package com.ems.energy.config;

import com.ems.energy.domain.vo.InfluxDBPropertyVo;
import com.influxdb.LogLevel;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.InfluxDBClientOptions;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class InfluxDBConfig {
    @Value("${spring.influx.token:''}")
    private String token;
    @Value("${spring.influx.url:''}")
    private String url;

    @Value("${spring.influx.org:''}")
    private String org;

    @Value("${spring.influx.bucket:''}")
    private String bucket;

    private String getReportBucket(){
        return bucket + "_Report";
    }

    @Bean
    public InfluxDBClient influxDBClient() {

        System.out.println("=============="+token+"==============");
        System.out.println("=============="+url+"==============");
        OkHttpClient.Builder httpClient = new OkHttpClient.Builder().readTimeout(100, TimeUnit.SECONDS).writeTimeout(100,TimeUnit.SECONDS);
        InfluxDBClientOptions options = InfluxDBClientOptions.builder().url(url).authenticateToken(token.toCharArray()).org(org).okHttpClient(httpClient).build();
        InfluxDBClient influxDBClient = InfluxDBClientFactory.create(options);
        influxDBClient.setLogLevel(LogLevel.BASIC);
        return influxDBClient;

    }

    @Bean
    public InfluxDBPropertyVo influxDBProperty(){
        InfluxDBPropertyVo influxDBPropertyVo = new InfluxDBPropertyVo();
        influxDBPropertyVo.setOrg(org);
        influxDBPropertyVo.setBucket(bucket);
        influxDBPropertyVo.setReportBucket(getReportBucket());
        return influxDBPropertyVo;
    }


}
