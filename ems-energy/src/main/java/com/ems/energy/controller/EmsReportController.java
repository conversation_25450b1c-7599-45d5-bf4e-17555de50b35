package com.ems.energy.controller;

import com.alibaba.fastjson.JSON;
import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.constant.EnergyConstant;
import com.ems.energy.domain.EmsReport;
import com.ems.energy.domain.ReportData;
import com.ems.energy.service.IEmsReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 能耗报Controller
 *
 * <AUTHOR>
 * @date 2021-12-21
 */
@Api("能耗报")
@RestController
@RequestMapping("/system/report")
public class EmsReportController extends BaseController
{
    @Autowired
    private IEmsReportService emsReportService;
//    @Autowired
//    private KafkaTemplate kafkaTemplate;
    /**
     * 查询能耗报列表
     */
    @ApiOperation("查询能耗报列表")
    @PreAuthorize("@ss.hasPermi('system:report:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsReport emsReport)
    {
        startPage();
        List<EmsReport> list = emsReportService.selectEmsReportList(emsReport);
        return getDataTable(list);
    }

    /**
     * 导出能耗报列表
     */
    @ApiOperation("导出能耗报列表")
    @PreAuthorize("@ss.hasPermi('system:report:export')")
    @Log(title = "能耗报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsReport emsReport)
    {
        List<EmsReport> list = emsReportService.selectEmsReportList(emsReport);
        ExcelUtil<EmsReport> util = new ExcelUtil<EmsReport>(EmsReport.class);
        return util.exportExcel(list, "能耗报数据");
    }

    /**
     * 获取能耗报详细信息
     */
    @ApiOperation("获取能耗报详细信息")
    @PreAuthorize("@ss.hasPermi('system:report:query')")
    @GetMapping(value = "/{reportId}")
    public AjaxResult getInfo(@PathVariable("reportId") Long reportId)
    {
        return AjaxResult.success(emsReportService.selectEmsReportByReportId(reportId));
    }

    /**
     * 新增能耗报
     */
    @ApiOperation("新增能耗报")
    @PreAuthorize("@ss.hasPermi('system:report:add')")
    @Log(title = "能耗报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsReport emsReport)
    {
        return toAjax(emsReportService.insertEmsReport(emsReport));
    }

    /**
     * 修改能耗报
     */
    @ApiOperation("修改能耗报")
    @PreAuthorize("@ss.hasPermi('system:report:edit')")
    @Log(title = "能耗报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsReport emsReport)
    {
        return toAjax(emsReportService.updateEmsReport(emsReport));
    }

    /**
     * 删除能耗报
     */
    @ApiOperation("删除能耗报")
    @PreAuthorize("@ss.hasPermi('system:report:remove')")
    @Log(title = "能耗报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{reportIds}")
    public AjaxResult remove(@PathVariable Long[] reportIds)
    {
        return toAjax(emsReportService.deleteEmsReportByReportIds(reportIds));
    }

    /**
     * 发送kafka消息
     * @param data
     * @return
     */
    @GetMapping("/toKafka")
    public AjaxResult toKafkaSend(@RequestBody ReportData data){
//        kafkaTemplate.send(EnergyConstant.KAFKA_TOPIC_NAME, JSON.toJSONString(data));
        return AjaxResult.success(data);
    }
}
