package com.ems.energy.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.enums.BusinessType;
import com.ems.energy.domain.EmsMeasuringTool;
import com.ems.energy.service.IEmsMeasuringToolService;
import com.ems.common.utils.poi.ExcelUtil;

/**
 * 表具Controller
 *
 * <AUTHOR>
 * @date 2022-03-07
 */
@Api("表具")
@RestController
@RequestMapping("/energy/measuringTool")
public class EmsMeasuringToolController extends BaseController
{
    @Autowired
    private IEmsMeasuringToolService emsMeasuringToolService;

    /**
     * 查询表具列表
     */
    @ApiOperation("查询表具列表")
    @PreAuthorize("@ss.hasPermi('energy:measuringTool:list')")
    @GetMapping("/list")
    public AjaxResult list(EmsMeasuringTool emsMeasuringTool)
    {
        List<EmsMeasuringTool> list = emsMeasuringToolService.selectEmsMeasuringToolList(emsMeasuringTool);
        return AjaxResult.success(list);
    }

    /**
     * 查询实体表具列表
     */
    @ApiOperation("查询真实表具列表")
    @PreAuthorize("@ss.hasPermi('energy:measuringTool:list')")
    @GetMapping("/listReal")
    public AjaxResult listReal(EmsMeasuringTool emsMeasuringTool)
    {
        List<EmsMeasuringTool> list = emsMeasuringToolService.selectRealEmsMeasuringToolList(emsMeasuringTool);
        return AjaxResult.success(list);
    }

    /**
     * 导出表具列表
     */
    @ApiOperation("导出表具列表")
    @PreAuthorize("@ss.hasPermi('energy:measuringTool:export')")
    @Log(title = "表具", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsMeasuringTool emsMeasuringTool)
    {
        List<EmsMeasuringTool> list = emsMeasuringToolService.selectEmsMeasuringToolList(emsMeasuringTool);
        ExcelUtil<EmsMeasuringTool> util = new ExcelUtil<>(EmsMeasuringTool.class);
        return util.exportExcel(list, "表具数据");
    }

    /**
     * 获取表具详细信息
     */
    @ApiOperation("获取表具详细信息")
    @PreAuthorize("@ss.hasPermi('energy:measuringTool:query')")
    @GetMapping(value = "/{measuringToolId}")
    public AjaxResult getInfo(@PathVariable("measuringToolId") Long measuringToolId)
    {
        return AjaxResult.success(emsMeasuringToolService.selectEmsMeasuringToolByMeasuringToolId(measuringToolId));
    }

    /**
     * 新增表具
     */
    @ApiOperation("新增表具")
    @PreAuthorize("@ss.hasPermi('energy:measuringTool:add')")
    @Log(title = "表具", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsMeasuringTool emsMeasuringTool)
    {
        return toAjax(emsMeasuringToolService.insertEmsMeasuringTool(emsMeasuringTool));
    }

    /**
     * 修改表具
     */
    @ApiOperation("修改表具")
    @PreAuthorize("@ss.hasPermi('energy:measuringTool:edit')")
    @Log(title = "表具", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsMeasuringTool emsMeasuringTool)
    {
        return toAjax(emsMeasuringToolService.updateEmsMeasuringTool(emsMeasuringTool));
    }

    /**
     * 删除表具
     */
    @ApiOperation("删除表具")
    @PreAuthorize("@ss.hasPermi('energy:measuringTool:remove')")
    @Log(title = "表具", businessType = BusinessType.DELETE)
	@DeleteMapping("/{measuringToolIds}")
    public AjaxResult remove(@PathVariable Long[] measuringToolIds)
    {
        return toAjax(emsMeasuringToolService.deleteEmsMeasuringToolByMeasuringToolIds(measuringToolIds));
    }
}
