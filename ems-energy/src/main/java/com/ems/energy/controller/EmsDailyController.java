package com.ems.energy.controller;

import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.EmsDaily;
import com.ems.energy.domain.EmsDailyDTO;
import com.ems.energy.domain.EmsReport;
import com.ems.energy.service.IEmsDailyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.List;

/**
 * 日报Controller
 *
 * <AUTHOR>
 * @date 2021-12-28
 */
@Api("日报")
@RestController
@RequestMapping("/system/daily")
public class EmsDailyController extends BaseController
{
    @Autowired
    private IEmsDailyService emsDailyService;

    /**
     * 查询日报列表
     */
    @ApiOperation("查询日报列表")
    @PreAuthorize("@ss.hasPermi('system:daily:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsDaily emsDaily)
    {
        startPage();
        List<EmsDaily> list = emsDailyService.selectEmsDailyList(emsDaily);
        return getDataTable(list);
    }

    @ApiOperation("查询列表数据")
    @PreAuthorize("@ss.hasPermi('system:daily:list')")
    @GetMapping("/listOne")
    public TableDataInfo listOne(EmsDailyDTO emsDaily)
    {
        startPage();
        List<EmsDailyDTO> list = emsDailyService.selectlistOne(emsDaily);
        return getDataTable(list);
    }

    @ApiOperation("查询报表信息")
    @PreAuthorize("@ss.hasPermi('system:daily:list')")
    @GetMapping("/listTo")
    public AjaxResult listTo(EmsDaily emsDaily, EmsReport emsReport)
    {
        int res = emsDailyService.selectEmsDailyListTo(emsDaily, emsReport);
        if (res == 0){
            return AjaxResult.error();
        }
        return AjaxResult.success();
    }

    @ApiOperation("查询日报表")
    @PreAuthorize("@ss.hasPermi('system:daily:list')")
    @GetMapping("/listDaily")
    public AjaxResult listDaily(EmsDailyDTO emsDailyDTO)
    {
        return emsDailyService.selectDaily(emsDailyDTO);
    }
    /**
     * 导出日报列表
     */
    @ApiOperation("导出日报列表")
    @PreAuthorize("@ss.hasPermi('system:daily:export')")
    @Log(title = "日报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsDaily emsDaily)
    {
        List<EmsDaily> list = emsDailyService.selectEmsDailyList(emsDaily);
        ExcelUtil<EmsDaily> util = new ExcelUtil<EmsDaily>(EmsDaily.class);
        return util.exportExcel(list, "日报数据");
    }

    /**
     * 获取日报详细信息
     */
    @ApiOperation("获取日报详细信息")
    @PreAuthorize("@ss.hasPermi('system:daily:query')")
    @GetMapping(value = "/{dailyId}")
    public AjaxResult getInfo(@PathVariable("dailyId") Long dailyId)
    {
        return AjaxResult.success(emsDailyService.selectEmsDailyByDailyId(dailyId));
    }

    /**
     * 新增日报
     */
    @ApiOperation("新增日报")
    @PreAuthorize("@ss.hasPermi('system:daily:add')")
    @Log(title = "日报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsDaily emsDaily)
    {
        return toAjax(emsDailyService.insertEmsDaily(emsDaily));
    }

    /**
     * 修改日报
     */
    @ApiOperation("修改日报")
    @PreAuthorize("@ss.hasPermi('system:daily:edit')")
    @Log(title = "日报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsDaily emsDaily)
    {
        return toAjax(emsDailyService.updateEmsDaily(emsDaily));
    }

    /**
     * 删除日报
     */
    @ApiOperation("删除日报")
    @PreAuthorize("@ss.hasPermi('system:daily:remove')")
    @Log(title = "日报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dailyIds}")
    public AjaxResult remove(@PathVariable Long[] dailyIds)
    {
        return toAjax(emsDailyService.deleteEmsDailyByDailyIds(dailyIds));
    }

    @ApiOperation("查询峰平谷用电")
    @PostMapping("/listPeak/{time}")
    public AjaxResult listPeak(@PathVariable("time")String time,EmsDaily emsDaily)
    {
        try {
            return emsDailyService.selectlistPeak(emsDaily,time);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return AjaxResult.error();
    }
    @ApiOperation("查询峰平谷用电饼图")
    @PostMapping("/listChart/{time}")
    public AjaxResult listChart(@PathVariable("time")String time,EmsDaily emsDaily) throws ParseException {
        return emsDailyService.selectlistChart(emsDaily,time);
    }
}
