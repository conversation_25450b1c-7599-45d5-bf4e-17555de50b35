package com.ems.energy.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.enums.BusinessType;
import com.ems.energy.domain.EmsCarbonFactor;
import com.ems.energy.service.IEmsCarbonFactorService;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.common.core.page.TableDataInfo;

/**
 * 碳排放因子设置Controller
 *
 * <AUTHOR>
 * @date 2022-06-02
 */
@Api("碳排放因子设置")
@RestController
@RequestMapping("/energy/carbonFactor")
public class EmsCarbonFactorController extends BaseController
{
    @Autowired
    private IEmsCarbonFactorService emsCarbonFactorService;

    /**
     * 查询碳排放因子设置列表
     */
    @ApiOperation("查询碳排放因子设置列表")
    @PreAuthorize("@ss.hasPermi('energy:carbonFactor:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsCarbonFactor emsCarbonFactor)
    {
        startPage();
        List<EmsCarbonFactor> list = emsCarbonFactorService.selectEmsCarbonFactorList(emsCarbonFactor);
        return getDataTable(list);
    }

    /**
     * 导出碳排放因子设置列表
     */
    @ApiOperation("导出碳排放因子设置列表")
    @PreAuthorize("@ss.hasPermi('energy:carbonFactor:export')")
    @Log(title = "碳排放因子设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsCarbonFactor emsCarbonFactor)
    {
        List<EmsCarbonFactor> list = emsCarbonFactorService.selectEmsCarbonFactorList(emsCarbonFactor);
        ExcelUtil<EmsCarbonFactor> util = new ExcelUtil<>(EmsCarbonFactor.class);
        return util.exportExcel(list, "碳排放因子设置数据");
    }

    /**
     * 获取碳排放因子设置详细信息
     */
    @ApiOperation("获取碳排放因子设置详细信息")
    @PreAuthorize("@ss.hasPermi('energy:carbonFactor:query')")
    @GetMapping(value = "/{factorId}")
    public AjaxResult getInfo(@PathVariable("factorId") Long factorId)
    {
        return AjaxResult.success(emsCarbonFactorService.selectEmsCarbonFactorByFactorId(factorId));
    }

    /**
     * 新增碳排放因子设置
     */
    @ApiOperation("新增碳排放因子设置")
    @PreAuthorize("@ss.hasPermi('energy:carbonFactor:add')")
    @Log(title = "碳排放因子设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsCarbonFactor emsCarbonFactor)
    {
        return toAjax(emsCarbonFactorService.insertEmsCarbonFactor(emsCarbonFactor));
    }

    /**
     * 修改碳排放因子设置
     */
    @ApiOperation("修改碳排放因子设置")
    @PreAuthorize("@ss.hasPermi('energy:carbonFactor:edit')")
    @Log(title = "碳排放因子设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsCarbonFactor emsCarbonFactor)
    {
        return toAjax(emsCarbonFactorService.updateEmsCarbonFactor(emsCarbonFactor));
    }

    /**
     * 删除碳排放因子设置
     */
    @ApiOperation("删除碳排放因子设置")
    @PreAuthorize("@ss.hasPermi('energy:carbonFactor:remove')")
    @Log(title = "碳排放因子设置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{factorIds}")
    public AjaxResult remove(@PathVariable Long[] factorIds)
    {
        return toAjax(emsCarbonFactorService.deleteEmsCarbonFactorByFactorIds(factorIds));
    }
}
