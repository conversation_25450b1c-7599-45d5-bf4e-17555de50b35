package com.ems.energy.controller;

import com.ems.common.annotation.Log;
import com.ems.common.constant.ErrorConstant;
import com.ems.common.constant.UserConstants;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.EmsMeasure;
import com.ems.energy.domain.EmsMeasureListDTO;
import com.ems.energy.domain.tree.Emstreecenter;
import com.ems.energy.domain.vo.EmsMeasureVo;
import com.ems.energy.service.IEmsMeasureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 计量器具Controller
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Api("计量器具管理")
@RestController
@RequestMapping("/energy/measure")
public class EmsMeasureController extends BaseController
{
    @Autowired
    private IEmsMeasureService emsMeasureService;

    /**
     * 查询计量器具列表
     */
    @ApiOperation("查询计量器具列表")
    @PreAuthorize("@ss.hasPermi('system:measure:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsMeasureVo emsMeasure)
    {
        startPage();
        List<EmsMeasureVo> list = emsMeasureService.selectEmsMeasureListPage(emsMeasure);
        return getDataTable(list);
    }

    @ApiOperation("计量器具列表页面查询")
    @GetMapping("/pageList")
    @PreAuthorize("@ss.hasPermi('enterprise:relation:list')")
    public TableDataInfo pageList(EmsMeasureVo emsMeasure)
    {
        startPage();
        List<EmsMeasureVo> list = emsMeasureService.selectEmsMeasureListPage(emsMeasure);
        return getDataTable(list);
    }

    /**
     * 导出计量器具列表
     */
    @ApiOperation("导出计量器具列表")
    @PreAuthorize("@ss.hasPermi('system:measure:export')")
    @Log(title = "计量器具", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsMeasure emsMeasure)
    {
        List<EmsMeasure> list = emsMeasureService.selectEmsMeasureList(emsMeasure);
        ExcelUtil<EmsMeasure> util = new ExcelUtil<>(EmsMeasure.class);
        return util.exportExcel(list, "计量器具数据");
    }

    /**
     * 获取计量器具详细信息
     */
    @ApiOperation("获取计量器具详细信息")
    @PreAuthorize("@ss.hasPermi('system:measure:query')")
    @GetMapping(value = "/{meaId}")
    public AjaxResult getInfo(@PathVariable("meaId") Long meaId)
    {
        return AjaxResult.success(emsMeasureService.selectEmsMeasureByMeaId(meaId));
    }


    /**
     * 新增计量器具
     */
    @ApiOperation("新增计量器具")
    @PreAuthorize("@ss.hasPermi('system:measure:add')")
    @Log(title = "计量器具", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody EmsMeasure emsMeasure)
    {
        if (UserConstants.NOT_UNIQUE.equals(emsMeasureService.checkMeasureNameUnique(emsMeasure)))
        {
            return AjaxResult.error("新增计量器具'" + emsMeasure.getMeaName() + "'失败，计量器具名称已存在");
        }
        return toAjax(emsMeasureService.insertEmsMeasure(emsMeasure));
    }

    /**
     * 修改计量器具
     */
    @ApiOperation("修改计量器具")
    @PreAuthorize("@ss.hasPermi('system:measure:edit')")
    @Log(title = "计量器具", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody EmsMeasure emsMeasure)
    {
        return toAjax(emsMeasureService.updateEmsMeasure(emsMeasure));
    }

    /**
     * 删除计量器具
     */
    @ApiOperation("删除计量器具")
    @PreAuthorize("@ss.hasPermi('system:measure:remove')")
    @Log(title = "计量器具", businessType = BusinessType.DELETE)
	@DeleteMapping("/{meaId}")
    public AjaxResult remove(@PathVariable Long meaId)
    {
        if (emsMeasureService.selectByMeaid(meaId)){
            return AjaxResult.error(ErrorConstant.LOW_LEVEL_DEPT_IS_EXIST);
        }
        return toAjax(emsMeasureService.deleteEmsMeasureByMeaId(meaId));
    }

    /**
     * 获取计量器具下拉树列表
     */
    @ApiOperation("计量器具列表树形结构")
    @GetMapping("/treeselect")
    public AjaxResult treeselect(EmsMeasure emsMeasure)
    {
        List<EmsMeasure> emsMeasures = emsMeasureService.selectEmsMeasureList(emsMeasure);
        List<Emstreecenter> res = emsMeasureService.buildEmsMeasureTreeSelect(emsMeasures);
        return AjaxResult.success(res);
    }

    @ApiOperation("计量器具列表查询")
    @GetMapping("/measureList")
    public AjaxResult measureList( EmsMeasureListDTO listDTO){
        return AjaxResult.success(emsMeasureService.selectEmsMeasureListDTO(listDTO));
    }

    /**
     * 计量器具列表页面导出
     * @param listDTO
     */
    @ApiOperation("计量器具列表页面导出")
    @GetMapping("/export")
    public AjaxResult measureListExport(EmsMeasureListDTO listDTO){
        List<EmsMeasureListDTO> list = emsMeasureService.selectEmsMeasureListDTO(listDTO);
        ExcelUtil<EmsMeasureListDTO> util = new ExcelUtil<>(EmsMeasureListDTO.class);
        return util.exportExcel(list,"计量器具列表数据");
    }

    @ApiOperation("根据成本中心ID查询计量器具信息")
    @PreAuthorize("@ss.hasPermi('system:measure:query')")
    @GetMapping(value = "/costCent/{costCenterId}")
    public AjaxResult getInfoTo(@PathVariable("costCenterId") Long costCenterId)
    {
        return AjaxResult.success(emsMeasureService.selectEmsMeasureBycostCent(costCenterId));
    }
}
