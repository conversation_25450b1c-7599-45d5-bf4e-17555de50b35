package com.ems.energy.controller;

import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.EmsEnergy;
import com.ems.energy.service.IEmsEnergyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 能源介质Controller
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Api("能源介质管理")
@RestController
@RequestMapping("/energy/energy")
public class EmsEnergyController extends BaseController
{
    @Autowired
    private IEmsEnergyService emsEnergyService;


    /**
     * 查询所有能源介质列表
     * @return
     */
    @GetMapping("/queryAllEnergy")
    @PreAuthorize("@ss.hasPermi('enterprise:management:list')")
    @ApiOperation("查询所有能源介质列表")
    public AjaxResult queryAllEnergy(){
        List<EmsEnergy> energyList = emsEnergyService.selectAllEnergy();
        return AjaxResult.success(energyList);
    }
    /**
     * 导出能源介质列表
     */
    @ApiOperation("导出能源介质列表")
    @PreAuthorize("@ss.hasPermi('system:energy:export')")
    @Log(title = "能源介质", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsEnergy emsEnergy)
    {
        List<EmsEnergy> list = emsEnergyService.selectEmsEnergyList(emsEnergy);
        ExcelUtil<EmsEnergy> util = new ExcelUtil<>(EmsEnergy.class);
        return util.exportExcel(list, "能源介质数据");
    }

    /**
     * 获取能源介质详细信息
     */
    @ApiOperation("获取能源介质详细信息")
    @PreAuthorize("@ss.hasPermi('system:energy:query')")
    @GetMapping(value = "/{energyId}")
    public AjaxResult getInfo(@PathVariable("energyId") Long energyId)
    {
        return AjaxResult.success(emsEnergyService.selectEmsEnergyByEnergyId(energyId));
    }

    /**
     * 新增能源介质
     */
    @ApiOperation("新增能源介质")
    @PreAuthorize("@ss.hasPermi('system:energy:add')")
    @Log(title = "能源介质", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody EmsEnergy emsEnergy)
    {
        return toAjax(emsEnergyService.insertEmsEnergy(emsEnergy));
    }

    /**
     * 修改能源介质
     */
    @ApiOperation("修改能源介质")
    @PreAuthorize("@ss.hasPermi('system:energy:edit')")
    @Log(title = "能源介质", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody EmsEnergy emsEnergy)
    {
        return toAjax(emsEnergyService.updateEmsEnergy(emsEnergy));
    }

    /**
     * 删除能源介质
     */
    @ApiOperation("删除能源介质")
    @PreAuthorize("@ss.hasPermi('system:energy:remove')")
    @Log(title = "能源介质", businessType = BusinessType.DELETE)
	@DeleteMapping("/{energyIds}")
    public AjaxResult remove(@PathVariable Long[] energyIds)
    {
        return toAjax(emsEnergyService.deleteEmsEnergyByEnergyIds(energyIds));
    }
}
