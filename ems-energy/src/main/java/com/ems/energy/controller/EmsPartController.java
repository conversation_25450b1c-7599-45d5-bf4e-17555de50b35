package com.ems.energy.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.enums.BusinessType;
import com.ems.energy.domain.EmsPart;
import com.ems.energy.service.IEmsPartService;
import com.ems.common.utils.poi.ExcelUtil;

/**
 * 产品管理Controller
 *
 * <AUTHOR>
 * @date 2022-03-07
 */
@Api("产品管理")
@RestController
@RequestMapping("/energy/part")
public class EmsPartController extends BaseController
{
    @Autowired
    private IEmsPartService emsPartService;

    /**
     * 查询产品管理列表
     */
    @ApiOperation("查询产品管理列表")
    @PreAuthorize("@ss.hasPermi('energy:part:list')")
    @GetMapping("/list")
    public AjaxResult list(EmsPart emsPart)
    {
        List<EmsPart> list = emsPartService.selectEmsPartList(emsPart);
        return AjaxResult.success(list);
    }

    /**
     * 导出产品管理列表
     */
    @ApiOperation("导出产品管理列表")
    @PreAuthorize("@ss.hasPermi('energy:part:export')")
    @Log(title = "产品管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsPart emsPart)
    {
        List<EmsPart> list = emsPartService.selectEmsPartList(emsPart);
        ExcelUtil<EmsPart> util = new ExcelUtil<>(EmsPart.class);
        return util.exportExcel(list, "产品管理数据");
    }

    /**
     * 获取产品管理详细信息
     */
    @ApiOperation("获取产品管理详细信息")
    @PreAuthorize("@ss.hasPermi('energy:part:query')")
    @GetMapping(value = "/{partId}")
    public AjaxResult getInfo(@PathVariable("partId") Long partId)
    {
        return AjaxResult.success(emsPartService.selectEmsPartByPartId(partId));
    }

    /**
     * 新增产品管理
     */
    @ApiOperation("新增产品管理")
    @PreAuthorize("@ss.hasPermi('energy:part:add')")
    @Log(title = "产品管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsPart emsPart)
    {
        return toAjax(emsPartService.insertEmsPart(emsPart));
    }

    /**
     * 修改产品管理
     */
    @ApiOperation("修改产品管理")
    @PreAuthorize("@ss.hasPermi('energy:part:edit')")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsPart emsPart)
    {
        return toAjax(emsPartService.updateEmsPart(emsPart));
    }

    /**
     * 删除产品管理
     */
    @ApiOperation("删除产品管理")
    @PreAuthorize("@ss.hasPermi('energy:part:remove')")
    @Log(title = "产品管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{partIds}")
    public AjaxResult remove(@PathVariable Long[] partIds)
    {
        return toAjax(emsPartService.deleteEmsPartByPartIds(partIds));
    }
}
