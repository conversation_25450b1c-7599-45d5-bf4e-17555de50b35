package com.ems.energy.controller;

import com.ems.common.annotation.Log;
import com.ems.common.constant.UserConstants;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.EmsCostCenter;
import com.ems.energy.domain.tree.Emstreecenter;
import com.ems.energy.domain.vo.EmsCostCenterVo;
import com.ems.energy.service.IEmsCostCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 成本中心Controller
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Api("成本中心管理")
@RestController
@RequestMapping("/energy/center")
public class EmsCostCenterController extends BaseController {
    @Autowired
    private IEmsCostCenterService emsCostCenterService;

    /**
     * 查询成本中心列表(@Deprecated) 弃用
     */
    @ApiOperation("查询成本中心列表")
    @PreAuthorize("@ss.hasPermi('system:center:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsCostCenter emsCostCenter) {
        startPage();
        List<EmsCostCenter> list = emsCostCenterService.selectEmsCostCenterList(emsCostCenter);
        return getDataTable(list);
    }

    /**
     * 成本中心页面列表
     * @param emsCostCenter
     * @return
     */
    @PreAuthorize("@ss.hasPermi('enterprise:cost:list')")
    @GetMapping("/pagelist")
    public AjaxResult pagelist(EmsCostCenter emsCostCenter) {
        startPage();
        List<EmsCostCenterVo> emsCostCenters = emsCostCenterService.pagelist(emsCostCenter);
        return AjaxResult.success(emsCostCenterService.buildcenterVoselect(emsCostCenters));
    }

    /**
     * 导出成本中心列表
     */
    @ApiOperation("导出成本中心列表")
    @PreAuthorize("@ss.hasPermi('system:center:export')")
    @Log(title = "成本中心", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsCostCenter emsCostCenter) {
        List<EmsCostCenter> list = emsCostCenterService.selectEmsCostCenterList(emsCostCenter);
        ExcelUtil<EmsCostCenter> util = new ExcelUtil<>(EmsCostCenter.class);
        return util.exportExcel(list, "成本中心数据");
    }

    /**
     * 获取成本中心详细信息
     */
    @ApiOperation("获取成本中心详细信息")
    @PreAuthorize("@ss.hasPermi('system:center:query')")
    @GetMapping(value = "/{centerId}")
    public AjaxResult getInfo(@PathVariable("centerId") Long centerId) {
        return AjaxResult.success(emsCostCenterService.selectEmsCostCenterByCenterId(centerId));
    }

    /**
     * 新增成本中心
     */
    @ApiOperation("新增成本中心")
    @PreAuthorize("@ss.hasPermi('system:center:add')")
    @Log(title = "成本中心", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody EmsCostCenter emsCostCenter) {
        if (UserConstants.NOT_UNIQUE.equals(emsCostCenterService.checkCenterNameUnique(emsCostCenter)))
        {
            return AjaxResult.error("新增能源成本中心'" + emsCostCenter.getCenterName() + "'失败，能源成本中心名称已存在");
        }
        return toAjax(emsCostCenterService.insertEmsCostCenter(emsCostCenter));
    }

    /**
     * 修改成本中心
     */
    @ApiOperation("修改成本中心")
    @PreAuthorize("@ss.hasPermi('system:center:edit')")
    @Log(title = "成本中心", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody EmsCostCenter emsCostCenter) {
        return toAjax(emsCostCenterService.updateEmsCostCenter(emsCostCenter));
    }

    /**
     * 删除成本中心
     */
    @ApiOperation("删除成本中心")
    @PreAuthorize("@ss.hasPermi('system:center:remove')")
    @Log(title = "成本中心", businessType = BusinessType.DELETE)
    @DeleteMapping("/{centerIds}")
    public AjaxResult remove(@PathVariable Long[] centerIds) {
        return toAjax(emsCostCenterService.deleteEmsCostCenterByCenterIds(centerIds));
    }

    /**
     * 成本中心树
     */
    @PreAuthorize("@ss.hasPermi('system:center:query')")
    @GetMapping("/treeselect")
    public AjaxResult treeselect(EmsCostCenter emsCostCenter) {
        List<EmsCostCenter> emsCostCenters = emsCostCenterService.selectEmsCostCenterList(emsCostCenter);
        List<Emstreecenter> list = emsCostCenterService.buildcenterselect(emsCostCenters);
        return AjaxResult.success(list);
    }

    /**
     * 成本中心的维护(添加)
     */
    @ApiOperation("新增成本中心")
    @PreAuthorize("@ss.hasPermi('system:center:add')")
    @Log(title = "成本中心", businessType = BusinessType.INSERT)
    @PostMapping("/toadd")
    public AjaxResult toadd(@Validated @RequestBody EmsCostCenter emsCostCenter) {
        if (UserConstants.NOT_UNIQUE.equals(emsCostCenterService.checkCenterNameUnique(emsCostCenter))) {
            return AjaxResult.error("新增部门'" + emsCostCenter.getCenterName() + "'失败，部门名称已存在");
        }
        return toAjax(emsCostCenterService.insertEmsCostCenter(emsCostCenter));
    }
}
