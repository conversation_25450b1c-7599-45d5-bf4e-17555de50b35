package com.ems.energy.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.enums.BusinessType;
import com.ems.energy.domain.EmsMesuringTool;
import com.ems.energy.service.IEmsMesuringToolService;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.common.core.page.TableDataInfo;

/**
 * 表具管理Controller
 *
 * <AUTHOR>
 * @date 2022-03-06
 */
@Api("表具管理")
@RestController
@RequestMapping("/enterprise/measuringTool")
public class    EmsMesuringToolController extends BaseController
{
    @Autowired
    private IEmsMesuringToolService emsMesuringToolService;

    /**
     * 查询表具管理列表
     */
    @ApiOperation("查询表具管理列表")
    @PreAuthorize("@ss.hasPermi('enterprise:measuringTool:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsMesuringTool emsMesuringTool)
    {
        startPage();
        List<EmsMesuringTool> list = emsMesuringToolService.selectEmsMesuringToolList(emsMesuringTool);
        return getDataTable(list);
    }

    /**
     * 导出表具管理列表
     */
    @ApiOperation("导出表具管理列表")
    @PreAuthorize("@ss.hasPermi('enterprise:measuringTool:export')")
    @Log(title = "表具管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsMesuringTool emsMesuringTool)
    {
        List<EmsMesuringTool> list = emsMesuringToolService.selectEmsMesuringToolList(emsMesuringTool);
        ExcelUtil<EmsMesuringTool> util = new ExcelUtil<>(EmsMesuringTool.class);
        return util.exportExcel(list, "表具管理数据");
    }

    /**
     * 获取表具管理详细信息
     */
    @ApiOperation("获取表具管理详细信息")
    @PreAuthorize("@ss.hasPermi('enterprise:measuringTool:query')")
    @GetMapping(value = "/{measuringToolId}")
    public AjaxResult getInfo(@PathVariable("measuringToolId") Long measuringToolId)
    {
        return AjaxResult.success(emsMesuringToolService.selectEmsMesuringToolByMeasuringToolId(measuringToolId));
    }

    /**
     * 新增表具管理
     */
    @ApiOperation("新增表具管理")
    @PreAuthorize("@ss.hasPermi('enterprise:measuringTool:add')")
    @Log(title = "表具管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsMesuringTool emsMesuringTool)
    {
        return toAjax(emsMesuringToolService.insertEmsMesuringTool(emsMesuringTool));
    }

    /**
     * 修改表具管理
     */
    @ApiOperation("修改表具管理")
    @PreAuthorize("@ss.hasPermi('enterprise:measuringTool:edit')")
    @Log(title = "表具管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsMesuringTool emsMesuringTool)
    {
        return toAjax(emsMesuringToolService.updateEmsMesuringTool(emsMesuringTool));
    }

    /**
     * 删除表具管理
     */
    @ApiOperation("删除表具管理")
    @PreAuthorize("@ss.hasPermi('enterprise:measuringTool:remove')")
    @Log(title = "表具管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{measuringToolIds}")
    public AjaxResult remove(@PathVariable Long[] measuringToolIds)
    {
        return toAjax(emsMesuringToolService.deleteEmsMesuringToolByMeasuringToolIds(measuringToolIds));
    }
}
