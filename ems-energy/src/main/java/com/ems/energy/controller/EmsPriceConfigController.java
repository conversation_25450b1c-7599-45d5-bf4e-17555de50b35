package com.ems.energy.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.enums.BusinessType;
import com.ems.energy.domain.EmsPriceConfig;
import com.ems.energy.service.IEmsPriceConfigService;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.common.core.page.TableDataInfo;

/**
 * 电价配置Controller
 *
 * <AUTHOR>
 * @date 2022-05-30
 */
@Api("电价配置")
@RestController
@RequestMapping("/energy/elecPriceConfig")
public class EmsPriceConfigController extends BaseController
{
    @Autowired
    private IEmsPriceConfigService emsPriceConfigService;

    /**
     * 查询电价配置列表
     */
    @ApiOperation("查询电价配置列表")
    @PreAuthorize("@ss.hasPermi('energy:elecPriceConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsPriceConfig emsPriceConfig)
    {
        startPage();
        List<EmsPriceConfig> list = emsPriceConfigService.selectEmsPriceConfigList(emsPriceConfig);
        return getDataTable(list);
    }

    /**
     * 导出电价配置列表
     */
    @ApiOperation("导出电价配置列表")
    @PreAuthorize("@ss.hasPermi('energy:elecPriceConfig:export')")
    @Log(title = "电价配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsPriceConfig emsPriceConfig)
    {
        List<EmsPriceConfig> list = emsPriceConfigService.selectEmsPriceConfigList(emsPriceConfig);
        ExcelUtil<EmsPriceConfig> util = new ExcelUtil<>(EmsPriceConfig.class);
        return util.exportExcel(list, "电价配置数据");
    }

    /**
     * 获取电价配置详细信息
     */
    @ApiOperation("获取电价配置详细信息")
    @PreAuthorize("@ss.hasPermi('energy:elecPriceConfig:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId)
    {
        return AjaxResult.success(emsPriceConfigService.selectEmsPriceConfigByConfigId(configId));
    }

    /**
     * 新增电价配置
     */
    @ApiOperation("新增电价配置")
    @PreAuthorize("@ss.hasPermi('energy:elecPriceConfig:add')")
    @Log(title = "电价配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsPriceConfig emsPriceConfig)
    {
        return toAjax(emsPriceConfigService.insertEmsPriceConfig(emsPriceConfig));
    }

    /**
     * 修改电价配置
     */
    @ApiOperation("修改电价配置")
    @PreAuthorize("@ss.hasPermi('energy:elecPriceConfig:edit')")
    @Log(title = "电价配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsPriceConfig emsPriceConfig)
    {
        return toAjax(emsPriceConfigService.updateEmsPriceConfig(emsPriceConfig));
    }

    /**
     * 删除电价配置
     */
    @ApiOperation("删除电价配置")
    @PreAuthorize("@ss.hasPermi('energy:elecPriceConfig:remove')")
    @Log(title = "电价配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        return toAjax(emsPriceConfigService.deleteEmsPriceConfigByConfigIds(configIds));
    }
}
