package com.ems.energy.controller;

import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.EmsDailyData;
import com.ems.energy.domain.EmsMonthly;
import com.ems.energy.service.IEmsDailyDataService;
import com.ems.energy.service.IEmsMonthlyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 月报Controller
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Api("月报")
@RestController
@RequestMapping("/system/monthly")
public class EmsMonthlyController extends BaseController
{
    @Autowired
    private IEmsMonthlyService emsMonthlyService;

    @Autowired
    private IEmsDailyDataService emsDailyDataService;

    /**
     * 查询月报列表
     */
    @ApiOperation("查询月报列表")
    @PreAuthorize("@ss.hasPermi('system:monthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsMonthly emsMonthly)
    {
        startPage();
        List<EmsMonthly> list = emsMonthlyService.selectEmsMonthlyList(emsMonthly);
        return getDataTable(list);
    }

    @ApiOperation("查询月报列表")
    @PreAuthorize("@ss.hasPermi('system:monthly:list')")
    @GetMapping("/pageList")
    public AjaxResult pageList(EmsDailyData emsDailyData)
    {

        return emsDailyDataService.selectEmsDailyDatapageList(emsDailyData);
    }

    /**
     * 导出月报列表
     */
    @ApiOperation("导出月报列表")
    @PreAuthorize("@ss.hasPermi('system:monthly:export')")
    @Log(title = "月报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsMonthly emsMonthly)
    {
        List<EmsMonthly> list = emsMonthlyService.selectEmsMonthlyList(emsMonthly);
        ExcelUtil<EmsMonthly> util = new ExcelUtil<EmsMonthly>(EmsMonthly.class);
        return util.exportExcel(list, "月报数据");
    }

    /**
     * 获取月报详细信息
     */
    @ApiOperation("获取月报详细信息")
    @PreAuthorize("@ss.hasPermi('system:monthly:query')")
    @GetMapping(value = "/{dailyId}")
    public AjaxResult getInfo(@PathVariable("dailyId") Long dailyId)
    {
        return AjaxResult.success(emsMonthlyService.selectEmsMonthlyByDailyId(dailyId));
    }

    /**
     * 新增月报
     */
    @ApiOperation("新增月报")
    @PreAuthorize("@ss.hasPermi('system:monthly:add')")
    @Log(title = "月报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsMonthly emsMonthly)
    {
        return toAjax(emsMonthlyService.insertEmsMonthly(emsMonthly));
    }

    /**
     * 修改月报
     */
    @ApiOperation("修改月报")
    @PreAuthorize("@ss.hasPermi('system:monthly:edit')")
    @Log(title = "月报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsMonthly emsMonthly)
    {
        return toAjax(emsMonthlyService.updateEmsMonthly(emsMonthly));
    }

    /**
     * 删除月报
     */
    @ApiOperation("删除月报")
    @PreAuthorize("@ss.hasPermi('system:monthly:remove')")
    @Log(title = "月报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dailyIds}")
    public AjaxResult remove(@PathVariable Long[] dailyIds)
    {
        return toAjax(emsMonthlyService.deleteEmsMonthlyByDailyIds(dailyIds));
    }
}
