package com.ems.energy.controller;

import com.ems.common.annotation.Log;
import com.ems.common.constant.UserConstants;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.EmsFactory;
import com.ems.energy.service.IEmsFactoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 企业架构Controller
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Api("企业架构管理")
@RestController
@RequestMapping("/energy/factory")
public class EmsFactoryController extends BaseController
{
    @Autowired
    private IEmsFactoryService emsFactoryService;

    /**
     * 查询企业架构列表
     */
    @ApiOperation("查询企业架构列表")
    @PreAuthorize("@ss.hasPermi('system:factory:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsFactory emsFactory)
    {
        startPage();
        List<EmsFactory> list = emsFactoryService.selectEmsFactoryList(emsFactory);
        return getDataTable(list);
    }

    /**
     * 导出企业架构列表
     */
    @ApiOperation("导出企业架构列表")
    @PreAuthorize("@ss.hasPermi('system:factory:export')")
    @Log(title = "企业架构", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsFactory emsFactory)
    {
        List<EmsFactory> list = emsFactoryService.selectEmsFactoryList(emsFactory);
        ExcelUtil<EmsFactory> util = new ExcelUtil<EmsFactory>(EmsFactory.class);
        return util.exportExcel(list, "企业架构数据");
    }

    /**
     * 获取企业架构详细信息
     */
    @ApiOperation("获取企业架构详细信息")
    @PreAuthorize("@ss.hasPermi('system:factory:query')")
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable("deptId") Long deptId)
    {
        return AjaxResult.success(emsFactoryService.selectEmsFactoryByDeptId(deptId));
    }

    /**
     * 企业架构添加
     */
    @ApiOperation("新增企业架构")
    @PreAuthorize("@ss.hasPermi('system:factory:add')")
    @Log(title = "企业管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody EmsFactory emsFactory)
    {
        if (UserConstants.NOT_UNIQUE.equals(emsFactoryService.checkDeptNameUnique(emsFactory)))
        {
            return AjaxResult.error("新增部门'" + emsFactory.getDeptName() + "'失败，部门名称已存在");
        }
        emsFactory.setCreateBy(getUsername());
        return toAjax(emsFactoryService.insertFactory(emsFactory));
    }

    /**
     * 修改企业架构
     */
    @ApiOperation("修改企业架构")
    @PreAuthorize("@ss.hasPermi('system:factory:edit')")
    @Log(title = "企业架构", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsFactory emsFactory)
    {
        return toAjax(emsFactoryService.updateEmsFactory(emsFactory));
    }

    /**
     * 删除企业架构
     */
    @ApiOperation("删除企业架构")
    @PreAuthorize("@ss.hasPermi('system:factory:remove')")
    @Log(title = "企业架构", businessType = BusinessType.DELETE)
	@DeleteMapping("/{deptIds}")
    public AjaxResult remove(@PathVariable Long[] deptIds)
    {
        return toAjax(emsFactoryService.deleteEmsFactoryByDeptIds(deptIds));
    }


    /**
     * 企业架构树
     */

    @PreAuthorize("@ss.hasPermi('system:factory:query')")
    @GetMapping("/treeselect")
    public AjaxResult treeselect(EmsFactory emsFactory)
    {
        List<EmsFactory> emsFactories = emsFactoryService.selectEmsFactoryList(emsFactory);
        return AjaxResult.success(emsFactoryService.buildFactoryselect(emsFactories));
    }
}
