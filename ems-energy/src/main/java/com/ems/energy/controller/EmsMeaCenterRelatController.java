package com.ems.energy.controller;

import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.EmsMeaCenterRelat;
import com.ems.energy.domain.EmsMeaCenterRelatListDTO;
import com.ems.energy.domain.vo.EmsMeaCenterRelatVo;
import com.ems.energy.service.IEmsMeaCenterRelatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 计量器具和成本中心关系Controller
 *
 * <AUTHOR>
 * @date 2021-12-14
 */
@Api("计量器具和成本中心关系管理")
@RestController
@RequestMapping("/energy/relat")
public class EmsMeaCenterRelatController extends BaseController
{
    @Autowired
    private IEmsMeaCenterRelatService emsMeaCenterRelatService;

    /**
     * 查询计量器具和成本中心关系列表
     */
    @ApiOperation("查询计量器具和成本中心关系列表")
    @PreAuthorize("@ss.hasPermi('system:relat:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsMeaCenterRelat emsMeaCenterRelat)
    {
        startPage();
        List<EmsMeaCenterRelat> list = emsMeaCenterRelatService.selectEmsMeaCenterRelatList(emsMeaCenterRelat);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('enterprise:management:list')")
    @GetMapping("/pagelist")
    public TableDataInfo pagelist(EmsMeaCenterRelatListDTO emsMeaCenterRelat)
    {
        startPage();
        List<EmsMeaCenterRelatVo> list = emsMeaCenterRelatService.selectEmsMeaCenterRelatpagelist(emsMeaCenterRelat);
        return getDataTable(list);
    }

    /**
     * 导出计量器具和成本中心关系列表
     */
    @ApiOperation("导出计量器具和成本中心关系列表")
    @PreAuthorize("@ss.hasPermi('system:relat:export')")
    @Log(title = "计量器具和成本中心关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsMeaCenterRelat emsMeaCenterRelat)
    {
        List<EmsMeaCenterRelat> list = emsMeaCenterRelatService.selectEmsMeaCenterRelatList(emsMeaCenterRelat);
        ExcelUtil<EmsMeaCenterRelat> util = new ExcelUtil<EmsMeaCenterRelat>(EmsMeaCenterRelat.class);
        return util.exportExcel(list, "计量器具和成本中心关系数据");
    }

    /**
     * 获取计量器具和成本中心关系详细信息
     */
    @ApiOperation("获取计量器具和成本中心关系详细信息")
    @PreAuthorize("@ss.hasPermi('system:relat:query')")
    @GetMapping(value = "/{relatId}")
    public AjaxResult getInfo(@PathVariable("relatId") Long relatId)
    {
        return AjaxResult.success(emsMeaCenterRelatService.selectEmsMeaCenterRelatByRelatId(relatId));
    }
    /**
     * 新增计量器具和成本中心关系
     */
    @ApiOperation("新增计量器具和成本中心关系")
    @PreAuthorize("@ss.hasPermi('system:relat:add')")
    @Log(title = "计量器具和成本中心关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsMeaCenterRelatListDTO emsMeaCenterRelat)
    {
        return toAjax(emsMeaCenterRelatService.insertEmsMeaCenterRelat(emsMeaCenterRelat));
    }

    /**
     * 修改计量器具和成本中心关系
     */
    @ApiOperation("修改计量器具和成本中心关系")
    @PreAuthorize("@ss.hasPermi('system:relat:edit')")
    @Log(title = "计量器具和成本中心关系", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult edit(@RequestBody EmsMeaCenterRelatListDTO emsMeaCenterRelat)
    {
        return toAjax(emsMeaCenterRelatService.updateEmsMeaCenterRelat(emsMeaCenterRelat));
    }

    /**
     * 删除计量器具和成本中心关系
     */
    @ApiOperation("删除计量器具和成本中心关系")
    @PreAuthorize("@ss.hasPermi('system:relat:remove')")
    @Log(title = "计量器具和成本中心关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{relatIds}")
    public AjaxResult remove(@PathVariable Long[] relatIds)
    {
        return toAjax(emsMeaCenterRelatService.deleteEmsMeaCenterRelatByRelatIds(relatIds));
    }
}
