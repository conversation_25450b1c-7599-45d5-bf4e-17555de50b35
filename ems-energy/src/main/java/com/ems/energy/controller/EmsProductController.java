package com.ems.energy.controller;

import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.EmsProduct;
import com.ems.energy.service.IEmsProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 产品Controller
 *
 * <AUTHOR>
 * @date 2022-01-18
 */
@Api("产品")
@RestController
@RequestMapping("/energy/product")
public class EmsProductController extends BaseController
{
    @Autowired
    private IEmsProductService emsProductService;

    /**
     * 查询产品列表
     */
    @ApiOperation("查询产品列表")
    @PreAuthorize("@ss.hasPermi('system:product:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsProduct emsProduct)
    {
        startPage();
        List<EmsProduct> list = emsProductService.selectEmsProductList(emsProduct);
        return getDataTable(list);
    }

    /**
     * 导出产品列表
     */
    @ApiOperation("导出产品列表")
    @PreAuthorize("@ss.hasPermi('system:product:export')")
    @Log(title = "产品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsProduct emsProduct)
    {
        List<EmsProduct> list = emsProductService.selectEmsProductList(emsProduct);
        ExcelUtil<EmsProduct> util = new ExcelUtil<EmsProduct>(EmsProduct.class);
        return util.exportExcel(list, "产品数据");
    }

    /**
     * 获取产品详细信息
     */
    @ApiOperation("获取产品详细信息")
    @PreAuthorize("@ss.hasPermi('system:product:query')")
    @GetMapping(value = "/{proId}")
    public AjaxResult getInfo(@PathVariable("proId") Long proId)
    {
        return AjaxResult.success(emsProductService.selectEmsProductByProId(proId));
    }

    /**
     * 新增产品
     */
    @ApiOperation("新增产品")
    @PreAuthorize("@ss.hasPermi('system:product:add')")
    @Log(title = "产品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsProduct emsProduct)
    {
        return toAjax(emsProductService.insertEmsProduct(emsProduct));
    }

    /**
     * 修改产品
     */
    @ApiOperation("修改产品")
    @PreAuthorize("@ss.hasPermi('system:product:edit')")
    @Log(title = "产品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsProduct emsProduct)
    {
        return toAjax(emsProductService.updateEmsProduct(emsProduct));
    }

    /**
     * 删除产品
     */
    @ApiOperation("删除产品")
    @PreAuthorize("@ss.hasPermi('system:product:remove')")
    @Log(title = "产品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{proIds}")
    public AjaxResult remove(@PathVariable Long[] proIds)
    {
        return toAjax(emsProductService.deleteEmsProductByProIds(proIds));
    }
}
