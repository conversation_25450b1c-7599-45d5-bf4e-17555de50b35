package com.ems.energy.controller;

import com.ems.common.core.domain.AjaxResult;
import com.ems.common.utils.DateUtils;
import com.ems.energy.domain.EmsEquipment;
import com.ems.energy.domain.vo.PartEnergyVo;
import com.ems.energy.service.IReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Api("设备管理")
@RestController
@RequestMapping("/energy/report")
public class ReportController {
    @Resource
    private IReportService reportService;

    @ApiOperation("查询能耗Top")
    @GetMapping("/queryTopEnergyByTag")
    public AjaxResult queryTopEnergyByTag(String tag, String time)
    {
        List<List<Object>> list = reportService.queryTopEnergyCostByTag(tag, DateUtils.parseDate(time));
        return AjaxResult.success(list);
    }

    @ApiOperation("查询设备能耗")
    @GetMapping("/energyReportWithCategoryByEquipment")
    public AjaxResult energyReportWithCategoryByEquipment(Long equipmentId, String start, String end)
    {
        List<List<Object>> list = reportService.energyReportWithCategoryByEquipment(equipmentId, DateUtils.parseDate(start), DateUtils.parseDate(end));
        return AjaxResult.success(list);
    }

    @ApiOperation("查询工厂总能耗")
    @GetMapping("/factoryEnergyReportWithCategory")
    public AjaxResult factoryEnergyReportWithCategory(String start, String end)
    {

        List<List<Object>> list = reportService.factoryEnergyReportWithCategory(DateUtils.parseDate(start), DateUtils.parseDate(end));
        return AjaxResult.success(list);
    }

    @ApiOperation("查询能耗日历")
    @GetMapping("/calendarEnergyReport")
    public AjaxResult calendarEnergyReport(String month)
    {
        List<List<Object>> list = reportService.queryFactoryMonthDayEnergy(month);
        return AjaxResult.success(list);
    }

    @ApiOperation("查询电费日历")
    @GetMapping("/calendarElecAmountReport")
    public AjaxResult calendarElecAmountReport(String month)
    {
        List<List<Object>> list = reportService.queryFactoryMonthDayElecAmount(month);
        return AjaxResult.success(list);
    }

    @ApiOperation("查询碳排放日历")
    @GetMapping("/calendarCarbonReport")
    public AjaxResult calendarCarbonReport(String month)
    {
        List<List<Object>> list = reportService.queryFactoryMonthDayCarbon(month);
        return AjaxResult.success(list);
    }

    @ApiOperation("查询能耗分月报表")
    @GetMapping("/monthEnergyReport")
    public AjaxResult monthEnergyReport(String year)
    {
        List<List<Map<String, Object>>> list = reportService.queryMonthEnergyReport(year);
        return AjaxResult.success(list);
    }

    @ApiOperation("查询用电分区饼图")
    @GetMapping("/oneMonthEnergyCateogryReport")
    public AjaxResult oneMonthEnergyCateogryReport(String month)
    {
        List<List<Object>> list = reportService.queryOneMonthCategoryEnergyReport(month);
        return AjaxResult.success(list);
    }

    @ApiOperation("查询碳排放分区饼图")
    @GetMapping("/oneMonthCarbonCateogryReport")
    public AjaxResult oneMonthCarbonCateogryReport(String month)
    {
        List<List<Object>> list = reportService.queryOneMonthCategoryCarbonReport(month);
        return AjaxResult.success(list);
    }

    @ApiOperation("查询电费分区饼图")
    @GetMapping("/oneMonthElecAmountCateogryReport")
    public AjaxResult oneMonthElecAmountCateogryReport(String month)
    {
        List<List<Object>> list = reportService.queryOneMonthCategoryElecAmountReport(month);
        return AjaxResult.success(list);
    }

    @ApiOperation("查询月总用电")
    @GetMapping("/queryOneMonthTotalEnergy")
    public AjaxResult queryOneMonthTotalEnergy(String month)
    {
        BigDecimal data = reportService.queryOneMonthTotalEnergy(month);
        return AjaxResult.success(data);
    }

    @ApiOperation("查询月总碳排放")
    @GetMapping("/queryOneMonthTotalCarbon")
    public AjaxResult queryOneMonthTotalCarbon(String month)
    {
        BigDecimal data = reportService.queryOneMonthTotalCarbon(month);
        return AjaxResult.success(data);
    }

    @ApiOperation("查询月总电费")
    @GetMapping("/queryOneMonthTotalElecAmount")
    public AjaxResult queryOneMonthTotalElecAmount(String month)
    {
        BigDecimal data = reportService.queryOneMonthTotalElecAmount(month);
        return AjaxResult.success(data);
    }

    @ApiOperation("查询工厂MD")
    @GetMapping("/queryFactoryMd")
    public AjaxResult queryFactoryMd(String start, String end)
    {
        List<List<Object>> data = reportService.queryFactoryMd(start, end);
        return AjaxResult.success(data);
    }

    @ApiOperation("查询PCF报表")
    @GetMapping("/queryPcfReport")
    public AjaxResult queryPcfReport(Long partId, String start, String end)
    {
        List<PartEnergyVo> data = reportService.queryPcfReport(partId, DateUtils.parseDate(start), DateUtils.parseDate(end));
        return AjaxResult.success(data);
    }

    @ApiOperation("产品当量能耗对比")
    @PostMapping("/queryPartsEquivalentCarbon")
    public AjaxResult queryPartsEquivalentCarbon(@RequestBody Map<String, Object> param)
    {
        List<List<Object>> data = reportService.queryPartsEquivalentCarbon((List<Integer>)param.get("partIds"), DateUtils.parseDate(param.get("start")), DateUtils.parseDate(param.get("end")));
        return AjaxResult.success(data);
    }

    @ApiOperation("查询设备碳排放运行监测")
    @GetMapping("/queryEquipmentCarbonDayReport")
    public AjaxResult queryEquipmentCarbonDayReport(Long equipmentId, String day)
    {
        Map<String, Object> data = reportService.queryEquipmentCarbonDayReport(equipmentId, day);
        return AjaxResult.success(data);
    }

    @ApiOperation("设备当量碳排放对比")
    @PostMapping("/queryEquipmentsEquivalentCarbon")
    public AjaxResult queryEquipmentsEquivalentCarbon(@RequestBody Map<String, Object> param)
    {
        List<List<Object>> data = reportService.queryEquipmentsEquivalentCarbonReport((List<Integer>)param.get("equipmentIds"), DateUtils.parseDate(param.get("start")), DateUtils.parseDate(param.get("end")));
        return AjaxResult.success(data);
    }

    @ApiOperation("设备有效利用率")
    @PostMapping("/queryEquipmentsOeeReport")
    public AjaxResult queryEquipmentsOeeReport(@RequestBody Map<String, Object> param)
    {
        List<List<Object>> data = reportService.queryEquipmentsOeeReport((List<Integer>)param.get("equipmentIds"), DateUtils.parseDate(param.get("start")), DateUtils.parseDate(param.get("end")));
        return AjaxResult.success(data);
    }

    @ApiOperation("查询设备状态用电量分析")
    @GetMapping("/queryEquipmentStatusEnergyReport")
    public AjaxResult queryEquipmentStatusEnergyReport(Long equipmentId, String start, String end)
    {
        List<List<Object>> data = reportService.queryEquipmentStatusEnergyReport(equipmentId, DateUtils.parseDate(start), DateUtils.parseDate(end));
        return AjaxResult.success(data);
    }
}
