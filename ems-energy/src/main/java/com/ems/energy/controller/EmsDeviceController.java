package com.ems.energy.controller;


import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.EmsDevice;
import com.ems.energy.domain.vo.EmsDeviceVo;
import com.ems.energy.service.IEmsDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工厂设备Controller
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Api("工厂设备")
@RestController
@RequestMapping("/energy/device")
public class EmsDeviceController extends BaseController
{
    @Autowired
    private IEmsDeviceService emsDeviceService;

    /**
     * 查询工厂设备列表
     */
    @ApiOperation("查询工厂设备列表")
    @PreAuthorize("@ss.hasPermi('system:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsDeviceVo emsDevice)
    {
        startPage();
        List<EmsDeviceVo> list = emsDeviceService.selectEmsDeviceList(emsDevice);
        return getDataTable(list);
    }

    /**
     * 导出工厂设备列表
     */
    @ApiOperation("导出工厂设备列表")
    @PreAuthorize("@ss.hasPermi('system:device:export')")
    @Log(title = "工厂设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsDeviceVo emsDevice)
    {
        List<EmsDeviceVo> list = emsDeviceService.selectEmsDeviceList(emsDevice);
        ExcelUtil<EmsDeviceVo> util = new ExcelUtil<>(EmsDeviceVo.class);
        return util.exportExcel(list, "工厂设备数据");
    }

    /**
     * 获取工厂设备详细信息
     */
    @ApiOperation("获取工厂设备详细信息")
    @PreAuthorize("@ss.hasPermi('system:device:query')")
    @GetMapping(value = "/{equId}")
    public AjaxResult getInfo(@PathVariable("equId") Long equId)
    {
        return AjaxResult.success(emsDeviceService.selectEmsDeviceByEquId(equId));
    }

    /**
     * 新增工厂设备
     */
    @ApiOperation("新增工厂设备")
    @PreAuthorize("@ss.hasPermi('system:device:add')")
    @Log(title = "工厂设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsDevice emsDevice)
    {
        return toAjax(emsDeviceService.insertEmsDevice(emsDevice));
    }

    /**
     * 修改工厂设备
     */
    @ApiOperation("修改工厂设备")
    @PreAuthorize("@ss.hasPermi('system:device:edit')")
    @Log(title = "工厂设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsDevice emsDevice)
    {
        return toAjax(emsDeviceService.updateEmsDevice(emsDevice));
    }

    /**
     * 删除工厂设备
     */
    @ApiOperation("删除工厂设备")
    @PreAuthorize("@ss.hasPermi('system:device:remove')")
    @Log(title = "工厂设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{equIds}")
    public AjaxResult remove(@PathVariable Long[] equIds)
    {
        return toAjax(emsDeviceService.deleteEmsDeviceByEquIds(equIds));
    }
}
