package com.ems.energy.controller;

import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.EmsDailyData;
import com.ems.energy.service.IEmsDailyDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 日报Controller
 * <AUTHOR>
 * @date 2022-01-04
 */
@Api("日报")
@RestController
@RequestMapping("/system/data")
public class EmsDailyDataController extends BaseController
{
    @Autowired
    private IEmsDailyDataService emsDailyDataService;

    /**
     * 查询日报列表
     */
    @ApiOperation("查询日报列表")
    @PreAuthorize("@ss.hasPermi('system:data:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsDailyData emsDailyData)
    {
        startPage();
        List<EmsDailyData> list = emsDailyDataService.selectEmsDailyDataList(emsDailyData);
        return getDataTable(list);
    }

    /**
     * 导出日报列表
     */
    @ApiOperation("导出日报列表")
    @PreAuthorize("@ss.hasPermi('system:data:export')")
    @Log(title = "日报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsDailyData emsDailyData)
    {
        List<EmsDailyData> list = emsDailyDataService.selectEmsDailyDataList(emsDailyData);
        ExcelUtil<EmsDailyData> util = new ExcelUtil<EmsDailyData>(EmsDailyData.class);
        return util.exportExcel(list, "日报数据");
    }

    /**
     * 获取日报详细信息
     */
    @ApiOperation("获取日报详细信息")
    @PreAuthorize("@ss.hasPermi('system:data:query')")
    @GetMapping(value = "/{dailyId}")
    public AjaxResult getInfo(@PathVariable("dailyId") Long dailyId)
    {
        return AjaxResult.success(emsDailyDataService.selectEmsDailyDataByDailyId(dailyId));
    }

    /**
     * 新增日报
     */
    @ApiOperation("新增日报")
    @PreAuthorize("@ss.hasPermi('system:data:add')")
    @Log(title = "日报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsDailyData emsDailyData)
    {
        return toAjax(emsDailyDataService.insertEmsDailyData(emsDailyData));
    }

    /**
     * 修改日报
     */
    @ApiOperation("修改日报")
    @PreAuthorize("@ss.hasPermi('system:data:edit')")
    @Log(title = "日报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsDailyData emsDailyData)
    {
        return toAjax(emsDailyDataService.updateEmsDailyData(emsDailyData));
    }

    /**
     * 删除日报
     */
    @ApiOperation("删除日报")
    @PreAuthorize("@ss.hasPermi('system:data:remove')")
    @Log(title = "日报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dailyIds}")
    public AjaxResult remove(@PathVariable Long[] dailyIds)
    {
        return toAjax(emsDailyDataService.deleteEmsDailyDataByDailyIds(dailyIds));
    }
}
