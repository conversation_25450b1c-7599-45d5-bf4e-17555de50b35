package com.ems.energy.controller;

import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.EmsCostMea;
import com.ems.energy.service.IEmsCostMeaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 成本中心和计量器具中间Controller
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Api("成本中心和计量器具中间")
@RestController
@RequestMapping("/system/mea")
public class EmsCostMeaController extends BaseController
{
    @Autowired
    private IEmsCostMeaService emsCostMeaService;

    /**
     * 查询成本中心和计量器具中间列表
     */
    @ApiOperation("查询成本中心和计量器具中间列表")
    @PreAuthorize("@ss.hasPermi('system:mea:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmsCostMea emsCostMea)
    {
        startPage();
        List<EmsCostMea> list = emsCostMeaService.selectEmsCostMeaList(emsCostMea);
        return getDataTable(list);
    }

    /**
     * 导出成本中心和计量器具中间列表
     */
    @ApiOperation("导出成本中心和计量器具中间列表")
    @PreAuthorize("@ss.hasPermi('system:mea:export')")
    @Log(title = "成本中心和计量器具中间", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsCostMea emsCostMea)
    {
        List<EmsCostMea> list = emsCostMeaService.selectEmsCostMeaList(emsCostMea);
        ExcelUtil<EmsCostMea> util = new ExcelUtil<EmsCostMea>(EmsCostMea.class);
        return util.exportExcel(list, "成本中心和计量器具中间数据");
    }
    /**
     * 获取成本中心和计量器具中间详细信息
     */
    @ApiOperation("获取成本中心和计量器具中间详细信息")
    @PreAuthorize("@ss.hasPermi('system:mea:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(emsCostMeaService.selectEmsCostMeaById(id));
    }

    /**
     * 新增成本中心和计量器具中间
     */
    @ApiOperation("新增成本中心和计量器具中间")
    @PreAuthorize("@ss.hasPermi('system:mea:add')")
    @Log(title = "成本中心和计量器具中间", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsCostMea emsCostMea)
    {
        return toAjax(emsCostMeaService.insertEmsCostMea(emsCostMea));
    }

    /**
     * 修改成本中心和计量器具中间
     */
    @ApiOperation("修改成本中心和计量器具中间")
    @PreAuthorize("@ss.hasPermi('system:mea:edit')")
    @Log(title = "成本中心和计量器具中间", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsCostMea emsCostMea)
    {
        return toAjax(emsCostMeaService.updateEmsCostMea(emsCostMea));
    }
    /**
     * 删除成本中心和计量器具中间
     */
    @ApiOperation("删除成本中心和计量器具中间")
    @PreAuthorize("@ss.hasPermi('system:mea:remove')")
    @Log(title = "成本中心和计量器具中间", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(emsCostMeaService.deleteEmsCostMeaByIds(ids));
    }
}
