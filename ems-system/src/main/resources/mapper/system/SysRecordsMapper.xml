<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.system.mapper.SysRecordsMapper">
    
    <resultMap type="SysRecords" id="SysRecordsResult">
        <result property="recordsId"    column="records_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="recordNumber"    column="record_number"    />
        <result property="updateBy"    column="update_by"    />
        <result property="configuration"    column="configuration"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysRecordsVo">
        select records_id, dept_id, user_id, record_number, update_by, configuration, status, del_flag, create_time, update_time, remark from sys_records
    </sql>

    <select id="selectSysRecordsList" parameterType="SysRecords" resultMap="SysRecordsResult">
<!--        <include refid="selectSysRecordsVo"/>-->
        SELECT
        sus.nick_name,
        sde.dept_name,
        rec.record_number,
        rec.update_by,
        rec.configuration,
        rec.create_time,
        rec.update_time,
        rec.remark
        FROM
        sys_records rec
        LEFT JOIN sys_user sus ON rec.user_id = sus.user_id
        LEFT JOIN sys_dept sde ON rec.dept_id = sde.dept_id
        <where>
        <if test="rec.del_flag != null "> and rec.del_flag = 0</if>

        </where>
    </select>
    <!--            <if test="deptId != null "> and dept_id = #{deptId}</if>-->
    <!--            <if test="userId != null "> and user_id = #{userId}</if>-->
    <!--            <if test="recordNumber != null  and recordNumber != ''"> and record_number = #{recordNumber}</if>-->
    <!--            <if test="configuration != null  and configuration != ''"> and configuration = #{configuration}</if>-->
    <!--            <if test="status != null  and status != ''"> and status = #{status}</if>-->
    
    <select id="selectSysRecordsByRecordsId" parameterType="Long" resultMap="SysRecordsResult">
        <include refid="selectSysRecordsVo"/>
        where deptId = #{deptId}
    </select>
        
    <insert id="insertSysRecords" parameterType="SysRecords" useGeneratedKeys="true" keyProperty="recordsId">
        insert into sys_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="recordNumber != null">record_number,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="configuration != null">configuration,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="recordNumber != null">#{recordNumber},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="configuration != null">#{configuration},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysRecords" parameterType="SysRecords">
        update sys_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="recordNumber != null">record_number = #{recordNumber},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="configuration != null">configuration = #{configuration},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where records_id = #{recordsId}
    </update>

    <update id="deleteSysRecordsByRecordsId" parameterType="Long">
        update sys_records  SET del_flag=2 where records_id = #{recordsId}
    </update>

    <update id="deleteSysRecordsByRecordsIds" parameterType="String">
        update sys_records SET del_flag=2 where records_id in
        <foreach item="recordsId" collection="array" open="(" separator="," close=")">
            #{recordsId}
        </foreach>
    </update>
</mapper>