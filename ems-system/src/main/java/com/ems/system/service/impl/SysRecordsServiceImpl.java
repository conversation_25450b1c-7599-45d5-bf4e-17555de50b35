package com.ems.system.service.impl;

import com.ems.common.utils.DateUtils;
import com.ems.system.domain.SysRecords;
import com.ems.system.mapper.SysRecordsMapper;
import com.ems.system.service.ISysRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工厂记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-03
 */
@Service
public class SysRecordsServiceImpl implements ISysRecordsService
{
    @Autowired
    private SysRecordsMapper sysRecordsMapper;

    /**
     * 查询工厂记录
     *
     * @param deptId 工厂记录主键
     * @return 工厂记录
     */
    @Override
    public SysRecords selectSysRecordsByRecordsId(Long deptId)
    {
        return sysRecordsMapper.selectSysRecordsByRecordsId(deptId);
    }

    /**
     * 查询工厂记录列表
     *
     * @param sysRecords 工厂记录
     * @return 工厂记录
     */
    @Override
    public List<SysRecords> selectSysRecordsList(SysRecords sysRecords)
    {
        return sysRecordsMapper.selectSysRecordsList(sysRecords);
    }

    /**
     * 新增工厂记录
     *
     * @param sysRecords 工厂记录
     * @return 结果
     */
    @Override
    public int insertSysRecords(SysRecords sysRecords)
    {
        sysRecords.setCreateTime(DateUtils.getNowDate());
        return sysRecordsMapper.insertSysRecords(sysRecords);
    }

    /**
     * 修改工厂记录
     *
     * @param sysRecords 工厂记录
     * @return 结果
     */
    @Override
    public int updateSysRecords(SysRecords sysRecords)
    {
        sysRecords.setUpdateTime(DateUtils.getNowDate());
        return sysRecordsMapper.updateSysRecords(sysRecords);
    }

    /**
     * 批量删除工厂记录
     *
     * @param recordsIds 需要删除的工厂记录主键
     * @return 结果
     */
    @Override
    public int deleteSysRecordsByRecordsIds(Long[] recordsIds)
    {
        return sysRecordsMapper.deleteSysRecordsByRecordsIds(recordsIds);
    }

    /**
     * 删除工厂记录信息
     *
     * @param recordsId 工厂记录主键
     * @return 结果
     */
    @Override
    public int deleteSysRecordsByRecordsId(Long recordsId)
    {
        return sysRecordsMapper.deleteSysRecordsByRecordsId(recordsId);
    }
}
