package com.ems.system.mapper;

import com.ems.system.domain.SysRecords;

import java.util.List;

/**
 * 工厂记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-12-03
 */
public interface SysRecordsMapper 
{
    /**
     * 查询工厂记录
     * 
     * @param deptId 工厂记录主键
     * @return 工厂记录
     */
    public SysRecords selectSysRecordsByRecordsId(Long deptId);

    /**
     * 查询工厂记录列表
     * 
     * @param sysRecords 工厂记录
     * @return 工厂记录集合
     */
    public List<SysRecords> selectSysRecordsList(SysRecords sysRecords);

    /**
     * 新增工厂记录
     * 
     * @param sysRecords 工厂记录
     * @return 结果
     */
    public int insertSysRecords(SysRecords sysRecords);

    /**
     * 修改工厂记录
     * 
     * @param sysRecords 工厂记录
     * @return 结果
     */
    public int updateSysRecords(SysRecords sysRecords);

    /**
     * 删除工厂记录
     * 
     * @param recordsId 工厂记录主键
     * @return 结果
     */
    public int deleteSysRecordsByRecordsId(Long recordsId);

    /**
     * 批量删除工厂记录
     * 
     * @param recordsIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysRecordsByRecordsIds(Long[] recordsIds);

}
