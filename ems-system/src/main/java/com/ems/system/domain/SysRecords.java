package com.ems.system.domain;

import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 工厂记录对象 sys_records
 *
 * <AUTHOR>
 * @date 2021-12-03
 */
public class SysRecords extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID编号 */
    private Long recordsId;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 维修编号 */
    @Excel(name = "维修编号")
    private String recordNumber;

    /** 配置规则 */
    @Excel(name = "配置规则")
    private String configuration;

    /** 设备状态（0正常 1停用） */
    @Excel(name = "设备状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 用户昵称 */
    @Excel(name = "用户名称")
    private String nickName;


    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public SysRecords(String nickName) {
        this.nickName = nickName;
    }

    public void setRecordsId(Long recordsId)
    {
        this.recordsId = recordsId;
    }

    public Long getRecordsId()
    {
        return recordsId;
    }
    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setRecordNumber(String recordNumber)
    {
        this.recordNumber = recordNumber;
    }

    public String getRecordNumber()
    {
        return recordNumber;
    }
    public void setConfiguration(String configuration)
    {
        this.configuration = configuration;
    }

    public String getConfiguration()
    {
        return configuration;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordsId", getRecordsId())
            .append("deptId", getDeptId())
            .append("userId", getUserId())
            .append("recordNumber", getRecordNumber())
            .append("updateBy", getUpdateBy())
            .append("configuration", getConfiguration())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
