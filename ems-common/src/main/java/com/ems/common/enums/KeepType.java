package com.ems.common.enums;

/**
 * @className: KeepType
 * @description: 维护类别
 * @author: RK
 * @date: 2022/1/5 14:41
 **/
public enum KeepType {
    /**
     * 成本中心
     */
    COST_CENTER(1L,"成本中心"),
    /**
     * 自定义
     */
    CUSTOM(2L,"自定义");
    private final Long status;
    private final String name;

    public Long getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    KeepType(Long status, String name) {
        this.status = status;
        this.name = name;
    }
}
