package com.ems.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @className: EmsLog
 * @description: 方法执行日志
 * @author: RK
 * @date: 2021/12/22 9:30
 **/
@Target({ ElementType.PARAMETER, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface EmsLog {
    /**
     * 模块
     */
    public String title() default "";

}
