package com.ems.common.constant;

/**
 * @className: ErrorConstant
 * @description:
 * @author: RK
 * @date: 2021/12/29 9:28
 **/
public class ErrorConstant {
    public static final String RELAT_IS_NULL = "维护关系不能为空,请重新添加";

    public static final String FORMULA_IS_INVALID = "关系算式不合法,请重新输入";

    public static final String BUSINESS_IS_INVALID = "企业不存在";

    public static final String COST_CENTER_NOT_EXIST = "成本中心不存在";

    public static final String MEASURE_NOT_EXIST = "计量器具不存在";

    public static final String TOP_COST_CENTER_IS_UNIQUE = "成本中心只能有一个最高级成本中心!";

    public static final String COST_CENTER_NOT_USE = "成本中心停用，不允许新增";

    public static final String KEY_IS_NOT_FOUND = "缓存中没有配置该key";

    public static final String LOW_LEVEL_DEPT_IS_EXIST = "存在下级部门,不允许删除";
}
