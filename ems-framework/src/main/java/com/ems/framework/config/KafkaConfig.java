package com.ems.framework.config;

//import org.apache.kafka.clients.admin.AdminClient;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
//import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.kafka.core.KafkaAdmin;

/**
 * @className: KafkaConfig
 * @description: kafka配置
 * @author: RK
 * @date: 2021/12/24 12:42
 **/
//@Configuration
//@ConditionalOnClass(KafkaAdmin.class)
//@EnableConfigurationProperties(KafkaProperties.class)
public class KafkaConfig {
//    private final KafkaProperties properties;
//
//
//    public KafkaConfig(KafkaProperties properties) {
//        this.properties = properties;
//    }
//
//    /**
//     * 初始化对kafka执行操作的对象
//     * @return
//     */
////    @Bean
//    public KafkaAdmin kafkaAdmin() {
//        return null;
////        return new KafkaAdmin(this.properties.buildProducerProperties());
//    }
//
//    /**
//     * 初始化操作连接
//     * @return
//     */
////    @Bean
//    public AdminClient adminClient() {
//        return null;
////        return AdminClient.create(kafkaAdmin().getConfigurationProperties());
//    }
}
