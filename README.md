### EMS能源管理系统
EMS能源管理系统是以若依为脚手架来开发的系统

### 启动注意事项
1.启动redis

2.修改配置文件`application.yml`中的数据源

3.刷新`pom`依赖,启动`ems-admin`
### 接口文档地址
项目启动后,访问[接口文档](http://localhost:8080/doc.html)

### 开发注意事项
1.`controller`的请求方法必须添加`swagger`注解,没有的请开发自行添加

2.访问数据库使用条件查询,在可使用的情况下,尽量使用`LambdaQueryWrapper`查询
```
示例:
LambdaQueryWrapper<EmsCostCenter> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(EmsCostCenter::getCenterId,"0");
```
3.创建`mapper`接口继承`BaseMapper`接口

4.查询树形结构的实体中`children`属性添加`@TableField(exist = false)` 注解

5.实体互换时,建议使用`mapstruct`或者`spring`自带的`BeanUtils`进行实体转换(效率较高)，不推荐使用`Apache`的工具类进行转换

6.表字段添加逻辑删除字段名为`del_flag`，全局统一配置

7.添加,删除,修改的方法添加` @Transactional `注解

8.日志示例
```
private final static Logger log = LoggerFactory.getLogger(EmsCostCenterController.class);
```

9.TODO格式
```java
//TODO x %yyyy%-%MM%-%dd%
```
