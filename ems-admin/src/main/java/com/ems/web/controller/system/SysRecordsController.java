package com.ems.web.controller.system;

import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.system.domain.SysRecords;
import com.ems.system.service.ISysRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 工厂记录Controller
 *
 * <AUTHOR>
 * @date 2021-12-03
 */
@RestController
@RequestMapping("/system/records")
public class SysRecordsController extends BaseController
{
    @Autowired
    private ISysRecordsService sysRecordsService;

    /**
     * 查询工厂记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:records:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysRecords sysRecords)
    {
        startPage();
        List<SysRecords> list = sysRecordsService.selectSysRecordsList(sysRecords);
        return getDataTable(list);
    }

    /**
     * 导出工厂记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:records:export')")
    @Log(title = "工厂记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(HttpServletResponse response, SysRecords sysRecords)
    {
        List<SysRecords> list = sysRecordsService.selectSysRecordsList(sysRecords);
        ExcelUtil<SysRecords> util = new ExcelUtil<SysRecords>(SysRecords.class);
        return util.exportExcel(list, "工厂记录数据");
    }

    /**
     * 获取工厂记录详细信息
     * 根据工厂部门联动获取单个工厂的记录
     */
    @PreAuthorize("@ss.hasPermi('system:records:query')")
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable("deptId") Long deptId)
    {
        return AjaxResult.success(sysRecordsService.selectSysRecordsByRecordsId(deptId));
    }

    /**
     * 新增工厂记录
     */
    @PreAuthorize("@ss.hasPermi('system:records:add')")
    @Log(title = "工厂记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysRecords sysRecords)
    {
        return toAjax(sysRecordsService.insertSysRecords(sysRecords));
    }

    /**
     * 修改工厂记录
     */
    @PreAuthorize("@ss.hasPermi('system:records:edit')")
    @Log(title = "工厂记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysRecords sysRecords)
    {
        return toAjax(sysRecordsService.updateSysRecords(sysRecords));
    }

    /**
     * 删除工厂记录
     */
    @PreAuthorize("@ss.hasPermi('system:records:remove')")
    @Log(title = "工厂记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{recordsIds}")
    public AjaxResult remove(@PathVariable Long[] recordsIds)
    {
        return toAjax(sysRecordsService.deleteSysRecordsByRecordsIds(recordsIds));
    }
}
