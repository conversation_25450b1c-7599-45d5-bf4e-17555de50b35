package com.ems.web.controller.system;

import com.ems.common.core.domain.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 空控制器，可用于测试全局访问功能，包括 过滤器 拦截器 等
 */
@RestController
public class SysEmptyController {
    /**
     * 检测 http 请求中的 token 是否有效
     */
    @GetMapping("/token-check")
    public AjaxResult checkToken() {
        return AjaxResult.success();
    }
}
