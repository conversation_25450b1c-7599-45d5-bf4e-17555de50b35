FROM redis:latest
LABEL maintainer="orangebox.ems"
WORKDIR /usr/local
RUN mkdir -p /usr/local/java
ADD jre.tar.gz /usr/local/java
COPY ems-admin/target/ems-admin.jar ./
COPY entrypoint.sh ./
EXPOSE 8080
# 设置容器内 JAVA_HOME 环境变量
ENV JAVA_HOME /usr/local/java/jre1.8.0_202
ENV PATH $PATH:$JAVA_HOME/bin
ENV REDIS_HOST 127.0.0.1
ENV REDIS_PORT 6379
ENV MYSQL_HOST 127.0.0.1
ENV MYSQL_PORT 3306
ENV MYSQL_USER ems_user
ENV MYSQL_PASS P@ss1234
ENV MYSQL_SCHEMA ems
ENV INFLUX_URL http://***********:8086
ENV INFLUX_TOKEN 3ZbGW-OXZV0AFFt_wIupGj9ZikRy2C6JcpJLMUVgdHUbNLyNLUU4f8-sQGwZInUlLRja03jzqpUKYRjEA8pOcg==
ENV INFLUX_ORG orange
ENV INFLUX_BUCKET DemoEMS
ENV NAME ems
ENTRYPOINT ["sh", "./entrypoint.sh"]